"use client";
import React, { useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import logo from "../../../public/assets/images/logo.svg";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import styles from "@/styles/auth.module.scss";
import { forgotPasswordValidation } from "@/validations/authValidations";
import { forgotPassword } from "@/services/authServices";
import { IForgotPassword } from "@/interfaces/authInterfaces";
import routes from "@/constants/routes";
import { toastMessageSuccess, toastMessageError } from "@/utils/helper";

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const t = useTranslations();
  const router = useRouter();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(forgotPasswordValidation(t)),
  });

  const onSubmit = async (data: IForgotPassword) => {
    try {
      setLoading(true);
      const encEmail = encodeURIComponent(data?.email);

      const result = await forgotPassword(data);

      if (result?.data?.success) {
        toastMessageSuccess(t(result?.data?.message as string));
        router.push(`${routes.VERIFY}?email=${encEmail}`);
        setLoading(false);
      } else {
        setLoading(false);
        toastMessageError(t((result?.data?.message as string) ?? "something_went_wrong"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row">
          <div className={styles.user_auth_main}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`}>
                  {/* <div className={styles.client_signature_box}>
                    <p>
                      The challenge is great, the effort is extraordinary, the achievement is life changing, and the impact will become your legacy.
                      Where are you now and what are you willing to change to get to where you want to be?
                    </p>
                    <Image src={clientSignature} alt="client" />
                  </div> */}
                </div>
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center">
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} />
                      <h1>
                        {t("forgot")}
                        <span> {t("password")}</span>
                      </h1>
                    </div>
                    <form onSubmit={handleSubmit(onSubmit)}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="email" required>
                          {t("email")}
                        </InputWrapper.Label>
                        <Textbox className="form-control" control={control} name="email" type="email" placeholder={t("enter_your_email")} />
                        <InputWrapper.Error message={errors?.email?.message || ""} />
                      </InputWrapper>
                      <Button disabled={loading} loading={loading} className="primary-btn rounded-md w-100 mt-5">
                        {t("send_verification_code")}
                      </Button>
                    </form>
                    <p className={styles.bottom_link}>
                      <Button disabled={loading} onClick={() => router.push(routes.LOGIN)} className="primary-btn rounded-md w-100">
                        {t("back_to_login")}
                      </Button>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
