import React from "react";

function CopyLinkIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="24" height="12" viewBox="0 0 24 12" fill="none">
      <path d="M3.02812 8.9664C2.20781 8.14609 1.8 7.15234 1.8 5.99453C1.8 4.88359 2.24531 3.81953 3.02812 3.03203C3.84844 2.21172 4.8375 1.80391 6 1.80391H10.2C10.6969 1.80391 11.1 1.40078 11.1 0.903906C11.1 0.407031 10.6969 0.00390625 10.2 0.00390625H6C4.34062 0.00390625 2.925 0.589843 1.75313 1.75703C0.58125 2.92422 0 4.33984 0 5.99922C0 7.65859 0.585938 9.07422 1.75313 10.2461C2.92031 11.418 4.34062 11.9992 6 11.9992H10.2C10.6969 11.9992 11.1 11.5961 11.1 11.0992C11.1 10.6023 10.6969 10.1992 10.2 10.1992H6.0375C4.90781 10.2039 3.825 9.75859 3.02812 8.9664Z" />
      <path d="M8.2501 5.09961H15.7501C16.247 5.09961 16.6501 5.50273 16.6501 5.99961C16.6501 6.49648 16.247 6.89961 15.7501 6.89961H8.2501C7.75322 6.89961 7.3501 6.49648 7.3501 5.99961C7.3501 5.50273 7.75322 5.09961 8.2501 5.09961Z" />
      <path d="M22.2468 1.75312C21.0749 0.58125 19.664 0 17.9999 0H13.7999C13.303 0 12.8999 0.403125 12.8999 0.9C12.8999 1.39687 13.303 1.8 13.7999 1.8H17.9624C19.0874 1.79531 20.1702 2.24062 20.9718 3.03281C21.7921 3.85312 22.1999 4.84687 22.1999 6.00469C22.1999 7.11562 21.7546 8.17969 20.9718 8.96719C20.1515 9.7875 19.1624 10.1953 17.9999 10.1953H13.7999C13.303 10.1953 12.8999 10.5984 12.8999 11.0953C12.8999 11.5922 13.303 11.9953 13.7999 11.9953H17.9999C19.6593 11.9953 21.0749 11.4094 22.2468 10.2422C23.4187 9.07031 23.9999 7.65937 23.9999 5.99531C23.9999 4.34062 23.414 2.925 22.2468 1.75312Z" />
    </svg>
  );
}

export default CopyLinkIcon;
