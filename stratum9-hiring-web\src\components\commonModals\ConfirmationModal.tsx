import React from "react";
import Button from "../formElements/Button";
import "../../styles/eventModal.scss";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose?: () => void;
  onConfirm?: () => void;
  title: string;
  message: string;
  confirmButtonText?: string;
  cancelButtonText?: string; // When not provided or empty, cancel button will be hidden
  children?: React.ReactNode;
  loading?: boolean;
  loadingText?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmButtonText = "Confirm",
  cancelButtonText, // No default value - when undefined/empty, cancel button won't show
  children,
  loading,
  loadingText = "Proceed...",
}) => {
  if (!isOpen) return null;

  return (
    <>
      <div className="modal theme-modal show-modal">
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header justify-content-center pb-3">
              <h2 className="m-0">{title}</h2>

              <Button className={`modal-close-btn ${loading ? "fade-close" : ""}`} onClick={onClose} disabled={loading}>
                <ModalCloseIcon />
              </Button>
            </div>
            <div className="modal-body pt-0">
              {/* qualification-card */}
              <p className="text-center pb-4 font16">
                {message}
                {children}
              </p>

              <div className="button-align">
                {cancelButtonText && cancelButtonText.trim() !== "" && (
                  <Button className={"dark-outline-btn rounded-md w-100"} onClick={onClose} disabled={loading}>
                    {cancelButtonText}
                  </Button>
                )}
                {confirmButtonText && (
                  <Button className={"primary-btn rounded-md w-100"} onClick={onConfirm} disabled={loading}>
                    {loading ? loadingText : confirmButtonText}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ConfirmationModal;
