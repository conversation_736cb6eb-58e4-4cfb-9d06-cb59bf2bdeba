"use client";
import React, { FC, ReactNode, useEffect } from "react";
import Button from "./Button";

interface ModalProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  size?: "sm" | "md" | "lg";
  children: ReactNode;
}

const Modal: FC<ModalProps> = ({ title, isOpen, onClose, size = "md", children }) => {
  // Add body class to prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("modal-open");
    } else {
      document.body.classList.remove("modal-open");
    }

    return () => {
      document.body.classList.remove("modal-open");
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const sizeClass =
    {
      sm: "modal-sm",
      md: "modal-md",
      lg: "modal-lg",
    }[size] || "modal-md";

  return (
    <>
      {/* Backdrop with click handler to close */}
      <div className="modal-backdrop" onClick={onClose}></div>

      <div className="modal theme-modal show-modal">
        <div className={`modal-dialog modal-dialog-centered ${sizeClass}`}>
          <div className="modal-content">
            <div className="modal-header pb-0">
              <h2 className="m-0"> {title}</h2>
              <Button className="modal-close-btn" onClick={onClose}>
                <span aria-hidden="true">&times;</span>
              </Button>
            </div>
            <div className="modal-body">{children}</div>
          </div>
        </div>
      </div>

      {/* Add modal styling */}
      <style jsx global>{`
        .modal-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          z-index: 1040;
          width: 100vw;
          height: 100vh;
          background-color: rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(2px);
        }

        .modal-open {
          overflow: hidden;
        }

        .theme-modal.show-modal {
          display: block;
          position: fixed;
          top: 0;
          left: 0;
          z-index: 1050;
          width: 100%;
          height: 100%;
          overflow-x: hidden;
          overflow-y: auto;
          outline: 0;
          padding: 1rem;
        }

        .modal-dialog {
          position: relative;
          margin: 1.75rem auto;
          max-width: 500px;
          animation: modalFadeIn 0.3s ease-out;
        }

        .modal-sm {
          max-width: 300px;
        }

        .modal-md {
          max-width: 500px;
        }

        .modal-lg {
          max-width: 800px;
        }

        .modal-content {
          position: relative;
          display: flex;
          flex-direction: column;
          width: 100%;
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1rem 1.5rem;
          border-bottom: 1px solid #eeeeee;
        }

        .modal-header h2 {
          margin: 0;
          font-size: 1.25rem;
          font-weight: 600;
        }

        .modal-close-btn {
          background: transparent;
          border: none;
          font-size: 1.5rem;
          line-height: 1;
          opacity: 0.7;
          transition: opacity 0.15s;
          top: 10px !important;
          right: 20px !important;
          border: 0;
          font-size: 24px;
        }

        .modal-close-btn:hover {
          opacity: 1;
        }

        .modal-body {
          padding: 1.5rem;
          overflow-y: auto;
          max-height: calc(100vh - 200px);
        }

        @keyframes modalFadeIn {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </>
  );
};

export default Modal;
