import endpoint from "@/constants/endpoint";
import { UserRoleForm } from "@/interfaces/employeeInterface";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import * as http from "@/utils/http";
import { IRolePermission } from "@/interfaces/roleInterface";

export interface FindRoleResponse {
  id: number;
  name: string;
  isDefaultRole: boolean;
}

export interface UpdateRolePermissionsResponse {
  id: number;
  name: string;
  isDefaultRole: number;
  permission_count: string;
  updated_ts: string;
}
interface IPermission {
  id: number;
  name: string;
  description: string;
  selected: boolean;
}
export interface RolePermissionResponse {
  role_id: number;
  role_name: string;
  permissions: IPermission[];
}

/**
 * Get all roles associated with an organization
 * @returns Promise with API response
 */
export const findRoleList = (offset: number, limit: number): Promise<IApiResponseCommonInterface<FindRoleResponse[]>> => {
  return http.get(endpoint.roles.GET_ROLES_WITH_PAGINATION, { offset, limit });
};

export const findRole = (): Promise<IApiResponseCommonInterface<FindRoleResponse[]>> => {
  return http.get(endpoint.roles.GET_ROLES);
};

/**
 * Add a new user role
 * @param roleData Role data to add
 * @returns Promise with API response
 */
export const addUserRole = (roleData: UserRoleForm): Promise<IApiResponseCommonInterface<FindRoleResponse>> => {
  return http.post(endpoint.roles.ADD_USER_ROLE, roleData);
};

/**
 * Update an existing user role
 * @param roleId ID of the role to update
 * @param roleData Updated role data
 * @returns Promise with API response
 */
export const updateUserRole = (roleId: number, roleData: UserRoleForm): Promise<IApiResponseCommonInterface<FindRoleResponse>> => {
  return http.put(`${endpoint.roles.UPDATE_USER_ROLE}/${roleId}`, roleData);
};

/**
 * Delete a user role
 * @param roleId ID of the role to delete
 * @returns Promise with API response
 */
export const deleteUserRole = (roleId: number): Promise<IApiResponseCommonInterface<FindRoleResponse>> => {
  return http.remove(`${endpoint.roles.DELETE_USER_ROLE}/${roleId}`);
};

/**
 * Get role permissions with counts and last modified date
 * @param organizationId ID of the organization
 * @returns Promise with API response
 */
export const getRolePermissions = (offset: number, limit: number, search?: string): Promise<IApiResponseCommonInterface<IRolePermission[]>> => {
  return http.get(endpoint.roles.GET_ROLE_PERMISSIONS, { offset, limit, search });
};

/**
 * Get detailed permissions for a specific role
 * @param roleId ID of the role to get permissions for
 * @returns Promise with API response
 */
export const getRolePermissionsById = (roleId: number): Promise<IApiResponseCommonInterface<RolePermissionResponse>> => {
  const url = endpoint.roles.GET_ROLE_PERMISSIONS_BY_ID.replace(":roleId", roleId.toString());
  return http.get(url);
};

/**
 * Update permissions for a specific role
 * @param roleId ID of the role to update permissions for
 * @param permissionIds Array of permission IDs
 * @returns Promise with API response
 */
export const updateRolePermissions = (
  roleId: number,
  permissionIds: number[]
): Promise<IApiResponseCommonInterface<UpdateRolePermissionsResponse>> => {
  const url = endpoint.roles.UPDATE_ROLE_PERMISSIONS.replace(":roleId", roleId.toString());
  return http.put(url, { permissionIds });
};
