import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  getCurrentSubscription,
  getAllPlans,
  cancelSubscription,
  getAllTransactions,
  buySubscription,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import { authorizedForManageSubscriptions } from "../../middleware/isAuthorized";
import { buySubscriptionValidation } from "./validation";
import { schemaValidation } from "../../middleware/validateSchema";

const subscriptionRoutes = express.Router();

/**
 * @swagger
 * /api/subscription/current:
 *   get:
 *     summary: Get Current Subscription
 *     description: Returns the current active subscription details for the authenticated organization.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current subscription retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Current subscription retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     planId:
 *                       type: number
 *                       example: 2
 *                     planName:
 *                       type: string
 *                       example: Professional Plan
 *                     subscriptionStatus:
 *                       type: string
 *                       enum: [active, canceled, past_due, unpaid]
 *                       example: active
 *                     expiryDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-07-01T00:00:00Z"
 *                     nextBillingDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-06-30T00:00:00Z"
 *                     pricingType:
 *                       type: string
 *                       enum: [monthly, yearly]
 *                       example: yearly
 *                     price:
 *                       type: number
 *                       example: 4999
 *                     isActive:
 *                       type: boolean
 *                       example: true
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       404:
 *         description: No active subscription found for this organization
 *       500:
 *         description: Server error while retrieving subscription details
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.CURRENT,
  auth,
  HandleErrors(getCurrentSubscription)
);

/**
 * @swagger
 * /api/subscription/all:
 *   get:
 *     summary: Get All Available Subscription Plans
 *     description: Returns a list of all available subscription plans with their features and pricing options.
 *     tags:
 *       - Subscription Routes
 *     responses:
 *       200:
 *         description: Available plans retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: All available plans retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       planId:
 *                         type: number
 *                         example: 2
 *                       planName:
 *                         type: string
 *                         example: Professional Plan
 *                       price:
 *                         type: number
 *                         example: 1999
 *                       pricingType:
 *                         type: string
 *                         enum: [monthly, yearly]
 *                         example: monthly
 *                       isActive:
 *                         type: boolean
 *                         example: true
 *                       description:
 *                         type: string
 *                         example: "Professional plan with advanced features"
 *                       features:
 *                         type: array
 *                         items:
 *                           type: string
 *                           example: "Unlimited assessments"
 *       500:
 *         description: Server error while retrieving plans
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.ALL_PLANS,
  auth,
  authorizedForManageSubscriptions,
  HandleErrors(getAllPlans)
);

/**
 * @swagger
 * /subscription/cancel:
 *   post:
 *     summary: Cancel Subscription
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: string
 *                 example: user_12345
 *             required:
 *               - user_id
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 code:
 *                   type: number
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: Old plan successfully canceled. You will be upgraded to the new plan.
 */
subscriptionRoutes.post(
  ROUTES.SUBSCRIPTION.CANCEL,
  auth,
  authorizedForManageSubscriptions,
  // schemaValidation(cancelSubscriptionValidation),
  HandleErrors(cancelSubscription)
);

/**
 * @swagger
 * /api/subscription/transactions:
 *   get:
 *     summary: Get Transaction Details
 *     description: Returns all transaction details for the authenticated user's organization
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Transaction details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Success
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: number
 *                         example: 123
 *                       payment_status:
 *                         type: string
 *                         enum: [Success, Pending, Failed]
 *                         example: Success
 *                       amount:
 *                         type: number
 *                         example: 49.99
 *                       transaction_type:
 *                         type: string
 *                         enum: [Purchase, Refund, Upgrade]
 *                         example: Purchase
 *                       transaction_method:
 *                         type: string
 *                         example: Card
 *                       transaction_date:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-07-15T10:30:00Z"
 *                       invoice_id:
 *                         type: string
 *                         example: "inv_123456789"
 *                       invoice_url:
 *                         type: string
 *                         example: "https://dashboard.stripe.com/invoices/inv_123456789"
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       404:
 *         description: No active subscription found for this organization
 *       500:
 *         description: Server error while retrieving transaction details
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.TRANSACTIONS,
  auth,
  HandleErrors(getAllTransactions)
);

/**
 * @swagger
 * /api/subscription/buy-subscription:
 *   post:
 *     summary: Buy Subscription Plan
 *     description: Initiates a subscription purchase by validating plan details, ensuring Stripe customer exists, and creating a checkout session. This is a streamlined API that combines validation, customer creation, and checkout session creation into a single endpoint.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - planId
 *               - pricingId
 *             properties:
 *               planId:
 *                 type: integer
 *                 description: The ID of the subscription plan to purchase
 *                 example: 2
 *               pricingId:
 *                 type: integer
 *                 description: The ID of the pricing option (monthly/yearly)
 *                 example: 3
 *     responses:
 *       200:
 *         description: Subscription purchase initiated successfully - checkout session created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Subscription purchase initiated successfully. Please complete the checkout process."
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                       description: Stripe checkout session ID
 *                       example: "cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
 *                     checkoutUrl:
 *                       type: string
 *                       description: Stripe checkout session URL to redirect user
 *                       example: "https://checkout.stripe.com/c/pay/cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
 *                     planName:
 *                       type: string
 *                       description: Name of the selected subscription plan
 *                       example: "Professional Plan"
 *                     price:
 *                       type: number
 *                       description: Price of the selected plan in cents
 *                       example: 4999
 *                     stripeCustomerId:
 *                       type: string
 *                       description: Stripe customer ID for the organization
 *                       example: "cus_1234567890abcdef"
 *       400:
 *         description: Bad request - Invalid plan ID, pricing ID, or validation failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   examples:
 *                     invalid_plan:
 *                       value: "Invalid plan ID or plan not found"
 *                     invalid_pricing:
 *                       value: "Invalid pricing ID or pricing not found"
 *                     same_plan:
 *                       value: "You are already subscribed to this plan"
 *                     inactive_plan:
 *                       value: "Selected plan is not active"
 *                 data:
 *                   type: null
 *                   example: null
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       404:
 *         description: Organization not found or no Stripe customer exists
 *       500:
 *         description: Server error during subscription purchase process
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to create checkout session or internal server error"
 *                 data:
 *                   type: null
 *                   example: null
 */
subscriptionRoutes.post(
  ROUTES.SUBSCRIPTION.BUY_SUBSCRIPTION,
  auth,
  authorizedForManageSubscriptions,
  schemaValidation(buySubscriptionValidation),
  HandleErrors(buySubscription)
);

export default subscriptionRoutes;
