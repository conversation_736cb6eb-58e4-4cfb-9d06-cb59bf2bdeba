"use client";
import React, { useEffect } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import Image from "next/image";
import { Controller, useForm } from "react-hook-form";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";
import OtpInput from "react-otp-input";
import "react-phone-input-2/lib/style.css";

import logo from "../../../public/assets/images/logo.svg";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import styles from "@/styles/auth.module.scss";
import { verifyOTPValidation } from "@/validations/authValidations";
import { resendOTP, verifyOTP } from "@/services/authServices";
import routes from "@/constants/routes";
import { toastMessageSuccess, toastMessageError } from "@/utils/helper";

const Verify = () => {
  const t = useTranslations();
  const [loading, setLoading] = useState(false);
  const [resendOtpLoading, setResendOtpLoading] = useState(false);
  const [counter, setCounter] = useState(60);
  const [linkDisabled, setLinkDisabled] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();
  const emailParam = searchParams?.get("email");
  const email = emailParam ? decodeURIComponent(emailParam) : "";

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(verifyOTPValidation(t)),
  });

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (counter > 0) timer = setInterval(() => setCounter(counter - 1), 1000);
    else if (counter === 0) {
      setLinkDisabled(false);
    }
    return () => clearInterval(timer);
  }, [counter]);

  const onSubmit = async (data: { otp: number }) => {
    setLoading(true);
    toast.dismiss();
    try {
      setLoading(true);
      const payLoad = {
        email,
        otp: data?.otp.toString(),
      };
      const result = await verifyOTP(payLoad);

      if (result?.data?.success) {
        router.push(
          `${routes.RESET_PASSWORD}?email=${encodeURIComponent(email)}&otp=${encodeURIComponent(result?.data?.data?.toString() as string)}`
        );
        toastMessageSuccess(t(result?.data?.message as string));
        setLoading(false);
      } else {
        setLoading(false);
        toastMessageError(t(result?.data?.message as string));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setResendOtpLoading(true);
    try {
      setCounter(60);
      setLinkDisabled(true);

      const payload = {
        email,
      };
      const result = await resendOTP(payload);
      if (result?.data?.success) {
        toastMessageSuccess(t(result?.data?.message as string));
        reset();
      } else {
        toastMessageError(t((result?.data?.message as string) ?? "something_went_wrong"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setResendOtpLoading(false);
    }
  };

  const handleOtpChange = (phone: string) => {
    setValue("otp", phone === "0" ? 0 : Number(phone));
  };

  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row">
          <div className={styles.user_auth_main}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`}>
                  {/* <div className={styles.client_signature_box}>
                    <p>
                      The challenge is great, the effort is extraordinary, the achievement is life changing, and the impact will become your legacy.
                      Where are you now and what are you willing to change to get to where you want to be?
                    </p>
                    <Image src={ClientSignature} alt="client" />
                  </div> */}
                </div>
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center">
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} />
                      <h1>
                        {t("verify")} <span>{t("code")}</span>
                      </h1>
                    </div>
                    <form onSubmit={handleSubmit(onSubmit)}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="otp" required>
                          {t("enter_verification_code")}
                        </InputWrapper.Label>
                        <Controller
                          name="otp"
                          control={control}
                          render={({ field }) => (
                            <div className="otp-main">
                              <OtpInput
                                numInputs={4}
                                value={field.value as unknown as string}
                                onChange={handleOtpChange}
                                renderInput={(props) => <input {...props} />}
                                inputType="text"
                              />
                            </div>
                          )}
                        />
                        <InputWrapper.Error message={errors?.otp?.message || ""} />
                      </InputWrapper>

                      <Button loading={loading} disabled={loading} className="primary-btn rounded-md w-100 mt-5">
                        {t("verify")}
                      </Button>
                    </form>
                    {!linkDisabled ? (
                      <Button
                        loading={resendOtpLoading}
                        onClick={handleResendOtp}
                        disabled={resendOtpLoading}
                        className="primary-btn rounded-md w-100 mt-5"
                      >
                        {t("resend_otp")}
                      </Button>
                    ) : (
                      <p className="text-center mt-3  ">
                        {t("resend_access_code_in")} {counter} {t("seconds")}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Verify;
