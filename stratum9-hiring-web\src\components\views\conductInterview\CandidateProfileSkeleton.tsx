import Button from "@/components/formElements/Button";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import PreviewResumeIcon from "@/components/svgComponents/PreviewResumeIcon";
import style from "../../../styles/conductInterview.module.scss";
import { useState } from "react";

import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
const CandidateProfileSkeleton = () => {
  const [selectedTab, setSelectedTab] = useState(true);

  const handleTabClick = () => {
    setSelectedTab(!selectedTab);
  };

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <Button className="clear-btn p-0 m-0">
                  <BackArrowIcon onClick={() => {}} />
                </Button>{" "}
                Candidate <span>Profile</span>
              </h2>
              <Button className="clear-btn text-btn primary p-0 m-0  ">
                <PreviewResumeIcon className="me-2" />
                Preview Candidate Resume
              </Button>
            </div>
          </div>
        </div>
        <div className="inner-section profile-section">
          <div className="candidate-profile">
            <Skeleton height={100} width={100} borderRadius={12} />
            <div className="candidate-info">
              <Skeleton height={22} width={"20%"} borderRadius={6} className="mb-2" />
              <div className="info-container">
                <div className="info-item">
                  <p className="info-title">
                    <Skeleton height={16} width={100} borderRadius={6} className="mb-3" />
                  </p>
                  <p className="info-value">
                    <Skeleton height={18} width={100} borderRadius={6} />
                  </p>
                </div>
                <div className="info-item">
                  <p className="info-title">
                    <Skeleton height={16} width={100} borderRadius={6} className="mb-3" />
                  </p>
                  <p className="info-value">
                    <Skeleton height={18} width={100} borderRadius={6} />
                  </p>
                </div>
                <div className="info-item">
                  <p className="info-title">
                    <Skeleton height={16} width={100} borderRadius={6} className="mb-3" />
                  </p>
                  <p className="info-value">
                    <Skeleton height={18} width={100} borderRadius={6} />
                  </p>
                </div>
                <div className="info-item">
                  <p className="info-title">
                    <Skeleton height={16} width={100} borderRadius={6} className="mb-3" />
                  </p>
                  <p className="info-value">
                    <Skeleton height={18} width={100} borderRadius={6} />
                  </p>
                </div>
                <div className="info-item">
                  <p className="info-title">
                    <Skeleton height={16} width={125} borderRadius={6} className="mb-3" />
                  </p>
                  <p className="info-value with-img">
                    {" "}
                    <Skeleton height={18} width={18} borderRadius={100} /> <Skeleton height={18} width={100} borderRadius={4} />
                  </p>
                </div>
                <div className="info-item">
                  <p className="info-title">
                    <Skeleton height={16} width={100} borderRadius={6} className="mb-3" />
                  </p>
                  <p className="info-value">
                    <Skeleton height={18} width={100} borderRadius={6} />
                  </p>
                </div>
                <div className="button-align">
                  <Skeleton height={47} width={90} borderRadius={12} />
                  <Skeleton height={47} width={90} borderRadius={12} />
                </div>
              </div>
            </div>
          </div>
          <div className="common-tab mb-5">
            <li className={selectedTab ? "active" : ""} onClick={handleTabClick}>
              Skill-Specific Assessment
            </li>
            <li className={!selectedTab ? "active" : ""} onClick={handleTabClick}>
              Interview History
            </li>
          </div>
          {selectedTab && (
            <div className="assessment-content">
              <div className="row g-4">
                <div className="col-md-4">
                  <div className="improvement-areas-card">
                    <Skeleton height={200} width={"100%"} borderRadius={20} />
                  </div>
                </div>
                <div className="col-md-8">
                  <div className="improvement-areas-card">
                    <Skeleton height={200} width={"100%"} borderRadius={20} />
                  </div>
                </div>
              </div>
              <div className="row g-4 mt-2">
                <div className="col-md-6">
                  <div className="improvement-areas-card">
                    <Skeleton height={305} width={"100%"} borderRadius={20} />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="improvement-areas-card">
                    <Skeleton height={305} width={"100%"} borderRadius={20} />
                  </div>
                </div>
              </div>
              {/* next design */}
              <div className="row g-4">
                <div className="col-lg-4">
                  <div className="improvement-areas-card">
                    <Skeleton height={400} width={"100%"} borderRadius={20} />
                  </div>
                </div>
                <div className="col-lg-8">
                  <div className="improvement-areas-card">
                    <Skeleton height={400} width={"100%"} borderRadius={20} />
                  </div>
                </div>
                <div className="col-12">
                  <div className="improvement-areas-card">
                    <Skeleton height={18} width={"20%"} borderRadius={6} />
                    <div className="row g-3 mt-3">
                      <div className="col-md-4">
                        <Skeleton height={177} width={"100%"} borderRadius={24} />
                      </div>
                      <div className="col-md-4">
                        <Skeleton height={177} width={"100%"} borderRadius={24} />
                      </div>
                      <div className="col-md-4">
                        <Skeleton height={177} width={"100%"} borderRadius={24} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* end next design */}
            </div>
          )}
          {!selectedTab && (
            <div className="history-content">
              <div className="interview-summary">
                <div className="summary-header">
                  <Skeleton height={21} width={"20%"} borderRadius={6} />
                </div>
                <div className="interviewer">
                  <Skeleton height={21} width={"20%"} className="mb-4" borderRadius={6} />

                  <div className="interviewer-info">
                    <Skeleton height={45} width={45} borderRadius={100} />
                    <Skeleton height={18} width={100} borderRadius={6} />
                  </div>
                </div>
                <div className="summary-scores">
                  <Skeleton height={18} width={100} className="mb-4" borderRadius={6} />
                  <div className="score-btns mt-2">
                    <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                    <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                    <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                    <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                    <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                  </div>
                </div>
                <div className="summary-highlights">
                  <Skeleton height={18} width={100} className="mb-4" borderRadius={6} />
                  <ul className="highlight-list p-0">
                    <Skeleton height={16} width={"80%"} className="mb-3" borderRadius={6} count={4} />
                  </ul>
                </div>
              </div>
              <div className="interview-summary">
                <div className="summary-header">
                  <Skeleton height={21} width={"20%"} borderRadius={6} />
                </div>
                <div className="interviewer">
                  <div className="interviewer-info large">
                    <Skeleton height={45} width={45} borderRadius={100} />
                    <Skeleton height={18} width={100} borderRadius={6} />
                  </div>
                </div>
                <div className="summary-highlights">
                  <Skeleton height={18} width={100} borderRadius={6} className="mb-4" />
                  <ul className="highlight-list p-0">
                    <Skeleton height={16} width={"80%"} className="mb-3" borderRadius={6} count={4} />
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CandidateProfileSkeleton;
