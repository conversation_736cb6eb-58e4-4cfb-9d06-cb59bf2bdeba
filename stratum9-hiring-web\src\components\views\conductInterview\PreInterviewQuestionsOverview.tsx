"use client";
import React, { use, useEffect, useMemo, useRef, useState } from "react";

import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import Skeleton from "react-loading-skeleton";
import { useDispatch, useSelector } from "react-redux";

import AddUpdateQuestionModal from "@/components/commonModals/AddUpdateQuestionModal";
import ConductingInterviewsModal from "@/components/commonModals/ConductingInterviewsModal";
import Button from "@/components/formElements/Button";
import DownloadResumeIcon from "@/components/svgComponents/DownloadResumeIcon";
import EditSkillIcon from "@/components/svgComponents/EditSkillIcon";
import { INTERVIEW_SCHEDULE_ROUND_TYPE, PERMISSION } from "@/constants/commonConstants";
import {
  IAddInterviewSkillQuestion,
  IGetInterviewSkillQuestionsResponse,
  IInterviewStaticInformation,
  IUpdateInterviewSkillQuestion,
} from "@/interfaces/interviewInterfaces";
import { setInterviewQuestions, setInterviewStaticInformation } from "@/redux/slices/interviewSlice";
import {
  addInterviewSkillQuestion,
  conductInterviewStaticInformation,
  getInterviewSkillQuestions,
  updateInterviewSkillQuestion,
} from "@/services/interviewServices";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";

import { PerformanceCardSkeleton } from "./skeletons/PerformanceCardSkeleton";
import styles from "../../../styles/accessManagement.module.scss";
import ROUTES from "@/constants/routes";
import { AuthState } from "@/redux/slices/authSlice";

export const ModalMode = {
  ADD: "add",
  UPDATE: "update",
};

export const SkillType = {
  ROLE_SPECIFIC: "role_specific",
  CULTURE_SPECIFIC: "culture_specific",
  CAREER_BASED: "career_based",
};

const PreInterviewQuestionsOverview = ({
  params,
}: {
  params: Promise<{ interviewId: string; jobApplicationId: string; interviewType: string; resumeLink: string; isEnded: string; date: string }>;
}) => {
  const router = useRouter();
  const interviewQuestionsData = useSelector(
    (state: { interview: IGetInterviewSkillQuestionsResponse & { interviewStaticInformation: IInterviewStaticInformation } }) => state.interview
  );

  const t = useTranslations();

  const dispatch = useDispatch();

  const initialFetchDone = useRef(false);
  const searchParamsPromise = use(params);
  const interviewId = +searchParamsPromise.interviewId;
  const jobApplicationId = +searchParamsPromise.jobApplicationId;
  const isEnded = +searchParamsPromise.isEnded;
  const interviewType = searchParamsPromise.interviewType;
  const resumeLink = searchParamsPromise.resumeLink;
  const interviewDate = searchParamsPromise.date;

  console.log(interviewId, jobApplicationId);

  const careerBasedQuestions = interviewQuestionsData.careerBasedQuestions;
  const roleSpecificQuestions = interviewQuestionsData.roleSpecificQuestions;
  const cultureSpecificQuestions = interviewQuestionsData.cultureSpecificQuestions;
  const interviewStaticInformation = interviewQuestionsData.interviewStaticInformation;

  const [showAddUpdateQuestionModal, setShowAddUpdateQuestionModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showConductingInterviewsModal, setShowConductingInterviewsModal] = useState(false);

  const [cultureSpecificSkillTitle, setCultureSpecificSkillTitle] = useState<string>("");
  const [roleSpecificSkillTitle, setRoleSpecificSkillTitle] = useState<string>("");

  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];
  const hasManagePreInterviewQuestionsPermission = userPermissions.includes(PERMISSION.MANAGE_PRE_INTERVIEW_QUESTIONS);

  const [currentQuestion, setCurrentQuestion] = useState<IAddInterviewSkillQuestion | IUpdateInterviewSkillQuestion | null>(null);
  const [modalMode, setModalMode] = useState<(typeof ModalMode)[keyof typeof ModalMode]>(ModalMode.ADD);

  const roleSkills = useMemo(() => Object.keys(interviewQuestionsData.roleSpecificQuestions || {}), [interviewQuestionsData.roleSpecificQuestions]);

  const cultureSkills = useMemo(
    () => Object.keys(interviewQuestionsData.cultureSpecificQuestions || {}),
    [interviewQuestionsData.cultureSpecificQuestions]
  );

  const isDataAlreadyExist = useMemo(() => {
    return interviewQuestionsData && roleSkills.length > 0 && cultureSkills.length > 0;
  }, [interviewQuestionsData, roleSkills, cultureSkills]);

  const isExpired = useMemo(() => {
    const currentDate = new Date();
    const date = new Date(interviewDate);
    return date < currentDate;
  }, [interviewDate]);

  console.log("isExpired", isExpired);

  // Set default skill titles when Redux data is available
  useEffect(() => {
    if (isDataAlreadyExist) {
      // Only set if not already set
      if (!roleSpecificSkillTitle) {
        const roleSpecificSkill = roleSkills[0];
        setRoleSpecificSkillTitle(roleSpecificSkill);
      }

      if (!cultureSpecificSkillTitle) {
        const cultureSpecificSkill = cultureSkills[0];
        setCultureSpecificSkillTitle(cultureSpecificSkill);
      }
    }
  }, [isDataAlreadyExist]);

  useEffect(() => {
    if (initialFetchDone.current) return;
    getInterviewQuestions(true);
    if (Object.keys(interviewStaticInformation).length === 0) {
      getInterviewStaticInformation();
    }
    initialFetchDone.current = true;
  }, []);

  const getInterviewStaticInformation = async () => {
    try {
      setLoader(true);
      const response = await conductInterviewStaticInformation();

      if (response?.data?.success) {
        dispatch(setInterviewStaticInformation(response?.data?.data));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoader(false);
    }
  };

  const getInterviewQuestions = async (isInitialFetch = false) => {
    try {
      setLoader(true);
      const response = await getInterviewSkillQuestions({ jobApplicationId, interviewId });

      if (response?.data?.success) {
        const cultureSpecificSkill = Object.keys(response?.data?.data?.cultureSpecificQuestions);
        const roleSpecificSkill = Object.keys(response?.data?.data?.roleSpecificQuestions);

        if (isInitialFetch) {
          setCultureSpecificSkillTitle(cultureSpecificSkill[0]);
          setRoleSpecificSkillTitle(roleSpecificSkill[0]);
        }

        const careerBasedQuestions = response?.data?.data?.careerBasedQuestions;
        const cultureSpecificQuestions = response?.data?.data?.cultureSpecificQuestions;
        const roleSpecificQuestions = response?.data?.data?.roleSpecificQuestions;

        dispatch(setInterviewQuestions({ careerBasedQuestions, cultureSpecificQuestions, roleSpecificQuestions }));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoader(false);
    }
  };

  // Download questions as a text file
  const downloadQuestions = () => {
    // Create a JSON object with all question types
    const questionsData = {
      careerBasedQuestions,
      roleSpecificQuestions,
      cultureSpecificQuestions,
    };

    // Convert to JSON string with pretty formatting
    const jsonString = JSON.stringify(questionsData, null, 2);

    // Create a Blob with JSON content
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = url;
    link.download = `interview-questions-${interviewId}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toastMessageSuccess(t("questions_downloaded_successfully"));
  };

  const addNewInterviewQuestion = async (data: IAddInterviewSkillQuestion) => {
    try {
      setLoading(true);
      const response = await addInterviewSkillQuestion(data);

      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const updateInterviewQuestion = async (data: IUpdateInterviewSkillQuestion) => {
    try {
      setLoading(true);
      const response = await updateInterviewSkillQuestion(data);

      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleAddQuestion = (skillType: string, jobSkillId?: number) => {
    // Set up data for creating a new question
    const newQuestionData: IAddInterviewSkillQuestion = {
      jobApplicationId,
      interviewId,
      question: "",
      skillType,
      jobSkillId,
    };

    setCurrentQuestion(newQuestionData);
    setModalMode(ModalMode.ADD);
    setShowAddUpdateQuestionModal(true);
  };

  const handleEditQuestion = (question: { id: number; question: string }) => {
    // Set up data for editing an existing question
    const questionData: IUpdateInterviewSkillQuestion = {
      interviewQuestionId: question.id,
      question: question.question,
    };

    setCurrentQuestion(questionData);
    setModalMode(ModalMode.UPDATE);
    setShowAddUpdateQuestionModal(true);
  };

  const handleQuestionSubmit = async (data: IAddInterviewSkillQuestion | IUpdateInterviewSkillQuestion) => {
    if (modalMode === ModalMode.UPDATE) {
      // Update existing question
      await updateInterviewQuestion(data as IUpdateInterviewSkillQuestion);
    } else {
      // Add new question
      await addNewInterviewQuestion(data as IAddInterviewSkillQuestion);
    }

    // Close modal and refresh questions
    setShowAddUpdateQuestionModal(false);
    await getInterviewQuestions();
  };

  return (
    <>
      <section className={styles.conduct_interview}>
        <div className="container">
          <div className="row">
            <div className="col-md-12">
              <div className="common-page-header">
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>
                      {t("pre_interview")} <span>{t("questions_overview")}</span>
                    </h2>
                    <div className="button-align justify-content-end w-50">
                      <Button className="clear-btn text-btn primary p-0 m-0  " onClick={downloadQuestions}>
                        <DownloadResumeIcon className="me-2" />
                        {t("download_questions")}
                      </Button>
                    </div>
                  </div>
                  <p className="description">{t("pre_interview_description")}</p>
                </div>
              </div>
              <div className="inner-section">
                {/* Career-Based Skills */}
                <div className="section-heading">
                  <h2>
                    {t("for")} <span>{t("career_based_skills")}</span>
                  </h2>
                </div>
                <div className="row g-4">
                  {loader && !isDataAlreadyExist ? (
                    <PerformanceCardSkeleton count={4} />
                  ) : (
                    careerBasedQuestions?.questions?.map((question, index) => (
                      <div className="col-md-4" key={question.id}>
                        <div className="overview-skill-card" data-text={index < 9 ? `0${index + 1}` : index + 1}>
                          <p className="overview-txt">{question.question}</p>
                          {!isEnded && !isExpired && hasManagePreInterviewQuestionsPermission ? (
                            <Button className="clear-btn p-0 m-0" onClick={() => handleEditQuestion(question)}>
                              <EditSkillIcon /> {t("edit_question")}
                            </Button>
                          ) : null}
                        </div>
                      </div>
                    ))
                  )}

                  {!isEnded && !isExpired && careerBasedQuestions?.questions?.length < 8 && hasManagePreInterviewQuestionsPermission ? (
                    <div className="col-md-12">
                      <Button
                        className="clear-btn text-btn secondary p-0 m-0"
                        onClick={() => handleAddQuestion(SkillType.CAREER_BASED, careerBasedQuestions?.questions?.[0]?.jobSkillId)}
                      >
                        {" "}
                        {t("add_new_question")}
                      </Button>
                    </div>
                  ) : null}
                </div>
              </div>

              {/* section saparater start*/}
              <div className="dotted-border my-5" />
              {/* section saparater end*/}

              <div className="section-heading">
                <h2>
                  {t("for")} <span>{t("role_specific_performance_based_skills")}</span>
                </h2>
              </div>
              <div className="row g-4">
                {/* skeletons for role specific skills */}
                {loader && !isDataAlreadyExist ? (
                  <>
                    <ul className="role-list px-3">
                      <Skeleton height={70} width={145} borderRadius={16} />
                      <Skeleton height={70} width={174} borderRadius={16} />
                      <Skeleton height={70} width={168} borderRadius={16} />
                      <Skeleton height={70} width={147} borderRadius={16} />
                      <Skeleton height={70} width={171} borderRadius={16} />
                      <Skeleton height={70} width={144} borderRadius={16} />
                      <Skeleton height={70} width={128} borderRadius={16} />
                      <Skeleton height={70} width={150} borderRadius={16} />
                      <Skeleton height={70} width={86} borderRadius={16} />
                      <Skeleton height={70} width={166} borderRadius={16} />
                    </ul>
                    <PerformanceCardSkeleton count={3} />
                  </>
                ) : (
                  <>
                    <div className="col-md-12">
                      <ul className="role-list secondary mb-4" aria-label="Job roles list">
                        {Object.keys(roleSpecificQuestions).map((skill, index) => (
                          <li
                            key={skill}
                            className={`role-item ${roleSpecificSkillTitle === skill ? "active" : ""}`}
                            tabIndex={index}
                            onClick={() => setRoleSpecificSkillTitle(skill)}
                          >
                            {skill}
                          </li>
                        ))}
                      </ul>
                    </div>
                    {roleSpecificQuestions[roleSpecificSkillTitle]?.questions?.map((question, index) => (
                      <div className="col-md-4" key={question.id}>
                        <div className="overview-skill-card" data-text={index < 9 ? `0${index + 1}` : index + 1}>
                          <p className="overview-txt">{question.question}</p>
                          {!isEnded && !isExpired && hasManagePreInterviewQuestionsPermission ? (
                            <Button className="clear-btn p-0 m-0" onClick={() => handleEditQuestion(question)}>
                              <EditSkillIcon /> {t("edit_question")}
                            </Button>
                          ) : null}
                        </div>
                      </div>
                    ))}
                  </>
                )}
                {!isEnded &&
                !isExpired &&
                roleSpecificQuestions[roleSpecificSkillTitle]?.questions?.length < 6 &&
                hasManagePreInterviewQuestionsPermission ? (
                  <div className="col-md-12">
                    <Button
                      className="clear-btn text-btn secondary p-0 m-0"
                      onClick={() => {
                        handleAddQuestion(SkillType.ROLE_SPECIFIC, roleSpecificQuestions?.[roleSpecificSkillTitle]?.questions?.[0]?.jobSkillId);
                      }}
                    >
                      {" "}
                      {t("add_new_question")}
                    </Button>
                  </div>
                ) : null}
              </div>

              {/* section saparater start*/}
              <div className="dotted-border my-5" />
              {/* section saparater end*/}

              <div className="section-heading">
                <h2>
                  {t("for")} <span>{t("culture_specific_performance_based_skills")}</span>
                </h2>
              </div>
              <div className="row g-4">
                {/* skeletons for role specific skills */}
                {loader && !isDataAlreadyExist ? (
                  <>
                    <ul className="role-list px-3">
                      <Skeleton height={70} width={168} borderRadius={16} />
                      <Skeleton height={70} width={114} borderRadius={16} />
                      <Skeleton height={70} width={180} borderRadius={16} />
                      <Skeleton height={70} width={169} borderRadius={16} />
                      <Skeleton height={70} width={132} borderRadius={16} />
                    </ul>
                    <PerformanceCardSkeleton count={3} />
                  </>
                ) : (
                  <>
                    <div className="col-md-12">
                      <ul className="role-list secondary mb-4" aria-label="Job roles list">
                        {Object.keys(cultureSpecificQuestions).map((skill, index) => (
                          <li
                            key={skill}
                            className={`role-item ${cultureSpecificSkillTitle === skill ? "active" : ""}`}
                            tabIndex={index}
                            onClick={() => setCultureSpecificSkillTitle(skill)}
                          >
                            {skill}
                          </li>
                        ))}
                      </ul>
                    </div>
                    {cultureSpecificQuestions[cultureSpecificSkillTitle]?.questions?.map((question, index) => (
                      <div className="col-md-4" key={question.id}>
                        <div className="overview-skill-card" data-text={index < 9 ? `0${index + 1}` : index + 1}>
                          <p className="overview-txt">{question.question}</p>
                          {!isEnded && !isExpired && hasManagePreInterviewQuestionsPermission ? (
                            <Button className="clear-btn p-0 m-0" onClick={() => handleEditQuestion(question)}>
                              <EditSkillIcon /> {t("edit_question")}
                            </Button>
                          ) : null}
                        </div>
                      </div>
                    ))}
                  </>
                )}

                {!isEnded &&
                !isExpired &&
                cultureSpecificQuestions[cultureSpecificSkillTitle]?.questions?.length < 6 &&
                hasManagePreInterviewQuestionsPermission ? (
                  <div className="col-md-12">
                    <Button
                      className="clear-btn text-btn secondary p-0 m-0"
                      onClick={() =>
                        handleAddQuestion(
                          SkillType.CULTURE_SPECIFIC,
                          cultureSpecificQuestions?.[cultureSpecificSkillTitle]?.questions?.[0]?.jobSkillId
                        )
                      }
                    >
                      {t("add_new_question")}
                    </Button>
                  </div>
                ) : null}
              </div>

              <div className="button-align mt-5 pb-5">
                {!isEnded && !isExpired ? (
                  <Button onClick={() => setShowConductingInterviewsModal(true)} className="primary-btn rounded-md">
                    {t("proceed_to_interview")}
                  </Button>
                ) : null}
                <Button onClick={() => router.back()} className="dark-outline-btn rounded-md">
                  {t("cancel")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {showAddUpdateQuestionModal && (
        <AddUpdateQuestionModal
          onClickCancel={() => setShowAddUpdateQuestionModal(false)}
          mode={modalMode}
          initialData={currentQuestion || undefined}
          onSubmit={handleQuestionSubmit}
          isLoading={loading}
        />
      )}
      {showConductingInterviewsModal && (
        <ConductingInterviewsModal
          onClickCancel={() => setShowConductingInterviewsModal(false)}
          type={interviewType}
          onClickContinue={() => {
            setShowConductingInterviewsModal(false);

            if (interviewType === INTERVIEW_SCHEDULE_ROUND_TYPE[0].value) {
              router.push(
                `${ROUTES.INTERVIEW.INTERVIEW_QUESTION}?interviewId=${interviewId}&interviewType=${interviewType}&resumeLink=${resumeLink}`
              );
            }
          }}
        />
      )}
    </>
  );
};

export default PreInterviewQuestionsOverview;
