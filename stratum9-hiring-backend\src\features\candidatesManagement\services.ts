import { Brackets, In } from "typeorm";
import * as Sentry from "@sentry/node";
import dbConnection from "../../db/dbConnection";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import { ResponseObject } from "../../interface/commonInterface";
import JobApplicationsModel, {
  ApplicationRankStatus,
  Status,
} from "../../schema/s9-innerview/job_applications";
import {
  CANDIDATE_APPLICATION_MSG,
  DEFAULT_LIMIT,
  USER_TYPE,
} from "../../utils/constants";
import AuthServices from "../auth/services";
import ApplicantAdditionalInfoModel from "../../schema/s9-innerview/applicant_additional_info";
import { ApplicantAdditionalInfo } from "../resumeScreen/interface";
import InterviewModel from "../../schema/s9-innerview/interview";
import UserModel from "../../schema/s9/user";
import {
  FlattenedSkillScore,
  GroupedSkillScore,
  SkillEvaluationRaw,
} from "./interface";
// eslint-disable-next-line import/prefer-default-export

export class CandidateApplicationService {
  static async getAllCandidates(
    orgId: number,
    jobId: number,
    isActive?: boolean,
    searchStr: string = "",
    offset: number = 0,
    limit: number = DEFAULT_LIMIT
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const candidatesRepo = dataSource.getRepository(CandidatesModel);

      const query = candidatesRepo
        .createQueryBuilder("candidate")
        .innerJoin(
          "job_applications",
          "job_application",
          "job_application.candidate_id = candidate.id"
        )
        .where("candidate.orgId = :orgId", { orgId });

      if (jobId) {
        query.andWhere("job_application.job_id = :jobId", { jobId });
      }

      if (isActive === undefined) {
        query.andWhere(
          new Brackets((qb) => {
            qb.where("job_application.is_active IS NULL").orWhere(
              "job_application.is_active = :isActive",
              { isActive: true }
            );
          })
        );
      } else {
        query.andWhere("job_application.is_active = :isActive", { isActive });
      }

      if (searchStr.trim().length > 0) {
        query.andWhere("LOWER(candidate.name) LIKE LOWER(:searchStr)", {
          searchStr: `%${searchStr.trim()}%`,
        });
      }

      const data = await query
        .andWhere("job_application.isTopApplication = false")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .offset(offset)
        .limit(limit)
        .select([
          "candidate.id AS candidateId",
          "candidate.name AS candidateName",
          "job_application.id AS applicationId",
          "job_application.status AS applicationStatus",
          "job_application.source AS applicationSource",
          "job_application.created_ts AS applicationCreatedTs",
          "job_application.updated_ts AS applicationUpdatedTs",
          "job_application.is_active AS isActive",
          "job_application.job_id AS job_id",
          "job_application.hiring_manager_id AS hiring_manager_id",
          "job_application.hiring_manager_reason AS hiringManagerReason",
          "job_application.applicationRankStatus AS applicationRankStatus",
          "job_application.ai_reason AS aiReason",
          "job_application.ai_decision AS aiDecision",
          "JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS atsScore",
        ])
        .getRawMany();
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.candidates_fetched,
        data,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_all_candidates_failed,
        error: error.message,
      };
    }
  }

  static async archiveActiveApplication(
    applicationId: number,
    orgId: number,
    status: boolean,
    reason?: string
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const jobApplication = await jobAppRepo
        .createQueryBuilder("job_application")
        .innerJoinAndSelect("job_application.candidate", "candidate")
        .where("job_application.id = :applicationId", { applicationId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .getOne();

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }

      jobApplication.isActive = status;
      jobApplication.isTopApplication = false; // Reset top application status when archiving
      jobApplication.applicationRankStatus = ApplicationRankStatus.NO_CHANGES;
      jobApplication.updatedTs = new Date();

      if (status === false && typeof reason === "string") {
        jobApplication.hiringManagerReason = reason.trim();
      } else if (status === true) {
        jobApplication.hiringManagerReason = null;
      }

      await jobAppRepo.save(jobApplication);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error: error.message,
      };
    }
  }

  // get top candidates

  static getTopCandidates = async (orgId: number, jobId: number) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      const query = jobApplicationRepo
        .createQueryBuilder("job_application")
        .innerJoin("job_application.candidate", "candidate")
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("job_application.jobId = :jobId", { jobId })
        .andWhere("job_application.isActive = true")
        .andWhere("job_application.isTopApplication = true")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .select([
          "candidate.name AS candidateName",
          "job_application.id AS applicationId",
          "job_application.applicationRankStatus AS applicationRankStatus",
          "candidate.id AS candidateId",
          "job_application.status AS applicationStatus",
          "job_application.source AS applicationSource",
          "job_application.created_ts AS applicationCreatedTs",
          "job_application.updated_ts AS applicationUpdatedTs",
          "job_application.hiring_manager_reason AS hiringManagerReason",
          "job_application.ai_reason AS aiReason",
          "job_application.ai_decision AS aiDecision",
          "job_application.job_id AS job_id",
          "job_application.isTopApplication AS isTopApplication",
          "JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS atsScore",
        ]);

      const data = await query.getRawMany();

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.top_candidates_retrieved,
        data,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_top_candidates_failed,
        error: error.message,
      };
    }
  };

  // promote or demote candidates

  static promoteDemoteCandidate = async (
    candidateId: number,
    applicationId: number,
    action: ApplicationRankStatus.PROMOTED | ApplicationRankStatus.DEMOTED
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const application = await jobAppRepo.findOne({
        where: {
          id: applicationId,
          candidateId,
        },
      });

      if (!application) {
        throw new Error(
          CANDIDATE_APPLICATION_MSG.candidate_application_not_found
        );
      }

      const isTopApplication = action === ApplicationRankStatus.PROMOTED;
      const applicationRankStatus = isTopApplication
        ? ApplicationRankStatus.PROMOTED
        : ApplicationRankStatus.DEMOTED;

      application.isTopApplication = isTopApplication;
      application.applicationRankStatus = applicationRankStatus;

      await jobAppRepo.save(application);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_success,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_failed,
        error: error.message,
      };
    }
  };

  static getCandidateDetails = async (
    candidateId: number,
    orgId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const interviewRepo = dataSource.getRepository(InterviewModel);
      const query = dataSource
        .getRepository(CandidatesModel)
        .createQueryBuilder("candidate")
        .leftJoinAndSelect(
          "job_applications",
          "jobApplication",
          "jobApplication.candidateId = candidate.id"
        )
        .leftJoinAndSelect(
          "jobApplication.job",
          "job",
          "job.id = jobApplication.jobId"
        )

        .leftJoinAndSelect(
          "job.department",
          "department",
          "department.id = job.departmentId"
        )
        // .leftJoinAndSelect(
        //   "jobApplication.statusHistory",
        //   "statusHistory"
        // )
        .where("candidate.id = :candidateId", { candidateId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .select([
          "candidate.name as candidateName",
          "jobApplication.id as jobApplicationId",
          "jobApplication.jobId as jobId",
          "job.title as jobTitle",
          "jobApplication.status as status",
          "jobApplication.resume_file as resumeLink",
          "jobApplication.hiring_manager_id as hiringManagerId",
          "candidate.imageUrl AS imageUrl",
          "department.name AS department",
        ]);
      // .setParameter('clearedStatus', Status.APPROVED);

      const result = await query.getRawOne();

      const interviewInfo = await interviewRepo.findOne({
        where: {
          jobApplicationId: result.jobApplicationId,
        },
        order: {
          id: "DESC",
        },
      });
      const interviewerInfo = await AuthServices.getUserByUserId(
        result.hiringManagerId
      );

      if (!result) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found,
        };
      }

      return {
        success: true,
        data: {
          ...result,
          interviewerName: `${interviewerInfo?.first_name} ${interviewerInfo?.last_name}`,
          interviewerImage: interviewerInfo?.image,
          roundNumber: interviewInfo?.roundNumber,
        },
      };
    } catch (error: unknown) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.fetch_candidate_details_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static addApplicantAdditionalInfo = async (
    orgId: number,
    body: ApplicantAdditionalInfo
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      const jobApplication = await dataSource
        .getRepository(JobApplicationsModel)
        .findOne({
          where: {
            id: body.applicationId,
          },
        });

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }
      // Step 1: Check if candidate exists for given orgId and candidateId
      const candidate = await dataSource
        .getRepository(CandidatesModel)
        .findOne({
          where: {
            id: jobApplication.candidateId,
            orgId,
          },
        });

      if (!candidate) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found_for_org,
        };
      }

      // Step 2: Save additional info
      const repo = dataSource.getRepository(ApplicantAdditionalInfoModel);

      const newInfo = new ApplicantAdditionalInfoModel();
      newInfo.description = body.description;
      newInfo.images = body.images ? { urls: [body.images] } : null;

      const savedInfo = await repo.save(newInfo);
      console.log("savedInfo", savedInfo);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.additional_info_saved,
        data: savedInfo,
      };
    } catch (error: unknown) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.save_additional_info_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static updateJobApplicationStatus = async (
    jobApplicationId: number,
    status: string
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      // Check if job application exists
      const jobApplication = await jobApplicationRepo.findOne({
        where: { id: jobApplicationId },
      });

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_application_not_found,
        };
      }

      // Update the status
      // Convert string to Status enum value
      jobApplication.status = status as Status;

      // Save the updated job application
      const updatedJobApplication =
        await jobApplicationRepo.save(jobApplication);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
        data: updatedJobApplication,
      };
    } catch (error: unknown) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get candidate interview history
   * @param candidateId - The ID of the candidate
   * @returns A response object with the candidate's interview history
   */
  static getCandidateInterviewHistory = async (
    candidateId: number,
    interviewerId: number,
    applicationId: number,
    orgId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      // Optimized interview data query with efficient aggregation
      // Note: Cannot JOIN users table as it's in a different database
      const query = dataSource
        .createQueryBuilder()
        .select([
          "i.id AS interviewId",
          "i.round_number AS roundNumber",
          "i.interviewer_id AS interviewerId",
          "i.hard_skill_marks AS hardSkillMarks",
          "i.interview_summary AS interviewSummary",
          "i.interviewer_performance_ai_analysis AS interviewerPerformanceAiAnalysis",
          "i.interview_end_time AS endTime",
          // Use JSON_OBJECTAGG for efficient skill aggregation
          "JSON_OBJECTAGG(s.title, ise.skill_marks) AS skillScores",
        ])
        .from("job_applications", "ja")
        .innerJoin("interview", "i", "i.job_application_id = ja.id")
        .innerJoin(
          "interview_skill_evaluations",
          "ise",
          "ise.interview_id = i.id"
        )
        .innerJoin("skills", "s", "s.id = ise.skill_id")
        .andWhere("candidate.org_id = :orgId", { orgId })
        .where("ja.candidate_id = :candidateId", { candidateId })
        // .andWhere("ja.id = :applicationId", { applicationId }) // Uncomment this line to filter by application ID
        .groupBy(
          "i.round_number, i.interviewer_id, i.hard_skill_marks, i.interview_summary, i.interviewer_performance_ai_analysis"
        )
        .orderBy("i.round_number", "ASC");

      // Execute optimized interview data query
      const interviewHistory = await query.getRawMany();

      // Early return if no interview history found
      if (!interviewHistory || interviewHistory.length === 0) {
        return {
          success: true,
          message: CANDIDATE_APPLICATION_MSG.interview_history_retrieved,
          data: [],
        };
      }

      // Extract unique interviewer IDs efficiently using Set to avoid duplicates
      const interviewerIds = [
        ...new Set(interviewHistory.map((item) => item.interviewerId)),
      ];

      // Get current user's account type to check admin privileges
      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const currentUser = await userRepo.findOne({
        where: { id: interviewerId },
        select: ["account_type"],
      });

      const isAdmin = currentUser?.account_type === USER_TYPE.admin;

      // Optimized user lookup query - single query for all interviewer IDs
      // Using the separate database connection for users table
      const users = await userRepo.find({
        where: { id: In(interviewerIds) },
        select: ["id", "first_name", "last_name", "image", "account_type"],
      });

      // Create efficient lookup map for O(1) user data access
      const userMap = new Map(
        users.map((user) => [
          user.id,
          {
            name: `${user.first_name || ""} ${user.last_name || ""}`.trim(),
            image: user.image || "",
            accountType: user.account_type || "",
          },
        ])
      );

      // Optimized data transformation with minimal processing
      const formattedHistory = interviewHistory.map((item) => {
        // eslint-disable-next-line prefer-destructuring
        // Parse JSON fields only once and handle null cases efficiently
        // eslint-disable-next-line prefer-destructuring
        let interviewSummary = item.interviewSummary;
        // eslint-disable-next-line prefer-destructuring
        let skillScores = item.skillScores;
        let aiAnalysis = null;

        // Efficient JSON parsing with error handling
        try {
          if (typeof interviewSummary === "string" && interviewSummary) {
            interviewSummary = JSON.parse(interviewSummary);
          }
        } catch (e) {
          console.warn(
            `Failed to parse interview summary for round ${item.roundNumber}:`,
            e
          );
          interviewSummary = null;
        }

        try {
          if (typeof skillScores === "string" && skillScores) {
            skillScores = JSON.parse(skillScores);
          }
        } catch (e) {
          console.warn(
            `Failed to parse skill scores for round ${item.roundNumber}:`,
            e
          );
          skillScores = {};
        }

        // Parse AI analysis based on admin privileges or interviewer match
        if (item.interviewerPerformanceAiAnalysis) {
          try {
            const fullAnalysis =
              typeof item.interviewerPerformanceAiAnalysis === "string"
                ? JSON.parse(item.interviewerPerformanceAiAnalysis)
                : item.interviewerPerformanceAiAnalysis;

            if (isAdmin || item.interviewerId === interviewerId) {
              // Admin or own interview: send full data including highlights
              aiAnalysis = fullAnalysis;
            } else {
              // Non-admin viewing other interviewer: send only interviewerPerformance data, exclude highlights
              aiAnalysis = { ...fullAnalysis };
              delete aiAnalysis.highlights;
            }
          } catch (e) {
            console.warn(
              `Failed to parse AI analysis for round ${item.roundNumber}:`,
              e
            );
            aiAnalysis = null;
          }
        }

        // Get user information from the efficient lookup map
        const userInfo = userMap.get(item.interviewerId);

        // console.log("item", item);

        return {
          roundNumber: item.roundNumber,
          interviewerId: item.interviewerId,
          interviewerPerformanceAiAnalysis: aiAnalysis,
          interviewerName: userInfo?.name || "",
          interviewerImage: userInfo?.image || "",
          hardSkillMarks: item.hardSkillMarks,
          interviewSummary,
          skillScores: skillScores || {},
          endTime: item.endTime,
        };
      });
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.interview_history_retrieved,
        data: formattedHistory,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error fetching candidate interview history:", error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_interview_history_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get Application Final Summary for a candidate
   * @param candidateId - The ID of the candidate
   * @returns A response object with the candidate's skill specific assessment and final assessment data
   */

  static getApplicationFinalSummary = async (
    candidateId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      // Create query builder for final assessment data
      const finalAssessmentQuery = dataSource
        .createQueryBuilder()
        .select([
          "ja.id AS job_application_id",
          "fa.development_recommendations",
          "fa.skill_summary",
          "fa.overall_success_probability",
        ])
        .from("job_applications", "ja")
        .leftJoin("final_assessments", "fa", "fa.job_application_id = ja.id")
        .where("ja.candidate_id = :candidateId", { candidateId });

      const finalAssessmentData = await finalAssessmentQuery.getRawOne();

      // Transform final assessment data - handle potential null values from LEFT JOIN
      const formattedFinalAssessment = {
        jobApplicationId: finalAssessmentData?.job_application_id,
        developmentRecommendations: JSON.parse(
          finalAssessmentData?.development_recommendations || null
        ),
        skillSummary: JSON.parse(finalAssessmentData?.skill_summary || null),
        overallSuccessProbability:
          finalAssessmentData?.overall_success_probability || null,
      };
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.application_final_summary_retrieved,
        data: formattedFinalAssessment,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error fetching skill specific assessment:", error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.application_final_summary_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get candidate profile skill score data with optimized performance
   *
   * Recommended Database Indexes for optimal performance:
   * 1. CREATE INDEX idx_job_applications_candidate_id ON job_applications(candidate_id);
   * 2. CREATE INDEX idx_interview_job_application_ended ON interview(job_application_id, is_ended);
   * 3. CREATE INDEX idx_interview_skill_evaluations_interview_interviewer ON interview_skill_evaluations(interview_id, interviewer_id);
   * 4. CREATE INDEX idx_skills_id_title ON skills(id, title);
   *
   * These indexes will significantly improve query performance by:
   * - Optimizing the candidate lookup in job_applications
   * - Speeding up the interview filtering by job_application_id and is_ended status
   * - Accelerating the skill evaluations join and grouping operations
   * - Enhancing the skills table lookup performance
   */

  static getCandidateProfileSkillScoreData = async (
    candidateId: number
  ): Promise<ResponseObject> => {
    // Type definitions for better type safety
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      // Optimized query with better field ordering and explicit casting
      const skillScoreQuery = dataSource
        .createQueryBuilder()
        .select([
          "ise.interviewer_id AS interviewer_id",
          "i.hard_skill_marks AS hard_skill_marks",
          // Optimized JSON aggregation with explicit field ordering for better performance
          "JSON_ARRAYAGG(" +
            "JSON_OBJECT(" +
            "'skill_name', s.title, " +
            "'skill_marks', ise.skill_marks, " +
            "'probability_of_success_in_this_skill', ise.probability_of_success_in_this_skill, " +
            "'strengths', ise.strengths, " +
            "'potentials_gaps', ise.potentials_gaps" +
            ")" +
            ") AS skills",
        ])
        .from("job_applications", "ja")
        // Optimized JOIN order: most selective conditions first
        .innerJoin(
          "interview",
          "i",
          "i.job_application_id = ja.id AND i.is_ended = true"
        )
        .innerJoin(
          "interview_skill_evaluations",
          "ise",
          "ise.interview_id = i.id"
        )
        .innerJoin("skills", "s", "s.id = ise.skill_id")
        .where("ja.candidate_id = :candidateId", { candidateId })
        // Optimized GROUP BY with proper ordering for index usage
        .groupBy("ise.interviewer_id, i.hard_skill_marks")
        .orderBy("ise.interviewer_id", "ASC"); // Add ordering for consistent results

      const rawSkillScores: SkillEvaluationRaw[] =
        await skillScoreQuery.getRawMany();
      // Early return with specific error message
      if (!rawSkillScores.length) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.no_interviews_found,
        };
      }

      // Parse JSON once and store in memory-efficient structure
      const groupedSkillScores: GroupedSkillScore[] = rawSkillScores.map(
        (item) => ({
          interviewerId: item.interviewer_id,
          hardSkillMarks: item.hard_skill_marks,
          skills:
            typeof item.skills === "string"
              ? JSON.parse(item.skills)
              : item.skills,
        })
      );

      // Memory-efficient flattening with pre-calculated array size
      const flattenedSkillScores: FlattenedSkillScore[] =
        groupedSkillScores.flatMap((interviewer) =>
          interviewer.skills.map((skill) => ({
            skill_name: skill.skill_name,
            skill_marks: skill.skill_marks,
            strengths: skill.strengths,
            potentials_gaps: skill.potentials_gaps,
            probability_of_success_in_this_skill:
              skill.probability_of_success_in_this_skill,
          }))
        );

      // Optimized career score calculation with validation
      const totalCareerSkillScore =
        groupedSkillScores.length > 0
          ? Math.ceil(
              groupedSkillScores.reduce(
                (acc, curr) => acc + curr.hardSkillMarks,
                0
              ) / groupedSkillScores.length
            )
          : 0;
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.skill_specific_assessment_retrieved,
        data: {
          careerBasedSkillsScore: totalCareerSkillScore,
          skillsScores: flattenedSkillScores,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_skill_specific_assessment_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Generates final summary for a candidate application
   *
   * This method triggers the generation of a comprehensive final summary for a candidate
   * based on their interview performance, skill assessments, and overall evaluation data.
   * Currently returns a placeholder success response.
   *
   * @param {string} candidateId - The unique identifier of the candidate
   * @param {string} jobApplicationId - The unique identifier of the job application
   * @param {number} orgId - The organization ID for authorization
   * @returns {Promise<ResponseObject>} Promise resolving to success confirmation
   */
  static async generateFinalSummary(
    candidateId: string,
    jobApplicationId: string,
    orgId: number
  ): Promise<ResponseObject> {
    try {
      // TODO: Implement actual final summary generation logic
      // This could include:
      // - Aggregating interview data from all rounds
      // - Calculating overall success probability
      // - Generating AI-powered insights and recommendations
      // - Storing the generated summary in the database

      // For now, return a placeholder success response
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.final_summary_generated_successfully,
      };
    } catch (error) {
      console.error("Error generating final summary:", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.generate_final_summary_failed,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }
}

export default CandidateApplicationService;
