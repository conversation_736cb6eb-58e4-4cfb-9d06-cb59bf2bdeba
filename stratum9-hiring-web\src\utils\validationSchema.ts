import * as yup from "yup";

// Regex patterns
export const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
export const NAME_REGEX = /^[a-zA-Z0-9\s.'-]+$/;

// Name validation regex - only allows alphabetic characters (including Unicode letters) and spaces
// This pattern supports international names while rejecting digits and special characters
export const CANDIDATE_NAME_REGEX = /^[\p{L}\s]+$/u;

// Employee validation schema
export const employeeValidationSchema = (translation: (key: string) => string) =>
  yup.object().shape({
    firstName: yup
      .string()
      .trim()
      .required(translation("first_name_req"))
      .matches(NAME_REGEX, {
        message: translation("valid_name"),
        excludeEmptyString: true,
      })
      .min(1, translation("min_first_name"))
      .max(50, translation("max_first_name")),
    lastName: yup
      .string()
      .trim()
      .required(translation("last_name_req"))
      .matches(NAME_REGEX, {
        message: translation("valid_name"),
        excludeEmptyString: true,
      })
      .min(1, translation("min_last_name"))
      .max(50, translation("max_last_name")),
    email: yup
      .string()
      .trim()
      .required(translation("email_req"))
      .email(translation("email_val_msg"))
      .matches(EMAIL_REGEX, translation("email_val_msg")),
    department: yup
      .number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required(translation("department_req"))
      .min(1, "Department must be selected"),
    role: yup
      .number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required(translation("role_req"))
      .min(1, "Role must be selected"),
    // orderOfInterview: yup.string().required(translation("order_interview_req")),
  });

// Employee array validation schema
export const employeesValidationSchema = (translation: (key: string) => string) =>
  yup.object().shape({
    employees: yup.array().of(employeeValidationSchema(translation)).required("At least one employee is required"),
  });

// Department validation schema
export const departmentValidationSchema = (translation: (key: string) => string) =>
  yup.object().shape({
    name: yup
      .string()
      .trim()
      .required(translation("department_name_req"))
      .matches(NAME_REGEX, {
        message: translation("valid_name"),
        excludeEmptyString: true,
      })
      .min(2, translation("min_department_name"))
      .max(50, translation("max_department_name")),
  });

// Role validation schema
export const roleValidationSchema = (translation: (key: string) => string) =>
  yup.object().shape({
    name: yup
      .string()
      .trim()
      .required(translation("role_name_req"))
      .matches(NAME_REGEX, {
        message: translation("valid_name"),
        excludeEmptyString: true,
      })
      .min(2, translation("min_role_name"))
      .max(50, translation("max_role_name")),
  });
