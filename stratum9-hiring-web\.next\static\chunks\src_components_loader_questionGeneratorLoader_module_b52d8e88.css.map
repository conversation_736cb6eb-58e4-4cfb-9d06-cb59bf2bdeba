{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/loader/questionGeneratorLoader.module.css"], "sourcesContent": [".question_generator_loader_overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.loader_wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.loader_container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 60px;\r\n}\r\n\r\n.loader_text {\r\n  margin-top: 15px;\r\n  color: #fff;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  text-align: center;\r\n  width: 100%;\r\n  /* Fixed position to ensure stability */\r\n  position: relative;\r\n}\r\n\r\n.loader {\r\n  width: 8px;\r\n  height: 40px;\r\n  border-radius: 4px;\r\n  display: block;\r\n  margin: 20px auto;\r\n  position: relative;\r\n  background: currentColor;\r\n  color: #fff;\r\n  box-sizing: border-box;\r\n  animation: loaderAnim 0.3s 0.3s linear infinite alternate;\r\n}\r\n\r\n.loader::after,\r\n.loader::before {\r\n  content: \"\";\r\n  width: 8px;\r\n  height: 40px;\r\n  border-radius: 4px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  left: 20px;\r\n  box-sizing: border-box;\r\n  animation: loaderAnim 0.3s 0.45s linear infinite alternate;\r\n}\r\n\r\n.loader::before {\r\n  left: -20px;\r\n  animation-delay: 0s;\r\n}\r\n\r\n@keyframes loaderAnim {\r\n  0% {\r\n    height: 48px;\r\n  }\r\n  100% {\r\n    height: 4px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAWA;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;;AAeA;;;;;AAKA"}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}