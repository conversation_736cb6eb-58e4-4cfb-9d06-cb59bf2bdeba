{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\r\n\r\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\r\n\r\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  iconClass?: string;\r\n  align?: \"left\" | \"right\";\r\n  children?: React.ReactNode;\r\n}\r\n\r\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\r\n  name: Path<T>;\r\n  control: Control<T>;\r\n}\r\n\r\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\r\n  return (\r\n    <div className={`${iconClass} ${align}`}>\r\n      <Controller\r\n        control={control}\r\n        name={name}\r\n        render={({ field }) => (\r\n          <input\r\n            {...props}\r\n            value={field.value}\r\n            onChange={(e) => {\r\n              field.onChange(e);\r\n              props.onChange?.(e);\r\n            }}\r\n            aria-label=\"\"\r\n          />\r\n        )}\r\n        defaultValue={\"\" as T[typeof name]}\r\n      />\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\r\n  return (\r\n    <div className={`${iconClass} ${align}`}>\r\n      <input {...props} onChange={onChange} />\r\n\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC,8JAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\r\nimport Button from \"./Button\";\r\n\r\n/**\r\n * Wrapper component for input fields\r\n * @param {string} className - Class name for the input field\r\n * @returns {JSX.Element} - Wrapper component\r\n */\r\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\r\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\r\n);\r\n\r\n/**\r\n * Label component for input fields\r\n * @param {string} children - Label text\r\n * @returns {JSX.Element} - Label component\r\n */\r\nInputWrapper.Label = function ({\r\n  children,\r\n  htmlFor,\r\n  required,\r\n  className,\r\n  onClick,\r\n  style,\r\n}: {\r\n  children: ReactNode;\r\n  htmlFor?: string;\r\n  required?: boolean;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  style?: React.CSSProperties;\r\n  ref?: React.RefObject<HTMLInputElement>;\r\n}): JSX.Element {\r\n  return (\r\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\r\n      {children}\r\n      {required ? <sup>*</sup> : null}\r\n    </label>\r\n  );\r\n};\r\n\r\n/**\r\n * Error component for input fields to display error message\r\n * @param { string } message - Error message\r\n * @param { React.CSSProperties } style - Optional style object\r\n * @returns { JSX.Element } - Error component\r\n */\r\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\r\n  return message ? (\r\n    <p className=\"auth-msg error\" style={style}>\r\n      {message}\r\n    </p>\r\n  ) : null;\r\n};\r\n\r\n/**\r\n * Icon component for input fields\r\n * @param { string } src - Icon source\r\n * @param { function } onClick - Function to be called on click\r\n * @returns { JSX.Element } - Icon component\r\n */\r\nInputWrapper.Icon = function ({\r\n  children,\r\n  // src,\r\n  onClick,\r\n}: {\r\n  children: ReactNode;\r\n  // src: string;\r\n  onClick?: () => void;\r\n}): JSX.Element {\r\n  return (\r\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\r\n      {children}\r\n    </Button>\r\n  );\r\n};\r\n\r\nexport default InputWrapper;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/CandidateResumeIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction CandidateResumeIcon() {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"21\" viewBox=\"0 0 25 24\" fill=\"none\">\r\n      <path\r\n        d=\"M14.582 0.963867V0.964844C14.6601 0.963757 14.7375 0.977964 14.8096 1.00781L14.9131 1.06348C14.9452 1.08531 14.9748 1.11067 15.002 1.13867L20.6953 6.83105L20.7695 6.92188C20.8339 7.01865 20.8687 7.13309 20.8691 7.25098V20.8828C20.8691 21.4538 20.642 22.0015 20.2383 22.4053C19.8345 22.809 19.2868 23.0361 18.7158 23.0361H5.2832C4.78372 23.0361 4.302 22.8626 3.91895 22.5488L3.76074 22.4053C3.35708 22.0015 3.12988 21.4537 3.12988 20.8828V3.11719L3.14062 2.9043C3.18955 2.41143 3.40745 1.94802 3.76074 1.59473L3.91895 1.45215C4.30205 1.13825 4.78359 0.963867 5.2832 0.963867H14.582ZM5.28418 2.15332C5.02866 2.15342 4.78323 2.25486 4.60254 2.43555C4.42192 2.6163 4.32031 2.86165 4.32031 3.11719V20.8828C4.32031 21.1383 4.42203 21.3837 4.60254 21.5645L4.67383 21.6289C4.84525 21.7693 5.06065 21.8476 5.28418 21.8477H18.7168L18.8115 21.8428C18.9056 21.8336 18.9982 21.8106 19.0859 21.7744L19.1719 21.7334C19.2551 21.6889 19.3314 21.6323 19.3984 21.5654C19.488 21.476 19.5599 21.3699 19.6084 21.2529L19.6406 21.1631C19.6681 21.0726 19.6816 20.9777 19.6816 20.8828V7.8457H15.8818C15.4426 7.84567 15.0195 7.69293 14.6826 7.41699L14.543 7.29199C14.188 6.93701 13.9893 6.45515 13.9893 5.95312V2.15332H5.28418ZM15.1758 5.95312C15.1758 6.13981 15.2508 6.31916 15.3828 6.45117L15.4893 6.53906C15.6041 6.61572 15.7407 6.65723 15.8809 6.65723H18.8398L15.1758 2.99316V5.95312Z\"\r\n        fill=\"#436EB6\"\r\n        stroke=\"#436EB6\"\r\n        strokeWidth=\"0.15\"\r\n      />\r\n      <path\r\n        d=\"M9.22949 4.86719C9.58595 4.7495 9.96794 4.72524 10.3379 4.79883L10.4941 4.83594C10.8559 4.93613 11.1864 5.12881 11.4531 5.39551L11.5625 5.51367C11.8075 5.79789 11.9762 6.14094 12.0498 6.51074L12.0752 6.66992C12.1145 6.98934 12.0824 7.31354 11.9814 7.61914L11.9258 7.76953C11.7609 8.16767 11.4814 8.50863 11.123 8.74805C10.8095 8.95751 10.4475 9.08071 10.0732 9.1084L9.91211 9.11523C9.40667 9.11455 8.91972 8.93781 8.53223 8.62012L8.37207 8.47559C7.96386 8.06717 7.73411 7.513 7.7334 6.93555L7.73926 6.77441C7.76705 6.40043 7.89127 6.03894 8.10059 5.72559L8.19531 5.59473C8.42628 5.29901 8.73076 5.06714 9.0791 4.92285L9.22949 4.86719ZM9.91211 5.94629C9.64983 5.9468 9.39836 6.05086 9.21289 6.23633C9.0273 6.42191 8.92323 6.67406 8.92285 6.93652C8.92297 7.13211 8.98118 7.3237 9.08984 7.48633L9.17969 7.60059C9.27746 7.70838 9.39767 7.79439 9.5332 7.85059L9.67188 7.89648C9.81322 7.93189 9.96138 7.93585 10.1055 7.90723L10.2461 7.86816C10.3833 7.81905 10.5084 7.73966 10.6123 7.63574C10.7507 7.4973 10.8456 7.32093 10.8838 7.12891L10.9014 6.98438C10.9061 6.88718 10.8967 6.78971 10.873 6.69531L10.8271 6.55664C10.7523 6.37609 10.6253 6.22197 10.4629 6.11328C10.3001 6.00451 10.1079 5.94629 9.91211 5.94629Z\"\r\n        fill=\"#436EB6\"\r\n        stroke=\"#436EB6\"\r\n        strokeWidth=\"0.15\"\r\n      />\r\n      <path\r\n        d=\"M10.5127 9.19043L10.8135 9.20605C11.5087 9.27592 12.1627 9.58361 12.6611 10.082L12.8623 10.3047C13.3053 10.8452 13.5508 11.5245 13.5518 12.2295V12.5469C13.5517 12.6652 13.5169 12.78 13.4521 12.877L13.3779 12.9668C13.2664 13.0783 13.1147 13.1416 12.957 13.1416C12.839 13.1415 12.7247 13.1056 12.6279 13.041L12.5371 12.9668C12.4257 12.8554 12.3633 12.7044 12.3633 12.5469V12.2295L12.3535 12.0469C12.317 11.684 12.1746 11.3396 11.9434 11.0576L11.8203 10.9219C11.5168 10.6184 11.1187 10.4312 10.6953 10.3887L10.5127 10.3799H9.3125C8.88326 10.3806 8.46967 10.53 8.14062 10.7998L8.00488 10.9219C7.65812 11.2686 7.46365 11.7391 7.46289 12.2295V12.5469C7.46285 12.6653 7.42713 12.78 7.3623 12.877L7.28809 12.9668C7.17662 13.0782 7.02577 13.1416 6.86816 13.1416C6.74981 13.1416 6.63509 13.1058 6.53809 13.041L6.44727 12.9668C6.33605 12.8554 6.27349 12.7043 6.27344 12.5469V12.2295L6.28906 11.9297C6.35882 11.2342 6.66648 10.5806 7.16504 10.082L7.3877 9.87988C7.92816 9.43694 8.60761 9.19134 9.3125 9.19043H10.5127Z\"\r\n        fill=\"#436EB6\"\r\n        stroke=\"#436EB6\"\r\n        strokeWidth=\"0.15\"\r\n      />\r\n      <path\r\n        d=\"M12.8838 15.7529L13 15.7646C13.1144 15.7874 13.221 15.8431 13.3047 15.9268L13.3789 16.0176C13.4435 16.1145 13.4785 16.2294 13.4785 16.3477C13.4785 16.4659 13.4436 16.5808 13.3789 16.6777L13.3047 16.7676C13.1932 16.8791 13.0415 16.9424 12.8838 16.9424H6.58008C6.46169 16.9424 6.34702 16.9066 6.25 16.8418L6.15918 16.7676C6.04798 16.6561 5.98536 16.5051 5.98535 16.3477C5.98535 16.19 6.04769 16.0382 6.15918 15.9268L6.25 15.8525C6.34694 15.7879 6.46186 15.7529 6.58008 15.7529H12.8838Z\"\r\n        fill=\"#436EB6\"\r\n        stroke=\"#436EB6\"\r\n        strokeWidth=\"0.15\"\r\n      />\r\n      <path\r\n        d=\"M16.4502 18.4873C16.6076 18.4873 16.7587 18.5499 16.8701 18.6611L16.9443 18.752C17.0091 18.849 17.0449 18.9636 17.0449 19.082C17.0449 19.2004 17.0091 19.3151 16.9443 19.4121L16.8701 19.502C16.7586 19.6134 16.6078 19.6767 16.4502 19.6768H6.58008C6.46169 19.6768 6.34702 19.641 6.25 19.5762L6.15918 19.502C6.04798 19.3905 5.98536 19.2395 5.98535 19.082C5.98535 18.9244 6.04769 18.7726 6.15918 18.6611L6.25 18.5869C6.34694 18.5223 6.46186 18.4873 6.58008 18.4873H16.4502Z\"\r\n        fill=\"#436EB6\"\r\n        stroke=\"#436EB6\"\r\n        strokeWidth=\"0.15\"\r\n      />\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport default CandidateResumeIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BACtF,8OAAC;gBACC,GAAE;gBACF,MAAK;gBACL,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,MAAK;gBACL,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,MAAK;gBACL,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,MAAK;gBACL,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,MAAK;gBACL,QAAO;gBACP,aAAY;;;;;;;;;;;;AAIpB;uCAEe", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/QuestionnaireIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction QuestionnaireIcon() {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"22\" viewBox=\"0 0 25 24\" fill=\"none\">\r\n      <g clipPath=\"url(#clip0_9953_3370)\">\r\n        <path\r\n          d=\"M19.4716 4.95688C19.3586 4.95688 19.2478 4.91091 19.1679 4.831C19.088 4.75109 19.042 4.64025 19.042 4.52727C19.042 4.41428 19.088 4.30344 19.1679 4.22353C19.2478 4.14367 19.3586 4.09766 19.4716 4.09766C19.5846 4.09766 19.6954 4.14362 19.7753 4.22353C19.8552 4.30344 19.9012 4.41428 19.9012 4.52727C19.9012 4.64068 19.8552 4.75109 19.7753 4.831C19.6954 4.91091 19.5846 4.95688 19.4716 4.95688Z\"\r\n          fill=\"#436EB6\"\r\n          stroke=\"#436EB6\"\r\n          strokeWidth=\"0.275\"\r\n        />\r\n        <path\r\n          d=\"M11.9997 22.9999C9.71858 23 7.48943 22.293 5.63205 20.9721L2.79336 21.7328C2.64506 21.7725 2.48692 21.7301 2.37836 21.6216C2.26984 21.5131 2.22744 21.3548 2.26718 21.2066L3.0278 18.3679C1.55862 16.3019 0.848909 13.7756 1.0269 11.23C1.21167 8.58796 2.34601 6.10031 4.221 4.22528C7.97424 0.472079 13.8656 -0.0695298 18.2295 2.93752C18.4248 3.07216 18.4741 3.33964 18.3395 3.53502C18.2049 3.7305 17.9373 3.77969 17.7419 3.64505C13.7195 0.873206 8.28871 1.37276 4.82864 4.83283C1.30533 8.35614 0.880743 14.0278 3.84105 18.0257C3.91898 18.1309 3.94467 18.266 3.91078 18.3925L3.28974 20.7103L5.60744 20.0892C5.73391 20.0554 5.86902 20.081 5.97428 20.1589C9.97223 23.1192 15.6439 22.6947 19.1672 19.1713C22.6273 15.7112 23.1268 10.2803 20.3548 6.2578C20.2202 6.06242 20.2694 5.7949 20.4648 5.66026C20.6601 5.52566 20.9277 5.5749 21.0623 5.77024C24.0696 10.1341 23.5281 16.0257 19.7748 19.7789C17.8997 21.654 15.4121 22.7883 12.7701 22.9731C12.513 22.991 12.2559 22.9999 11.9997 22.9999Z\"\r\n          fill=\"#436EB6\"\r\n          stroke=\"#436EB6\"\r\n          strokeWidth=\"0.275\"\r\n        />\r\n        <path\r\n          d=\"M11.9259 15.3692C11.1775 15.3692 10.5715 14.7632 10.5715 14.0184C10.5715 12.6016 11.3649 11.3221 12.6421 10.6794C13.2065 10.3953 13.5524 9.803 13.5233 9.17036C13.4883 8.40883 12.7629 7.68348 12.0014 7.64842C11.5584 7.62832 11.138 7.78456 10.8186 8.08954C10.6282 8.27144 10.4898 8.48732 10.4071 8.73112C10.2196 9.28489 9.70782 9.66075 9.13361 9.66643V9.66647C9.1291 9.66651 9.12459 9.66651 9.12008 9.66651C8.68879 9.66651 8.28002 9.45944 8.02436 9.11068C7.76565 8.7578 7.69472 8.31823 7.82974 7.9046C8.04738 7.23789 8.43485 6.62539 8.95017 6.13319C9.809 5.31298 10.9364 4.89166 12.1257 4.94609C13.1679 4.99399 14.1698 5.44822 14.9467 6.22508C15.7235 7.00194 16.1777 8.00375 16.2256 9.04607C16.3037 10.7431 15.3745 12.3328 13.8582 13.0959C13.4995 13.2764 13.2766 13.6299 13.2766 14.0184C13.2767 14.7632 12.6708 15.3692 11.9259 15.3692ZM11.9229 6.7874C11.9622 6.7874 12.0015 6.7883 12.041 6.79015C13.2539 6.84591 14.3258 7.91783 14.3816 9.13083C14.4263 10.1016 13.8951 11.0107 13.0283 11.4469C12.0428 11.9428 11.4307 12.9282 11.4307 14.0183C11.4307 14.2894 11.6512 14.5099 11.9223 14.5099C12.1969 14.5099 12.4175 14.2894 12.4175 14.0183C12.4175 13.3032 12.8215 12.6556 13.4719 12.3283C14.6859 11.7173 15.4299 10.4444 15.3674 9.08551C15.3293 8.25769 14.9641 7.45759 14.3391 6.83264C13.7141 6.20764 12.914 5.84247 12.0863 5.80437C11.1341 5.76115 10.231 6.098 9.54363 6.75453C9.12467 7.15467 8.82283 7.63132 8.6466 8.17126C8.59849 8.31853 8.62431 8.47576 8.71732 8.60267C8.81278 8.73288 8.95923 8.80729 9.11977 8.80729C9.12154 8.80729 9.12295 8.80639 9.12506 8.80725C9.33458 8.80519 9.52279 8.6638 9.59333 8.45549C9.71904 8.0843 9.93754 7.74294 10.2251 7.46824C10.6875 7.02669 11.2865 6.7874 11.9229 6.7874Z\"\r\n          fill=\"#436EB6\"\r\n          stroke=\"#436EB6\"\r\n          strokeWidth=\"0.275\"\r\n        />\r\n        <path\r\n          d=\"M11.9249 19.0607C11.179 19.0607 10.5723 18.4539 10.5723 17.7081C10.5723 16.9622 11.179 16.3555 11.9249 16.3555C12.6707 16.3555 13.2775 16.9622 13.2775 17.7081C13.2775 18.4539 12.6707 19.0607 11.9249 19.0607ZM11.9249 17.2147C11.6528 17.2147 11.4315 17.436 11.4315 17.7081C11.4315 17.9801 11.6528 18.2015 11.9249 18.2015C12.197 18.2015 12.4183 17.9801 12.4183 17.7081C12.4183 17.436 12.197 17.2147 11.9249 17.2147Z\"\r\n          fill=\"#436EB6\"\r\n          stroke=\"#436EB6\"\r\n          strokeWidth=\"0.275\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_9953_3370\">\r\n          <rect width=\"24\" height=\"24\" fill=\"white\" transform=\"translate(0.529297)\" />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport default QuestionnaireIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BACtF,8OAAC;gBAAE,UAAS;;kCACV,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;;;;;;;;;;;;0BAGhB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;uCAEe", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/SearchIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction SearchIcon() {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\">\r\n      <g opacity=\"0.7\">\r\n        <path\r\n          d=\"M28.2109 18.8274C28.2109 20.6833 27.6605 22.4976 26.6295 24.0407C25.5984 25.5839 24.1329 26.7867 22.4182 27.497C20.7036 28.2072 18.8168 28.3931 16.9965 28.0311C15.1762 27.6691 13.5042 26.7755 12.1917 25.4632C10.8793 24.1509 9.9855 22.479 9.62331 20.6587C9.26111 18.8384 9.4468 16.9517 10.1569 15.237C10.867 13.5222 12.0696 12.0566 13.6127 11.0253C15.1557 9.99409 16.9699 9.44356 18.8259 9.44336C20.0583 9.44323 21.2786 9.68586 22.4173 10.1574C23.5559 10.6289 24.5905 11.3201 25.462 12.1915C26.3335 13.0629 27.0248 14.0974 27.4965 15.236C27.9681 16.3746 28.2109 17.595 28.2109 18.8274Z\"\r\n          stroke=\"#333333\"\r\n          strokeWidth=\"1.5\"\r\n          strokeLinecap=\"round\"\r\n          strokeLinejoin=\"round\"\r\n        />\r\n        <path d=\"M30.557 30.559L25.457 25.459\" stroke=\"#333333\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n      </g>\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport default SearchIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YAAE,SAAQ;;8BACT,8OAAC;oBACC,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEjB,8OAAC;oBAAK,GAAE;oBAA+B,QAAO;oBAAU,aAAY;oBAAM,eAAc;oBAAQ,gBAAe;;;;;;;;;;;;;;;;;AAIvH;uCAEe", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ModalCloseIcon.tsx"], "sourcesContent": ["const ModalCloseIcon = (props: { className?: string }) => {\r\n  const { className } = props;\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"41\" viewBox=\"0 0 40 41\" fill=\"none\" className={className}>\r\n      <circle cx=\"20.0003\" cy=\"20.5\" r=\"18.209\" fill=\"white\" />\r\n      <path\r\n        d=\"M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z\"\r\n        fill=\"#333333\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default ModalCloseIcon;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,iBAAiB,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAO,GAAE;gBAAS,MAAK;;;;;;0BAC/C,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/ResumeModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef } from \"react\";\r\n\r\nimport Button from \"../formElements/Button\";\r\n// import { useTranslations } from \"next-intl\";\r\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\r\nimport \"../../styles/eventModal.scss\";\r\n\r\ninterface ResumeModalProps {\r\n  isOpen: boolean; // Controls whether the modal is visible\r\n  onClose: () => void; // Function to close the modal\r\n  resumeLink?: string | null; // Optional link to the resume PDF\r\n}\r\n// 🔁 Static test link\r\n// const resumeLink = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\r\n\r\nconst ResumeModal: React.FC<ResumeModalProps> = ({ isOpen, onClose, resumeLink }) => {\r\n  // const t = useTranslations(\"common\");\r\n  // const [fileError, setFileError] = useState<string>(\"\");\r\n\r\n  // 🧠 Ref to detect clicks outside the modal\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n\r\n  // 🧩 Detect outside click and close modal\r\n  useEffect(() => {\r\n    const handleOutsideClick = (event: MouseEvent) => {\r\n      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener(\"mousedown\", handleOutsideClick);\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleOutsideClick);\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  // ✅ Prevent rendering if modal not open\r\n  if (!isOpen) return null;\r\n  return (\r\n    <div className=\"modal theme-modal show-modal modal-lg\">\r\n      <div className=\"modal-dialog modal-dialog-centered\" ref={modalRef}>\r\n        <div className=\"modal-content\">\r\n          {/* ✅ Header */}\r\n          <div className=\"modal-header\">\r\n            <h4 className=\"m-0\">Resume Preview</h4>\r\n            <Button className=\"modal-close-btn\" onClick={onClose} type=\"button\">\r\n              <ModalCloseIcon />\r\n            </Button>\r\n          </div>\r\n\r\n          {/* ✅ Resume preview area */}\r\n          {resumeLink && (\r\n            <div className=\"modal-body\" style={{ height: \"80vh\" }}>\r\n              <iframe src={resumeLink} className=\"w-100 h-100\" title=\"Resume Preview\" style={{ border: \"none\" }} />\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ResumeModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA,+CAA+C;AAC/C;AANA;;;;;;AAcA,sBAAsB;AACtB,sHAAsH;AAEtH,MAAM,cAA0C,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE;IAC9E,uCAAuC;IACvC,0DAA0D;IAE1D,4CAA4C;IAC5C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACxE;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,wCAAwC;IACxC,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;YAAqC,KAAK;sBACvD,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAM;;;;;;0CACpB,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;gCAAS,MAAK;0CACzD,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;oBAKlB,4BACC,8OAAC;wBAAI,WAAU;wBAAa,OAAO;4BAAE,QAAQ;wBAAO;kCAClD,cAAA,8OAAC;4BAAO,KAAK;4BAAY,WAAU;4BAAc,OAAM;4BAAiB,OAAO;gCAAE,QAAQ;4BAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9G;uCAEe", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/dsahboard/dashboardServies.ts"], "sourcesContent": ["import * as http from \"@/utils/http\";\r\nimport endpoint from \"@/constants/endpoint\";\r\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\r\n\r\nconst getDashboardCounts = (): Promise<ApiResponse> => {\r\n  return http.get(endpoint.Dashboard.GET_DASHBOARD_COUNTS);\r\n};\r\n\r\nexport default getDashboardCounts;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGA,MAAM,qBAAqB;IACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,oBAAoB;AACzD;uCAEe", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/interviewServices.ts"], "sourcesContent": ["import * as http from \"@/utils/http\";\r\nimport endpoint from \"@/constants/endpoint\";\r\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\r\nimport {\r\n  IAddInterviewSkillQuestion,\r\n  IGetCandidateListResponse,\r\n  IGetInterviewersResponse,\r\n  IGetInterviews,\r\n  IGetInterviewSkillQuestions,\r\n  IGetInterviewSkillQuestionsResponse,\r\n  IGetInterviewsResponse,\r\n  IGetJobListResponse,\r\n  IInterviewStaticInformation,\r\n  IScheduleInterview,\r\n  IUpcomingOrPastInterview,\r\n  IUpdateInterviewAnswers,\r\n  IUpdateInterviewSkillQuestion,\r\n  IUpdateScheduleInterview,\r\n} from \"@/interfaces/interviewInterfaces\";\r\n\r\nexport const updateOrScheduleInterview = (data: IScheduleInterview | IUpdateScheduleInterview): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.interview.UPDATE_OR_SCHEDULE_INTERVIEW, data);\r\n};\r\n\r\nexport const getInterviews = (data: IGetInterviews): Promise<ApiResponse<IGetInterviewsResponse[]>> => {\r\n  return http.get(endpoint.interview.GET_INTERVIEWS, data);\r\n};\r\n\r\nexport const getInterviewers = (searchString: string, jobId: string): Promise<ApiResponse<IGetInterviewersResponse[]>> => {\r\n  return http.get(endpoint.interview.GET_INTERVIEWERS, { searchString, jobId });\r\n};\r\n\r\nexport const upcomigOrPastInterview = (params: {\r\n  isPast: boolean;\r\n  limit?: number;\r\n  offset?: number;\r\n  searchStr?: string;\r\n}): Promise<ApiResponse<IUpcomingOrPastInterview[]>> => {\r\n  return http.get(endpoint.interview.GET_UPCOMING_OR_PAST_INTERVIEW, { ...params });\r\n};\r\n\r\nexport const getMyInterviews = (monthYear: string): Promise<ApiResponse<IGetInterviewsResponse[]>> => {\r\n  return http.get(endpoint.interview.GET_MY_INTERVIEWS, { monthYear });\r\n};\r\n\r\nexport const getInterviewSkillQuestions = (data: IGetInterviewSkillQuestions): Promise<ApiResponse<IGetInterviewSkillQuestionsResponse>> => {\r\n  return http.get(endpoint.interview.GET_INTERVIEW_SKILL_QUESTIONS, data);\r\n};\r\n\r\nexport const updateInterviewSkillQuestion = (data: IUpdateInterviewSkillQuestion): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.interview.UPDATE_INTERVIEW_SKILL_QUESTION, data);\r\n};\r\n\r\nexport const addInterviewSkillQuestion = (data: IAddInterviewSkillQuestion): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.interview.ADD_INTERVIEW_SKILL_QUESTION, data);\r\n};\r\n\r\nexport const getJobList = (searchString: string): Promise<ApiResponse<IGetJobListResponse[]>> => {\r\n  return http.get(endpoint.interview.GET_JOB_LIST, { searchString });\r\n};\r\n\r\nexport const getCandidateList = (data: { searchString: string; jobId: string }): Promise<ApiResponse<IGetCandidateListResponse[]>> => {\r\n  return http.get(endpoint.interview.GET_CANDIDATE_LIST, data);\r\n};\r\n\r\nexport const updateInterviewAnswers = (data: IUpdateInterviewAnswers): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.interview.UPDATE_INTERVIEW_ANSWERS, data);\r\n};\r\n\r\nexport const endInterview = (data: { interviewId: number; behaviouralNotes: string }): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.interview.END_INTERVIEW, data);\r\n};\r\n\r\nexport const conductInterviewStaticInformation = (): Promise<ApiResponse<IInterviewStaticInformation>> => {\r\n  return http.get(endpoint.interview.CONDUCT_INTERVIEW_STATIC_INFORMATION);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAmBO,MAAM,4BAA4B,CAAC;IACxC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE;AACpE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,cAAc,EAAE;AACrD;AAEO,MAAM,kBAAkB,CAAC,cAAsB;IACpD,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,gBAAgB,EAAE;QAAE;QAAc;IAAM;AAC7E;AAEO,MAAM,yBAAyB,CAAC;IAMrC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,8BAA8B,EAAE;QAAE,GAAG,MAAM;IAAC;AACjF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE;QAAE;IAAU;AACpE;AAEO,MAAM,6BAA6B,CAAC;IACzC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,6BAA6B,EAAE;AACpE;AAEO,MAAM,+BAA+B,CAAC;IAC3C,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,+BAA+B,EAAE;AACvE;AAEO,MAAM,4BAA4B,CAAC;IACxC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE;AACpE;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,YAAY,EAAE;QAAE;IAAa;AAClE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE;AACzD;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE;AAChE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,aAAa,EAAE;AACrD;AAEO,MAAM,oCAAoC;IAC/C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,oCAAoC;AACzE", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/screenResumeConstant.ts"], "sourcesContent": ["export const GENDER_OPTIONS = [\r\n  { value: \"Male\", label: \"Male\" },\r\n  { value: \"Female\", label: \"Female\" },\r\n];\r\n\r\nexport enum InterviewTabType {\r\n  UPCOMING = \"UpcomingInterviews\",\r\n  PAST = \"PastInterviews\",\r\n}\r\n\r\nexport const APPLICATION_UPDATE_STATUS = {\r\n  PROMOTED: \"Promoted\",\r\n  DEMOTED: \"Demoted\",\r\n};\r\n\r\nexport type APPLICATION_UPDATE_STATUS = (typeof APPLICATION_UPDATE_STATUS)[keyof typeof APPLICATION_UPDATE_STATUS];\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAEM,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,MAAM,4BAA4B;IACvC,UAAU;IACV,SAAS;AACX", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/dashboard-topcard.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 309, height: 187, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAsElEQVR42gGlAFr/AAAAAAADBAYXEBAZYSwvOypeZ3sggIunKrzR+2+20f+eAAAAAAAzKi5eiWZj9ZmeuZOmtttsYmN8vXiIrcusyv29AAYHCQR9bnJe36aV9K+32sqSnsTOnGVd/GxVYvWUrdvJAA8RG1p2dIDJxbC1+pis091peJ7plnJz/2BZb/52jr3YACEmPeNCSmb/oZaj/87Bwfy0t8f6VFp4/0RekP9ehcfuQFpS8DUx1gMAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2IAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkV,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/create-job.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 177, height: 154, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA8klEQVR42gHnABj/AC89UJkfMU10AgQGBwEAAAENDAsfBQYHGAAAAAAAAAAAAFVsjeU+ZKPkIzVQnxgZGVSGalu7ODc3sQMEBRIAAAAAABYiNlccJjpPOz9IuigoJ3bIlXzTkHVo2QkJCiEAAAAAAAAAAAATFBglU1BbkxkgLz+vj4fLd3eO3BgnQFoCAwUGAAAAAAAPGSo3NliRxD9indu3tMX+X3yx/jthoN4YKENXAAAAAAALEh0mJj5njC5DZoXDzeH4Z4e9/C5Keqg4W5fNAAAAAAAAAAEBAQECAhoiMDrQ0Nnxrqi5/j9hm9sqRHCYdFdC0OEqILAAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 7 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0a,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/screen-resumes.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 228, height: 150, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAsElEQVR42gGlAFr/AAAAAAABAQEBRERFSm5vcXSJh4moKCgpLwQEBAQAAAAAAAYICRMQDg0fTUZCe4CBgYjW19vsf39/hDEuLEwCAgMJABEVFygpLz5aYVRWhktGRE1paWptKCYlMH5uZb4WGBo6ABMaITc3VorEPFF6siQhJUYsIR4+UEdDY6usrdU7RVNpABMaID9MXnTlPERPxBsZGlMZExE0RkRDknN5fd01QlN8W/MtyQoSyT8AAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,wIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkV,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/conduct-interviews.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 187, height: 167, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA8klEQVR42gHnABj/AAYGBgU1NTUzFxcXFCQiJCUuLC0ufHx8c4eDg4oNCgobAAsLCx1lY2N5mZeWkcW9vdje2trm5+bl6Mm9vetPTFiBAAUFB1hLRU2liYB/gtrS0u308fH/7urp/9nT2v90ha3WABAQGElRU2y3ioWFd9na3Ob29vb/+PX0/9nY3/edq8XYABoNEGlPPUG1mZmZluTn5/D5+fj/9PLy/bW6xcSUn7O2AEAsLG92Y2S1zc3S7fLw8P729PT+6OTk/KClrr1vd4WVAFZCP3NtYF6Jcm1thHBsbHx0cHB8cG5ufDIyM2YZGhtOOC58ROyUFjsAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 7 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,4IAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0a,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/conductInterview/skeletons/PerformanceCardSkeleton.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Skeleton from \"react-loading-skeleton\";\r\nimport \"react-loading-skeleton/dist/skeleton.css\";\r\n\r\ninterface PerformanceCardSkeletonProps {\r\n  count?: number;\r\n}\r\n\r\nexport const PerformanceCardSkeleton = ({ count = 1 }: PerformanceCardSkeletonProps) => {\r\n  return (\r\n    <div className=\"row g-4\">\r\n      {Array.from({ length: count }).map((_, index) => (\r\n        <div key={index} className=\"col-md-4\">\r\n          <div className=\"w-100\">\r\n            <Skeleton height={168} width=\"100%\" borderRadius={24} />\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;;;;AAOO,MAAM,0BAA0B,CAAC,EAAE,QAAQ,CAAC,EAAgC;IACjF,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAAgB,WAAU;0BACzB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6JAAA,CAAA,UAAQ;wBAAC,QAAQ;wBAAK,OAAM;wBAAO,cAAc;;;;;;;;;;;eAF5C;;;;;;;;;;AAQlB", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/hooks/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\n/**\r\n * Custom hook that delays updating a value until after a specified delay\r\n * to prevent excessive function calls (e.g., API requests)\r\n *\r\n * @template T The type of value being debounced\r\n * @param {T} value The value to debounce\r\n * @param {number} delay The delay in milliseconds\r\n * @returns {T} The debounced value\r\n */\r\nexport function useDebounce<T>(value: T, delay: number): T {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n}\r\n\r\nexport default useDebounce;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAWO,SAAS,YAAe,KAAQ,EAAE,KAAa;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,kBAAkB;QACpB,GAAG;QAEH,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/dashboard/Dashboard.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useCallback, useEffect, useRef, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Skeleton from \"react-loading-skeleton\";\r\nimport dayjs from \"dayjs\";\r\n\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { useSelector } from \"react-redux\";\r\n\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nimport Textbox from \"@/components/formElements/Textbox\";\r\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\r\nimport CandidateResumeIcon from \"@/components/svgComponents/CandidateResumeIcon\";\r\nimport QuestionnaireIcon from \"@/components/svgComponents/QuestionnaireIcon\";\r\nimport SearchIcon from \"@/components/svgComponents/SearchIcon\";\r\nimport ResumeModal from \"@/components/commonModals/ResumeModal\";\r\n\r\nimport getDashboardCounts from \"@/services/dsahboard/dashboardServies\";\r\nimport { upcomigOrPastInterview } from \"@/services/interviewServices\";\r\n\r\nimport { AuthState } from \"@/redux/slices/authSlice\";\r\nimport { IDashboardStats } from \"@/interfaces/screenResumeInterfaces\";\r\nimport { InterviewTabType } from \"@/constants/screenResumeConstant\";\r\nimport { IUpcomingOrPastInterview } from \"@/interfaces/interviewInterfaces\";\r\nimport ROUTES from \"@/constants/routes\";\r\n\r\nimport dashboardImg from \"../../../../public/assets/images/dashboard-topcard.png\";\r\nimport crateJobImg from \"../../../../public/assets/images/create-job.png\";\r\nimport resumesImg from \"../../../../public/assets/images/screen-resumes.png\";\r\nimport conductImg from \"../../../../public/assets/images/conduct-interviews.png\";\r\n\r\nimport \"react-loading-skeleton/dist/skeleton.css\";\r\nimport style from \"@/styles/commonPage.module.scss\";\r\nimport styles from \"../../../styles/commonPage.module.scss\";\r\nimport { toastMessageError } from \"@/utils/helper\";\r\nimport { PerformanceCardSkeleton } from \"../conductInterview/skeletons/PerformanceCardSkeleton\";\r\nimport useDebounce from \"@/hooks/useDebounce\";\r\n\r\nfunction Dashboard() {\r\n  const router = useRouter();\r\n  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);\r\n  const t = useTranslations();\r\n  const { control, watch } = useForm();\r\n\r\n  const [selectedTab, setSelectedTab] = useState<InterviewTabType>(InterviewTabType.UPCOMING);\r\n  const [interviews, setInterviews] = useState<IUpcomingOrPastInterview[]>();\r\n  const [dashboardStats, setDashboardStats] = useState<IDashboardStats>({\r\n    totalJobs: 0,\r\n    activeJobs: 0,\r\n    candidatesHired: 0,\r\n    scheduledInterviews: 0,\r\n    resumeOnHold: 0,\r\n    upcomingInterviews: 0,\r\n  });\r\n\r\n  // Combine resumePreview and selectedResumeLink into one state\r\n  const [resumeModal, setResumeModal] = useState<{ isOpen: boolean; resumeLink: string | null }>({ isOpen: false, resumeLink: null });\r\n  const tCommon = useTranslations(\"common\");\r\n  const tDashboard = useTranslations(\"dashboard\");\r\n\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [interviewsLoading, setInterviewsLoading] = useState(false);\r\n  const initialFetchDone = useRef(false);\r\n\r\n  const searchStr = watch(\"search\");\r\n  const debouncedSearchStr = useDebounce(searchStr, 1000);\r\n\r\n  useEffect(() => {\r\n    if (initialFetchDone.current) return;\r\n\r\n    const fetchDashboardStats = async () => {\r\n      setIsLoading(true);\r\n      if (!userData?.orgId || !userData?.id) {\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const res = await getDashboardCounts();\r\n        // Handle nested API response structure\r\n        if (res.data && res.data.success) {\r\n          const counts = res.data.data;\r\n          setDashboardStats({\r\n            totalJobs: counts.totalJobs || 0,\r\n            activeJobs: counts.activeJobs || 0,\r\n            candidatesHired: counts.candidatesHired || 0,\r\n            scheduledInterviews: counts.scheduledInterviews || 0,\r\n            resumeOnHold: counts.onHoldApplications || 0,\r\n            upcomingInterviews: counts.upcomingInterviews || 0,\r\n          });\r\n        } else {\r\n          toastMessageError(t(res?.data?.message));\r\n        }\r\n      } catch {\r\n        toastMessageError(t(\"something_went_wrong\"));\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDashboardStats();\r\n    fetchInterviewData();\r\n    initialFetchDone.current = true;\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchInterviewData();\r\n  }, [selectedTab, debouncedSearchStr]);\r\n\r\n  const fetchInterviewData = useCallback(async () => {\r\n    setInterviewsLoading(true);\r\n    try {\r\n      const res = await upcomigOrPastInterview({\r\n        isPast: selectedTab === InterviewTabType.PAST,\r\n        searchStr: debouncedSearchStr || \"\",\r\n      });\r\n      if (res?.data?.success) {\r\n        setInterviews(res.data.data || []);\r\n      } else {\r\n        toastMessageError(t(res?.data?.message));\r\n      }\r\n    } catch {\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setInterviewsLoading(false);\r\n    }\r\n  }, [selectedTab, debouncedSearchStr]);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"container\">\r\n        <div className=\"common-page-header\">\r\n          <div className=\"common-page-head-section\">\r\n            <div className=\"main-heading\">\r\n              <h2>\r\n                {t(\"hiring_manager_dashboard\")} - <span>{tCommon(\"home\")}</span>\r\n              </h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"common-box\">\r\n          {/* <Sidebar /> */}\r\n          <main className=\"main-content\">\r\n            <div className={style.dashboard_page}>\r\n              <section className={styles.dashboard__stats}>\r\n                <div className={styles.dashboard__stat}>\r\n                  <span className={styles.dashboard__stat_label}>{tDashboard(\"jobs_created\")}</span>\r\n                  <span className={styles.dashboard__stat_value}>{isLoading ? <Skeleton width={30} height={30} /> : dashboardStats?.totalJobs}</span>\r\n                </div>\r\n                <div className={styles.dashboard__stat} style={{ cursor: \"pointer\" }} onClick={() => router.push(ROUTES.JOBS.ACTIVE_JOBS)}>\r\n                  <span className={styles.dashboard__stat_label}>{tDashboard(\"active_jobs\")}</span>\r\n                  <span className={styles.dashboard__stat_value}>{isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.activeJobs}</span>\r\n                </div>\r\n                <div className={styles.dashboard__stat}>\r\n                  <span className={styles.dashboard__stat_label}>{tDashboard(\"upcoming_interviews\")}</span>\r\n                  <span className={styles.dashboard__stat_value}>\r\n                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.upcomingInterviews}\r\n                  </span>\r\n                </div>\r\n                <div className={styles.dashboard__stat}>\r\n                  <span className={styles.dashboard__stat_label}>{tDashboard(\"candidates_hired\")}</span>\r\n                  <span className={styles.dashboard__stat_value}>\r\n                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.candidatesHired}\r\n                  </span>\r\n                </div>\r\n                <div className={styles.dashboard__stat}>\r\n                  <span className={styles.dashboard__stat_label}>{tDashboard(\"scheduled_interviews\")}</span>\r\n                  <span className={styles.dashboard__stat_value}>\r\n                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.scheduledInterviews}\r\n                  </span>\r\n                </div>\r\n                <div className={`${styles.dashboard__stat} ${styles.border_none}`}>\r\n                  <span className={styles.dashboard__stat_label}>{tDashboard(\"resume_on_hold\")}</span>\r\n                  <span className={styles.dashboard__stat_value}>\r\n                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.resumeOnHold}\r\n                  </span>\r\n                </div>\r\n                <div className={styles.dashboard__stat_design}>\r\n                  <Image src={dashboardImg} alt=\"dashboard-icon\" className={styles.dashboard__stat_image} />\r\n                </div>\r\n              </section>\r\n\r\n              <div className=\"row\">\r\n                <div className=\"col-md-4\">\r\n                  <div className=\"announcement-card\" onClick={() => router.push(ROUTES.JOBS.HIRING_TYPE)}>\r\n                    <div className=\"announcement-content\">\r\n                      <h3>\r\n                        {tDashboard(\"create\")} <br /> {tDashboard(\"a_new_job\")}\r\n                      </h3>\r\n                    </div>\r\n                    <div className=\"announcement-image\">\r\n                      <Image src={crateJobImg} alt=\"dashboard-icon\" className=\"announce-img\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"col-md-4\">\r\n                  <div className=\"announcement-card\" onClick={() => router.push(ROUTES.JOBS.ACTIVE_JOBS)}>\r\n                    <div className=\"announcement-content\">\r\n                      <h3>\r\n                        {tDashboard(\"screen\")} <br /> {tDashboard(\"resumes\")}\r\n                      </h3>\r\n                    </div>\r\n                    <div className=\"announcement-image\">\r\n                      <Image src={conductImg} alt=\"dashboard-icon\" className=\"announce-img\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"col-md-4\">\r\n                  <div className=\"announcement-card\">\r\n                    <div className=\"announcement-content\">\r\n                      <h3>\r\n                        {tDashboard(\"conduct\")} <br /> {tDashboard(\"interviews\")}\r\n                      </h3>\r\n                    </div>\r\n                    <div className=\"announcement-image\">\r\n                      <Image src={resumesImg} alt=\"dashboard-icon\" className=\"announce-img\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"col-md-12\">\r\n                <div className={styles.dashboard_inner_head}>\r\n                  <ul className={styles.header_tab}>\r\n                    <li\r\n                      className={selectedTab === InterviewTabType.UPCOMING ? styles.active : \"\"}\r\n                      onClick={() => setSelectedTab(InterviewTabType.UPCOMING)}\r\n                      style={{ cursor: \"pointer\" }}\r\n                    >\r\n                      {tDashboard(\"upcoming_interviews\")}\r\n                    </li>\r\n                    <li\r\n                      className={selectedTab === InterviewTabType.PAST ? styles.active : \"\"}\r\n                      onClick={() => setSelectedTab(InterviewTabType.PAST)}\r\n                      style={{ cursor: \"pointer\" }}\r\n                    >\r\n                      {tDashboard(\"past_interviews\")}\r\n                    </li>\r\n                  </ul>\r\n                  <div className={styles.search_box}>\r\n                    <InputWrapper className=\"mb-0 w-100\">\r\n                      <div className=\"icon-align right\">\r\n                        <Textbox\r\n                          className=\"form-control w-100\"\r\n                          control={control}\r\n                          name=\"search\"\r\n                          type=\"text\"\r\n                          placeholder={tDashboard(\"search_placeholder\")}\r\n                        >\r\n                          <InputWrapper.Icon>\r\n                            <SearchIcon />\r\n                          </InputWrapper.Icon>\r\n                        </Textbox>\r\n                      </div>\r\n                    </InputWrapper>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"row g-4 mb-5\">\r\n                {interviewsLoading ? (\r\n                  <PerformanceCardSkeleton count={6} />\r\n                ) : interviews && interviews.length > 0 ? (\r\n                  interviews.map((interview, idx) => (\r\n                    <div className=\"col-md-4\" key={idx}>\r\n                      <div className=\"candidate-card\">\r\n                        <h2>{interview.candidateName}</h2>\r\n                        <p className=\"title\">{interview.jobTitle}</p>\r\n                        <p className=\"title\">\r\n                          {dayjs(interview.startTime).format(\"MMM DD, YYYY\")} | {dayjs(interview.startTime).format(\"hh:mm A\")} -{\" \"}\r\n                          {dayjs(interview.endTime).format(\"hh:mm A\")}\r\n                        </p>\r\n\r\n                        <div className=\"actions\">\r\n                          <a\r\n                            style={{ cursor: \"pointer\" }}\r\n                            onClick={() => {\r\n                              setResumeModal({ isOpen: true, resumeLink: interview.resumeFile });\r\n                            }}\r\n                            aria-label=\"Candidate Resume\"\r\n                          >\r\n                            <CandidateResumeIcon />\r\n                            {tDashboard(\"candidate_resume\")}\r\n                          </a>\r\n                          <a\r\n                            href={\r\n                              selectedTab === InterviewTabType.UPCOMING\r\n                                ? `${ROUTES.INTERVIEW.PRE_INTERVIEW_QUESTIONS_OVERVIEW}?interviewId=${interview.interviewId}&jobApplicationId=${interview.jobApplicationId}&interviewType=${encodeURIComponent(interview.roundType!)}&resumeLink=${encodeURIComponent(interview.resumeFile!)}&isEnded=${interview.isEnded}&date=${encodeURIComponent(interview.startTime)}`\r\n                                : ROUTES.JOBS.ACTIVE_JOBS\r\n                            }\r\n                            aria-label={\r\n                              selectedTab === InterviewTabType.UPCOMING ? tDashboard(\"set_view_questionarie\") : tDashboard(\"view_your_summary\")\r\n                            }\r\n                          >\r\n                            <QuestionnaireIcon />\r\n                            {selectedTab === InterviewTabType.UPCOMING ? tDashboard(\"set_view_questionarie\") : tDashboard(\"view_your_summary\")}\r\n                          </a>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <div className=\"col-12 text-center\">\r\n                    <p>{tDashboard(\"no_interviews_found\")}</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </main>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Resume Modal */}\r\n      <ResumeModal\r\n        isOpen={resumeModal.isOpen}\r\n        onClose={() => setResumeModal({ ...resumeModal, isOpen: false })}\r\n        resumeLink={resumeModal.resumeLink}\r\n      />\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Dashboard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAIA;AAEA;AAEA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AAtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,wIAAA,CAAA,mBAAgB,CAAC,QAAQ;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACpE,WAAW;QACX,YAAY;QACZ,iBAAiB;QACjB,qBAAqB;QACrB,cAAc;QACd,oBAAoB;IACtB;IAEA,8DAA8D;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;QAAE,QAAQ;QAAO,YAAY;IAAK;IACjI,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,aAAa,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAEnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,MAAM,YAAY,MAAM;IACxB,MAAM,qBAAqB,CAAA,GAAA,2HAAA,CAAA,UAAW,AAAD,EAAE,WAAW;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,OAAO,EAAE;QAE9B,MAAM,sBAAsB;YAC1B,aAAa;YACb,IAAI,CAAC,UAAU,SAAS,CAAC,UAAU,IAAI;gBACrC,aAAa;gBACb;YACF;YAEA,IAAI;gBACF,MAAM,MAAM,MAAM,CAAA,GAAA,gJAAA,CAAA,UAAkB,AAAD;gBACnC,uCAAuC;gBACvC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChC,MAAM,SAAS,IAAI,IAAI,CAAC,IAAI;oBAC5B,kBAAkB;wBAChB,WAAW,OAAO,SAAS,IAAI;wBAC/B,YAAY,OAAO,UAAU,IAAI;wBACjC,iBAAiB,OAAO,eAAe,IAAI;wBAC3C,qBAAqB,OAAO,mBAAmB,IAAI;wBACnD,cAAc,OAAO,kBAAkB,IAAI;wBAC3C,oBAAoB,OAAO,kBAAkB,IAAI;oBACnD;gBACF,OAAO;oBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,KAAK,MAAM;gBACjC;YACF,EAAE,OAAM;gBACN,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACtB,SAAU;gBACR,aAAa;YACf;QACF;QAEA;QACA;QACA,iBAAiB,OAAO,GAAG;IAC7B,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;KAAmB;IAEpC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,qBAAqB;QACrB,IAAI;YACF,MAAM,MAAM,MAAM,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE;gBACvC,QAAQ,gBAAgB,wIAAA,CAAA,mBAAgB,CAAC,IAAI;gBAC7C,WAAW,sBAAsB;YACnC;YACA,IAAI,KAAK,MAAM,SAAS;gBACtB,cAAc,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;YACnC,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,KAAK,MAAM;YACjC;QACF,EAAE,OAAM;YACN,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,qBAAqB;QACvB;IACF,GAAG;QAAC;QAAa;KAAmB;IAEpC,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;wCACE,EAAE;wCAA4B;sDAAG,8OAAC;sDAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKzD,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,cAAc;;kDAClC,8OAAC;wCAAQ,WAAW,yJAAA,CAAA,UAAM,CAAC,gBAAgB;;0DACzC,8OAAC;gDAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;kEACpC,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,WAAW;;;;;;kEAC3D,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,0BAAY,8OAAC,6JAAA,CAAA,UAAQ;4DAAC,OAAO;4DAAI,QAAQ;;;;;mEAAS,gBAAgB;;;;;;;;;;;;0DAEpH,8OAAC;gDAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;gDAAE,OAAO;oDAAE,QAAQ;gDAAU;gDAAG,SAAS,IAAM,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;;kEACtH,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,WAAW;;;;;;kEAC3D,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,0BAAY,8OAAC,6JAAA,CAAA,UAAQ;4DAAC,OAAO;4DAAI,QAAQ;;;;;mEAAS,eAAe,UAAU;;;;;;;;;;;;0DAE7H,8OAAC;gDAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;kEACpC,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,WAAW;;;;;;kEAC3D,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAC1C,0BAAY,8OAAC,6JAAA,CAAA,UAAQ;4DAAC,OAAO;4DAAI,QAAQ;;;;;mEAAS,eAAe,kBAAkB;;;;;;;;;;;;0DAGxF,8OAAC;gDAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;kEACpC,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,WAAW;;;;;;kEAC3D,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAC1C,0BAAY,8OAAC,6JAAA,CAAA,UAAQ;4DAAC,OAAO;4DAAI,QAAQ;;;;;mEAAS,eAAe,eAAe;;;;;;;;;;;;0DAGrF,8OAAC;gDAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;kEACpC,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,WAAW;;;;;;kEAC3D,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAC1C,0BAAY,8OAAC,6JAAA,CAAA,UAAQ;4DAAC,OAAO;4DAAI,QAAQ;;;;;mEAAS,eAAe,mBAAmB;;;;;;;;;;;;0DAGzF,8OAAC;gDAAI,WAAW,GAAG,yJAAA,CAAA,UAAM,CAAC,eAAe,CAAC,CAAC,EAAE,yJAAA,CAAA,UAAM,CAAC,WAAW,EAAE;;kEAC/D,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAAG,WAAW;;;;;;kEAC3D,8OAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;kEAC1C,0BAAY,8OAAC,6JAAA,CAAA,UAAQ;4DAAC,OAAO;4DAAI,QAAQ;;;;;mEAAS,eAAe,YAAY;;;;;;;;;;;;0DAGlF,8OAAC;gDAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,sBAAsB;0DAC3C,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDAAC,KAAK,oUAAA,CAAA,UAAY;oDAAE,KAAI;oDAAiB,WAAW,yJAAA,CAAA,UAAM,CAAC,qBAAqB;;;;;;;;;;;;;;;;;kDAI1F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAoB,SAAS,IAAM,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;;sEACnF,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;oEACE,WAAW;oEAAU;kFAAC,8OAAC;;;;;oEAAK;oEAAE,WAAW;;;;;;;;;;;;sEAG9C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEAAC,KAAK,sTAAA,CAAA,UAAW;gEAAE,KAAI;gEAAiB,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAI9D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAoB,SAAS,IAAM,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;;sEACnF,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;oEACE,WAAW;oEAAU;kFAAC,8OAAC;;;;;oEAAK;oEAAE,WAAW;;;;;;;;;;;;sEAG9C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEAAC,KAAK,sUAAA,CAAA,UAAU;gEAAE,KAAI;gEAAiB,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAI7D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;oEACE,WAAW;oEAAW;kFAAC,8OAAC;;;;;oEAAK;oEAAE,WAAW;;;;;;;;;;;;sEAG/C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEAAC,KAAK,8TAAA,CAAA,UAAU;gEAAE,KAAI;gEAAiB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM/D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,oBAAoB;;8DACzC,8OAAC;oDAAG,WAAW,yJAAA,CAAA,UAAM,CAAC,UAAU;;sEAC9B,8OAAC;4DACC,WAAW,gBAAgB,wIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,yJAAA,CAAA,UAAM,CAAC,MAAM,GAAG;4DACvE,SAAS,IAAM,eAAe,wIAAA,CAAA,mBAAgB,CAAC,QAAQ;4DACvD,OAAO;gEAAE,QAAQ;4DAAU;sEAE1B,WAAW;;;;;;sEAEd,8OAAC;4DACC,WAAW,gBAAgB,wIAAA,CAAA,mBAAgB,CAAC,IAAI,GAAG,yJAAA,CAAA,UAAM,CAAC,MAAM,GAAG;4DACnE,SAAS,IAAM,eAAe,wIAAA,CAAA,mBAAgB,CAAC,IAAI;4DACnD,OAAO;gEAAE,QAAQ;4DAAU;sEAE1B,WAAW;;;;;;;;;;;;8DAGhB,8OAAC;oDAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,UAAU;8DAC/B,cAAA,8OAAC,kJAAA,CAAA,UAAY;wDAAC,WAAU;kEACtB,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6IAAA,CAAA,UAAO;gEACN,WAAU;gEACV,SAAS;gEACT,MAAK;gEACL,MAAK;gEACL,aAAa,WAAW;0EAExB,cAAA,8OAAC,kJAAA,CAAA,UAAY,CAAC,IAAI;8EAChB,cAAA,8OAAC,iJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASzB,8OAAC;wCAAI,WAAU;kDACZ,kCACC,8OAAC,uLAAA,CAAA,0BAAuB;4CAAC,OAAO;;;;;mDAC9B,cAAc,WAAW,MAAM,GAAG,IACpC,WAAW,GAAG,CAAC,CAAC,WAAW,oBACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAI,UAAU,aAAa;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAS,UAAU,QAAQ;;;;;;sEACxC,8OAAC;4DAAE,WAAU;;gEACV,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,UAAU,SAAS,EAAE,MAAM,CAAC;gEAAgB;gEAAI,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,UAAU,SAAS,EAAE,MAAM,CAAC;gEAAW;gEAAG;gEACtG,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,UAAU,OAAO,EAAE,MAAM,CAAC;;;;;;;sEAGnC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,OAAO;wEAAE,QAAQ;oEAAU;oEAC3B,SAAS;wEACP,eAAe;4EAAE,QAAQ;4EAAM,YAAY,UAAU,UAAU;wEAAC;oEAClE;oEACA,cAAW;;sFAEX,8OAAC,0JAAA,CAAA,UAAmB;;;;;wEACnB,WAAW;;;;;;;8EAEd,8OAAC;oEACC,MACE,gBAAgB,wIAAA,CAAA,mBAAgB,CAAC,QAAQ,GACrC,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,gCAAgC,CAAC,aAAa,EAAE,UAAU,WAAW,CAAC,kBAAkB,EAAE,UAAU,gBAAgB,CAAC,eAAe,EAAE,mBAAmB,UAAU,SAAS,EAAG,YAAY,EAAE,mBAAmB,UAAU,UAAU,EAAG,SAAS,EAAE,UAAU,OAAO,CAAC,MAAM,EAAE,mBAAmB,UAAU,SAAS,GAAG,GACzU,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;oEAE7B,cACE,gBAAgB,wIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,WAAW,2BAA2B,WAAW;;sFAG/F,8OAAC,wJAAA,CAAA,UAAiB;;;;;wEACjB,gBAAgB,wIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,WAAW,2BAA2B,WAAW;;;;;;;;;;;;;;;;;;;+CA/BvE;;;;sEAsCjC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7B,8OAAC,iJAAA,CAAA,UAAW;gBACV,QAAQ,YAAY,MAAM;gBAC1B,SAAS,IAAM,eAAe;wBAAE,GAAG,WAAW;wBAAE,QAAQ;oBAAM;gBAC9D,YAAY,YAAY,UAAU;;;;;;;;AAI1C;uCAEe", "debugId": null}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}