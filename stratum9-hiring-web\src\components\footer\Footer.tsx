import Image from "next/image";

import Logo from "@/../public/assets/images/logo.svg";
import AppStore from "@/../public/assets/images/app-store.png";
import PlayStore from "@/../public/assets/images/play-store.png";
import Link from "next/link";
import Facebook from "../svgComponents/Facebook";
import XIcon from "../svgComponents/XIcon";
import LinkIn from "../svgComponents/LinkIn";
import Instagram from "../svgComponents/Instagram";
import style from "@/styles/footer.module.scss";
const Footer = () => {
  return (
    <>
      <footer className={style.footer}>
        <div className="container">
          <div className={style.footer_top}>
            <div className="row row-center">
              <div className="col-md-4 col-lg-6">
                <div className={style.footer_left}>
                  <Image src={Logo} alt="logo" className={style.logo_footer} />
                  <div className={style.social_media}>
                    <Link href="https://www.facebook.com/profile.php?id=100078198215999&mibextid=b06tZ0" target="_blank" aria-label="Facebook">
                      <Facebook />
                    </Link>
                    <Link href="https://twitter.com/9thStratum" target="_blank" aria-label="Twitter">
                      <XIcon />
                    </Link>
                    <Link href="https://www.linkedin.com/in/aaronsalko/" target="_blank" aria-label="Linkedin">
                      <LinkIn />
                    </Link>
                    <Link href="https://www.instagram.com/9thstratum/" target="_blank" aria-label="Instagram">
                      <Instagram />
                    </Link>
                  </div>
                </div>
              </div>
              <div className="col-md-4 offset-md-4 col-lg-3 offset-lg-3">
                <div className={style.footer_right}>
                  <p className={style.links_heading}>Download the app now</p>
                  <div className={style.app_links}>
                    <Link href="https://play.google.com/store/apps/details?id=com.stratum9&pli=1" target="_blank">
                      <Image src={PlayStore} alt="app screen" className="img-fluid" />
                    </Link>
                    <Link
                      href="https://apps.apple.com/us/app/stratum-9/id6478380143
 "
                      target="_blank"
                    >
                      <Image src={AppStore} alt="app screen" className="img-fluid" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className={style.footer_bottom}>
            <p>{`${new Date().getFullYear()} © Stratum9 ${"all_right_reserve"}`}</p>
            <div className={style.terms_policy}>
              <Link href={"/privacy-policy"} target="_blank">
                Privacy Policy
              </Link>
              |
              <Link href={"/terms-and-conditions"} target="_blank">
                Terms and Conditions
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
