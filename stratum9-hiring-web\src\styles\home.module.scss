@use "./abstracts" as *;

.home_page {
  section {
    padding: 60px 0;
  }
  .page_heading {
    h2 {
      font-size: $heading-md;
      font-weight: $bold;
      color: $dark;
      margin-bottom: 20px;
      span {
        color: $primary;
      }
    }
    p {
      font-size: $text-lg;
      font-weight: $medium;
      color: $dark;
    }
  }

  .hero_section {
    padding: 150px 0 100px;
    background-image: url("../../public/assets/images/home-bg.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

    .banner_content {
      h1 {
        font-family: "Plus Jakarta Sans";
        font-size: 66px;
        font-weight: 800;
        color: $white;
        margin-bottom: 30px;
        span {
          background: linear-gradient(63deg, #74a8ff 31.35%, #aacaff 42.53%, #5d86cc 54.92%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: block;
        }
      }
      p {
        font-size: $text-lg;
        font-weight: $medium;
        color: $white;
        margin-bottom: 50px;
      }
      button {
        position: relative;
      }
    }
    .banner_image {
      position: relative;
      padding-left: 40px;
      img {
        width: 100%;
        height: auto;
        z-index: 2;
        position: relative;
      }
    }
  }
  .top_brand_section {
    padding: 90px 0;
    .top_brand_image {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 633px;
        opacity: 0.8;
        z-index: 1;
        background: linear-gradient(54deg, #74a8ff 20.92%, #aacaff 52.91%, #5d86cc 88.37%);
        filter: blur(148.4499969482422px);
      }

      img {
        width: 100%;
        height: auto;
        z-index: 2;
        position: relative;
      }
    }
  }

  .advantage_card {
    border-radius: 32px;
    border: 1px solid #d9d9d9;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    min-height: 570px;
    height: 570px;
    justify-content: space-between;
    &::after {
      content: "";
      position: absolute;
      bottom: -270px;
      left: -210px;
      width: 400px;
      height: 400px;
      border-radius: 100%;
      background: radial-gradient(50% 50%, #aacaff 0%, #436eb6 100%);
      filter: blur(256.05px);
      z-index: -1;
    }

    .card_body {
      padding: 30px;
      h3 {
        font-size: $text-xxl;
        font-weight: $bold;
        color: $dark;
        margin-bottom: 25px;
      }
      p {
        font-size: $text-md;
        font-weight: $medium;
        color: $dark;
      }
    }

    .card_image {
      padding-left: 30px;
      width: 100%;
      object-fit: cover;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      margin-top: 10px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        margin-bottom: -1px;
        margin-right: -1px;
        object-position: left top;
      }
    }
  }
}
