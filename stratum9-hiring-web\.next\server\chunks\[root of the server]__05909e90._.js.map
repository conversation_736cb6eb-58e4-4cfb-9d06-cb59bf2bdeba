{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/endpoint.ts"], "sourcesContent": ["// import config from \"@/config/config\";\r\n\r\nconst URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst endpoint = {\r\n  auth: {\r\n    SIGNIN: `${URL}/auth/sign-in`,\r\n    VERIFY_OTP: `${URL}/auth/verify-otp`,\r\n    RESEND_OTP: `${URL}/auth/resend-otp`,\r\n    FORGOT_PASSWORD: `${URL}/auth/forgot-password`,\r\n    RESET_PASSWORD: `${URL}/auth/reset-password`,\r\n    DELETE_SESSION: `${URL}/auth/delete-session`,\r\n    UPDATE_TIMEZONE: `${URL}/auth/update-timezone`,\r\n  },\r\n  interview: {\r\n    UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,\r\n    GET_INTERVIEWS: `${URL}/interview/get-interviews`,\r\n    GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,\r\n    GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,\r\n    UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,\r\n\r\n    GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,\r\n    GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,\r\n    UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,\r\n    ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,\r\n    GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,\r\n\r\n    GET_JOB_LIST: `${URL}/interview/get-job-list`,\r\n    GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,\r\n    END_INTERVIEW: `${URL}/interview/end-interview`,\r\n    CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`,\r\n  },\r\n  common: {\r\n    REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,\r\n    GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`,\r\n  },\r\n  jobRequirements: {\r\n    GENERATE_SKILL: `${URL}/jobs/generate-skills`,\r\n    UPLOAD_URL: `${URL}/jobs/upload-url`,\r\n    PARSE_PDF: `${URL}/jobs/parse-pdf`,\r\n    GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,\r\n    GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,\r\n    SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,\r\n    GET_JOBS_META: `${URL}/jobs/get-jobs-meta`, // <-- Full URL here\r\n    UPDATE_JOB_STATUS: \"/jobs/updateJob\",\r\n    GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,\r\n    UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,\r\n    GENERATE_PDF: `${URL}/jobs/generate-pdf`,\r\n  },\r\n  Dashboard: {\r\n    GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`,\r\n  },\r\n  resumeScreen: {\r\n    MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,\r\n    GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,\r\n    GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,\r\n    CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`,\r\n  },\r\n  employee: {\r\n    ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,\r\n    GET_EMPLOYEES: `${URL}/employee-management`,\r\n    GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,\r\n    UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,\r\n    UPDATE_EMPLOYEE_STATUS: `${URL}/employee-management/employee/change-status/:employeeId`,\r\n    // this task is for future use\r\n    // DELETE_EMPLOYEE: `${URL}/employee-management/employee/:employeeId`, // original\r\n    DELETE_EMPLOYEE: `${URL}/employee-management/dummy`, //dummy\r\n\r\n    UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`,\r\n  },\r\n  userprofile: {\r\n    GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,\r\n    UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`,\r\n  },\r\n\r\n  roles: {\r\n    GET_ROLES_WITH_PAGINATION: `${URL}/access-management/user-roles-pagination`,\r\n    GET_ROLES: `${URL}/access-management/user-roles`,\r\n    ADD_USER_ROLE: `${URL}/access-management/add-user-role`,\r\n    UPDATE_USER_ROLE: `${URL}/access-management/user-role`,\r\n    DELETE_USER_ROLE: `${URL}/access-management/user-role`,\r\n    GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,\r\n    GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,\r\n    UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,\r\n    USER_PERMISSIONS: `${URL}/access-management/user-permissions`,\r\n  },\r\n  notification: {\r\n    UPDATE_NOTIFICATION: `${URL}/notifications/mark-as-watched`,\r\n    GET_NOTIFICATIONS: `${URL}/notifications/get-notifications`,\r\n    DELETE_ALL_NOTIFICATIONS: `${URL}/notifications/delete-users-all-notifications`,\r\n    GET_UNREAD_NOTIFICATIONS_COUNT: `${URL}/notifications/get-unread-notifications-count`,\r\n  },\r\n\r\n  departments: {\r\n    GET_DEPARTMENTS: `${URL}/employee-management/departments`,\r\n    ADD_DEPARTMENT: `${URL}/employee-management/add-department`,\r\n    UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,\r\n    DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`,\r\n  },\r\n\r\n  assessment: {\r\n    CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,\r\n    GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,\r\n    CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,\r\n    SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,\r\n    SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,\r\n    GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,\r\n    SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,\r\n    GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,\r\n    VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,\r\n    GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`,\r\n  },\r\n  candidatesApplication: {\r\n    ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,\r\n    PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`, // Base URL for candidates-related endpoints\r\n    GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,\r\n    GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,\r\n    ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,\r\n    GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`, // New endpoint for individual candidate details\r\n    UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`, // Endpoint for updating job application status\r\n    GET_CANDIDATE_INTERVIEW_HISTORY: `${URL}/candidates/get-candidate-interview-history/:candidateId/:applicationId`,\r\n    GET_APPLICATION_FINAL_SUMMARY: `${URL}/candidates/application-final-summary/:candidateId`,\r\n    GET_APPLICATION_SKILL_SCORE_DATA: `${URL}/candidates/application-skill-score-data/:candidateId`,\r\n    GENERATE_FINAL_SUMMARY: `${URL}/candidates/generate-final-summary`, // Endpoint for generating final summary\r\n  },\r\n  subscription: {\r\n    GET_ALL_PLANS: `${URL}/subscription/all`,\r\n    GET_CURRENT_SUBSCRIPTION: `${URL}/subscription/current`,\r\n    CANCEL_SUBSCRIPTION: `${URL}/subscription/cancel`,\r\n    GET_TRANSACTIONS: `${URL}/subscription/transactions`,\r\n    BUY_SUBSCRIPTION: `${URL}/subscription/buy-subscription`,\r\n  },\r\n};\r\n\r\nexport default endpoint;\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;AAExC,MAAM;AAEN,MAAM,WAAW;IACf,MAAM;QACJ,QAAQ,GAAG,IAAI,aAAa,CAAC;QAC7B,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;QAC9C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;IAChD;IACA,WAAW;QACT,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,gBAAgB,GAAG,IAAI,yBAAyB,CAAC;QACjD,kBAAkB,GAAG,IAAI,2BAA2B,CAAC;QACrD,mBAAmB,GAAG,IAAI,4BAA4B,CAAC;QACvD,0BAA0B,GAAG,IAAI,mCAAmC,CAAC;QAErE,gCAAgC,GAAG,IAAI,0CAA0C,CAAC;QAClF,+BAA+B,GAAG,IAAI,wCAAwC,CAAC;QAC/E,iCAAiC,GAAG,IAAI,0CAA0C,CAAC;QACnF,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,sBAAsB,GAAG,IAAI,+BAA+B,CAAC;QAE7D,cAAc,GAAG,IAAI,uBAAuB,CAAC;QAC7C,oBAAoB,GAAG,IAAI,6BAA6B,CAAC;QACzD,eAAe,GAAG,IAAI,wBAAwB,CAAC;QAC/C,sCAAsC,GAAG,IAAI,+CAA+C,CAAC;IAC/F;IACA,QAAQ;QACN,4BAA4B,GAAG,IAAI,2BAA2B,CAAC;QAC/D,wBAAwB,GAAG,IAAI,sBAAsB,CAAC;IACxD;IACA,iBAAiB;QACf,gBAAgB,GAAG,IAAI,qBAAqB,CAAC;QAC7C,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,WAAW,GAAG,IAAI,eAAe,CAAC;QAClC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,kBAAkB,GAAG,IAAI,sBAAsB,CAAC;QAChD,eAAe,GAAG,IAAI,mBAAmB,CAAC;QAC1C,mBAAmB;QACnB,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,wBAAwB,GAAG,IAAI,4BAA4B,CAAC;QAC5D,cAAc,GAAG,IAAI,kBAAkB,CAAC;IAC1C;IACA,WAAW;QACT,sBAAsB,GAAG,IAAI,sBAAsB,CAAC;IACtD;IACA,cAAc;QACZ,yBAAyB,GAAG,IAAI,sCAAsC,CAAC;QACvE,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,kCAAkC,GAAG,IAAI,+CAA+C,CAAC;QACzF,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;IAC7E;IACA,UAAU;QACR,eAAe,GAAG,IAAI,wCAAwC,CAAC;QAC/D,eAAe,GAAG,IAAI,oBAAoB,CAAC;QAC3C,6BAA6B,GAAG,IAAI,8BAA8B,CAAC;QACnE,sBAAsB,GAAG,IAAI,8CAA8C,CAAC;QAC5E,wBAAwB,GAAG,IAAI,uDAAuD,CAAC;QACvF,8BAA8B;QAC9B,kFAAkF;QAClF,iBAAiB,GAAG,IAAI,0BAA0B,CAAC;QAEnD,iCAAiC,GAAG,IAAI,yDAAyD,CAAC;IACpG;IACA,aAAa;QACX,gBAAgB,GAAG,IAAI,4BAA4B,CAAC;QACpD,mBAAmB,GAAG,IAAI,+BAA+B,CAAC;IAC5D;IAEA,OAAO;QACL,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;QAC3E,WAAW,GAAG,IAAI,6BAA6B,CAAC;QAChD,eAAe,GAAG,IAAI,gCAAgC,CAAC;QACvD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,sBAAsB,GAAG,IAAI,mCAAmC,CAAC;QACjE,4BAA4B,GAAG,IAAI,2CAA2C,CAAC;QAC/E,yBAAyB,GAAG,IAAI,2CAA2C,CAAC;QAC5E,kBAAkB,GAAG,IAAI,mCAAmC,CAAC;IAC/D;IACA,cAAc;QACZ,qBAAqB,GAAG,IAAI,8BAA8B,CAAC;QAC3D,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,0BAA0B,GAAG,IAAI,6CAA6C,CAAC;QAC/E,gCAAgC,GAAG,IAAI,6CAA6C,CAAC;IACvF;IAEA,aAAa;QACX,iBAAiB,GAAG,IAAI,gCAAgC,CAAC;QACzD,gBAAgB,GAAG,IAAI,mCAAmC,CAAC;QAC3D,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;QAC/E,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;IACjF;IAEA,YAAY;QACV,yBAAyB,GAAG,IAAI,yCAAyC,CAAC;QAC1E,gCAAgC,GAAG,IAAI,sCAAsC,CAAC;QAC9E,4BAA4B,GAAG,IAAI,4CAA4C,CAAC;QAChF,0BAA0B,GAAG,IAAI,+CAA+C,CAAC;QACjF,kBAAkB,GAAG,IAAI,kCAAkC,CAAC;QAC5D,mCAAmC,GAAG,IAAI,sCAAsC,CAAC;QACjF,mBAAmB,GAAG,IAAI,6CAA6C,CAAC;QACxE,uBAAuB,GAAG,IAAI,mCAAmC,CAAC;QAClE,wBAAwB,GAAG,IAAI,wCAAwC,CAAC;QACxE,2BAA2B,GAAG,IAAI,2CAA2C,CAAC;IAChF;IACA,uBAAuB;QACrB,iBAAiB,GAAG,IAAI,yCAAyC,CAAC;QAClE,0BAA0B,GAAG,IAAI,wCAAwC,CAAC;QAC1E,sCAAsC,GAAG,IAAI,0BAA0B,CAAC;QACxE,kCAAkC,GAAG,IAAI,0BAA0B,CAAC;QACpE,4BAA4B,GAAG,IAAI,qDAAqD,CAAC;QACzF,uBAAuB,GAAG,IAAI,iCAAiC,CAAC;QAChE,+BAA+B,GAAG,IAAI,2DAA2D,CAAC;QAClG,iCAAiC,GAAG,IAAI,uEAAuE,CAAC;QAChH,+BAA+B,GAAG,IAAI,kDAAkD,CAAC;QACzF,kCAAkC,GAAG,IAAI,qDAAqD,CAAC;QAC/F,wBAAwB,GAAG,IAAI,kCAAkC,CAAC;IACpE;IACA,cAAc;QACZ,eAAe,GAAG,IAAI,iBAAiB,CAAC;QACxC,0BAA0B,GAAG,IAAI,qBAAqB,CAAC;QACvD,qBAAqB,GAAG,IAAI,oBAAoB,CAAC;QACjD,kBAAkB,GAAG,IAAI,0BAA0B,CAAC;QACpD,kBAAkB,GAAG,IAAI,8BAA8B,CAAC;IAC1D;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\r\n  LOGIN: \"/login\",\r\n  FORGOT_PASSWORD: \"/forgot-password\",\r\n  VERIFY: \"/verify\",\r\n  RESET_PASSWORD: \"/reset-password\",\r\n  CANDIDATE_ASSESSMENT: \"/candidate-assessment\",\r\n  DASHBOARD: \"/dashboard\",\r\n  HOME: \"/\",\r\n  BUY_SUBSCRIPTION: \"/buy-subscription\",\r\n  PROFILE: {\r\n    MY_PROFILE: \"/my-profile\",\r\n  },\r\n  SUBSCRIPTIONS: {\r\n    SUCCESS: \"/subscriptions/success\",\r\n    CANCEL: \"/subscriptions/cancel\",\r\n  },\r\n  JOBS: {\r\n    CAREER_BASED_SKILLS: \"/career-based-skills\",\r\n    ROLE_BASED_SKILLS: \"/role-based-skills\",\r\n    CULTURE_BASED_SKILLS: \"/culture-based-skills\",\r\n    GENERATE_JOB: \"/generate-job\",\r\n    EDIT_SKILLS: \"/edit-skills\",\r\n    HIRING_TYPE: \"/hiring-type\",\r\n    JOB_EDITOR: \"/job-editor\",\r\n    ACTIVE_JOBS: \"/active-jobs\",\r\n    CANDIDATE_PROFILE: \"/candidate-profile\",\r\n    ARCHIVE: \"/archive\",\r\n  },\r\n  SCREEN_RESUME: {\r\n    MANUAL_CANDIDATE_UPLOAD: \"/manual-upload-resume\",\r\n    CANDIDATE_QUALIFICATION: \"/candidate-qualification\",\r\n    CANDIDATE_LIST: \"/candidates-list\",\r\n    CANDIDATES: \"/candidates\",\r\n  },\r\n  INTERVIEW: {\r\n    ADD_CANDIDATE_INFO: \"/additional-submission\",\r\n    SCHEDULE_INTERVIEW: \"/schedule-interview\",\r\n    PRE_INTERVIEW_QUESTIONS_OVERVIEW: \"/pre-interview-questions-overview\",\r\n    INTERVIEW_QUESTION: \"/interview-question\",\r\n    CALENDAR: \"/calendar\",\r\n    INTERVIEW_SUMMARY: \"/interview-summary\",\r\n  },\r\n\r\n  ROLE_EMPLOYEES: {\r\n    ROLES_PERMISSIONS: \"/roles-permissions\",\r\n    EMPLOYEE_MANAGEMENT: \"/employee-management\",\r\n    EMPLOYEE_MANAGEMENT_DETAIL: \"/employee-management-detail\",\r\n    ADD_EMPLOYEE: \"/add-employees\",\r\n    ADD_DEPARTMENT: \"/add-department\",\r\n  },\r\n\r\n  FINAL_ASSESSMENT: {\r\n    FINAL_ASSESSMENT: \"/final-assessment\",\r\n  },\r\n};\r\n\r\nexport const BEFORE_LOGIN_ROUTES = [ROUTES.LOGIN, ROUTES.FORGOT_PASSWORD, ROUTES.VERIFY, ROUTES.RESET_PASSWORD, ROUTES.CANDIDATE_ASSESSMENT];\r\n\r\n// Routes that don't require permission checks for authenticated users\r\nexport const UNRESTRICTED_ROUTES = [ROUTES.SUBSCRIPTIONS.SUCCESS, ROUTES.SUBSCRIPTIONS.CANCEL];\r\n\r\nexport default ROUTES;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,SAAS;IACb,OAAO;IACP,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,sBAAsB;IACtB,WAAW;IACX,MAAM;IACN,kBAAkB;IAClB,SAAS;QACP,YAAY;IACd;IACA,eAAe;QACb,SAAS;QACT,QAAQ;IACV;IACA,MAAM;QACJ,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,mBAAmB;QACnB,SAAS;IACX;IACA,eAAe;QACb,yBAAyB;QACzB,yBAAyB;QACzB,gBAAgB;QAChB,YAAY;IACd;IACA,WAAW;QACT,oBAAoB;QACpB,oBAAoB;QACpB,kCAAkC;QAClC,oBAAoB;QACpB,UAAU;QACV,mBAAmB;IACrB;IAEA,gBAAgB;QACd,mBAAmB;QACnB,qBAAqB;QACrB,4BAA4B;QAC5B,cAAc;QACd,gBAAgB;IAClB;IAEA,kBAAkB;QAChB,kBAAkB;IACpB;AACF;AAEO,MAAM,sBAAsB;IAAC,OAAO,KAAK;IAAE,OAAO,eAAe;IAAE,OAAO,MAAM;IAAE,OAAO,cAAc;IAAE,OAAO,oBAAoB;CAAC;AAGrI,MAAM,sBAAsB;IAAC,OAAO,aAAa,CAAC,OAAO;IAAE,OAAO,aAAa,CAAC,MAAM;CAAC;uCAE/E", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/pages/api/auth/%5B...nextauth%5D.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\r\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\r\n\r\nimport endpoints from \"@/constants/endpoint\";\r\nimport routes from \"@/constants/routes\";\r\n\r\nexport default NextAuth({\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        email: { label: \"Email\", type: \"email\" },\r\n        password: { label: \"Password\", type: \"password\" },\r\n      },\r\n\r\n      async authorize(credentials) {\r\n        const { email, password } = credentials as { email: string; password: string };\r\n\r\n        const res = await fetch(endpoints.auth.SIGNIN, {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify({\r\n            email,\r\n            password,\r\n          }),\r\n        });\r\n\r\n        const user = await res.json();\r\n\r\n        if (user) {\r\n          return user;\r\n        } else return null;\r\n      },\r\n    }),\r\n  ],\r\n  session: {\r\n    maxAge: 3 * 24 * 60 * 60,\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET,\r\n  pages: {\r\n    signIn: routes.LOGIN,\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      return { ...token, ...user };\r\n    },\r\n    async session({ session, token }) {\r\n      session.user = token;\r\n      return session;\r\n    },\r\n  },\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;;;;;uCAEe,CAAA,GAAA,iHAAA,CAAA,UAAQ,AAAD,EAAE;IACtB,WAAW;QACT,CAAA,GAAA,yKAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YAEA,MAAM,WAAU,WAAW;gBACzB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;gBAE5B,MAAM,MAAM,MAAM,MAAM,qHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,MAAM,EAAE;oBAC7C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;oBACF;gBACF;gBAEA,MAAM,OAAO,MAAM,IAAI,IAAI;gBAE3B,IAAI,MAAM;oBACR,OAAO;gBACT,OAAO,OAAO;YAChB;QACF;KACD;IACD,SAAS;QACP,QAAQ,IAAI,KAAK,KAAK;IACxB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;QACL,QAAQ,mHAAA,CAAA,UAAM,CAAC,KAAK;IACtB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,OAAO;gBAAE,GAAG,KAAK;gBAAE,GAAG,IAAI;YAAC;QAC7B;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,QAAQ,IAAI,GAAG;YACf,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/server/route-modules/pages-api/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/pages-api/module.js')\n} else {\n  if (process.env.NODE_ENV === 'development') {\n    module.exports = require('next/dist/compiled/next-server/pages-api.runtime.dev.js')\n  } else if (process.env.TURBOPACK) {\n    module.exports = require('next/dist/compiled/next-server/pages-api-turbo.runtime.prod.js')\n  } else {\n    module.exports = require('next/dist/compiled/next-server/pages-api.runtime.prod.js')\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,QAAQ,KAAK,WAAe;QAC1CH,OAAOC,OAAO,GAAGC,QAAQ;IAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACM,SAAS,EAAE;;IAIlC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/server/route-kind.ts"], "sourcesContent": ["export const enum RouteKind {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  IMAGE = 'IMAGE',\n}\n"], "names": ["RouteKind"], "mappings": ";;;AAAO,IAAWA,YAAAA,WAAAA,GAAAA,SAAAA,SAAAA;IAChB;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;IAED;;GAEC,GAAA,SAAA,CAAA,YAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,WAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,YAAA,GAAA;IAGD;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;WAtBeA;MAwBjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/build/templates/helpers.ts"], "sourcesContent": ["/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */\nexport function hoist(module: any, name: string) {\n  // If the name is available in the module, return it.\n  if (name in module) {\n    return module[name]\n  }\n\n  // If a property called `then` exists, assume it's a promise and\n  // return a promise that resolves to the name.\n  if ('then' in module && typeof module.then === 'function') {\n    return module.then((mod: any) => hoist(mod, name))\n  }\n\n  // If we're trying to hoise the default export, and the module is a function,\n  // return the module itself.\n  if (typeof module === 'function' && name === 'default') {\n    return module\n  }\n\n  // Otherwise, return undefined.\n  return undefined\n}\n"], "names": ["hoist", "module", "name", "then", "mod", "undefined"], "mappings": "AAAA;;;;;;CAMC,GACD;;;AAAO,SAASA,MAAMC,MAAW,EAAEC,IAAY;IAC7C,qDAAqD;IACrD,IAAIA,QAAQD,QAAQ;QAClB,OAAOA,MAAM,CAACC,KAAK;IACrB;IAEA,gEAAgE;IAChE,8CAA8C;IAC9C,IAAI,UAAUD,UAAU,OAAOA,OAAOE,IAAI,KAAK,YAAY;QACzD,OAAOF,OAAOE,IAAI,CAAC,CAACC,MAAaJ,MAAMI,KAAKF;IAC9C;IAEA,6EAA6E;IAC7E,4BAA4B;IAC5B,IAAI,OAAOD,WAAW,cAAcC,SAAS,WAAW;QACtD,OAAOD;IACT;IAEA,+BAA+B;IAC/B,OAAOI;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/next/dist/src/build/templates/pages-api.ts"], "sourcesContent": ["import { PagesAPIRouteModule } from '../../server/route-modules/pages-api/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\n\nimport { hoist } from './helpers'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\n\n// Re-export the handler (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export config.\nexport const config = hoist(userland, 'config')\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesAPIRouteModule({\n  definition: {\n    kind: RouteKind.PAGES_API,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  userland,\n})\n"], "names": ["PagesAPIRouteModule", "RouteKind", "hoist", "userland", "config", "routeModule", "definition", "kind", "PAGES_API", "page", "pathname", "bundlePath", "filename"], "mappings": ";;;;;AAAA,SAASA,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,SAAS,QAAQ,0BAAyB;AAEnD,SAASC,KAAK,QAAQ,YAAW;AAEjC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;;;;;yMAGzBD,QAAAA,EAAMC,4IAAU,WAAU;AAGlC,MAAMC,2KAASF,QAAAA,EAAMC,4IAAU,UAAS;AAGxC,MAAME,cAAc,qMAAIL,sBAAAA,CAAoB;IACjDM,YAAY;QACVC,8JAAMN,YAAAA,CAAUO,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;cACAT;AACF,GAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}