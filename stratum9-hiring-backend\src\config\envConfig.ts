import env from "../env.json";
import { ENV_VARIABLE } from "../utils/constants";

/**
 * Returns the environment configuration based on NODE_ENV
 * Falls back to different environments if the specified one doesn't exist
 */
const envConfig = () => {
  // Get NODE_ENV or default to local
  const nodeEnv = process.env.NODE_ENV || ENV_VARIABLE.LOCAL;

  // Check if the requested environment exists in the config
  if (env[nodeEnv]) {
    return env[nodeEnv];
  }

  // If the requested environment doesn't exist, try to fall back in order
  // of production -> development -> local
  if (nodeEnv !== "production" && env.production) {
    console.log(
      `Environment ${nodeEnv} not found, using production environment`
    );
    return env.production;
  }

  if (nodeEnv !== "development" && env.development) {
    console.log(
      `Environment ${nodeEnv} not found, using development environment`
    );
    return env.development;
  }

  // Default fallback to local
  console.log(`Environment ${nodeEnv} not found, using local environment`);
  return env[ENV_VARIABLE.LOCAL];
};

export default envConfig;
