/* [project]/src/styles/eventModal.scss.css [app-client] (css) */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #00000080;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background: #fff;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 12px #00000026;
  position: relative;
}

.modalHeaderWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.modalHeaderWrapper h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0 5px;
  line-height: 1;
  transition: color .2s;
}

.closeButton:hover {
  color: #333;
}

.modalBody {
  margin-bottom: 20px;
}

.modalBody p {
  margin: 0 0 10px;
  color: #555;
  line-height: 1.5;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.modalFooter button {
  min-width: 100px;
}

.darkOutlineBtn {
  background-color: #0000;
  border: 1px solid #555;
  color: #333;
  padding: 10px 16px;
  font-weight: 500;
  transition: all .2s;
}

.darkOutlineBtn:hover {
  background-color: #f5f5f5;
}

.primaryBtn {
  background-color: #2563eb;
  border: none;
  color: #fff;
  padding: 10px 16px;
  font-weight: 600;
  transition: all .2s;
}

.primaryBtn:hover {
  background-color: #1d4ed8;
}

.rounded-md {
  border-radius: 4px;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.formGroup input, .formGroup select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.timeGroup {
  display: flex;
  gap: 16px;
}

.timeGroup .formGroup {
  flex: 1;
}

.timeInputContainer {
  display: flex;
  gap: 8px;
}

.timeInputContainer input {
  flex: 3;
}

.timeInputContainer select {
  flex: 1;
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.buttonGroup button {
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.saveButton {
  background-color: #4285f4;
  color: #fff;
  border: none;
}

.cancelButton {
  background-color: #0000;
  border: 1px solid #ddd;
}


/* [project]/node_modules/react-circular-progressbar/dist/styles.css [app-client] (css) */
.CircularProgressbar {
  width: 100%;
  vertical-align: middle;
}

.CircularProgressbar .CircularProgressbar-path {
  stroke: #3e98c7;
  stroke-linecap: round;
  -webkit-transition: stroke-dashoffset .5s;
  transition: stroke-dashoffset .5s;
}

.CircularProgressbar .CircularProgressbar-trail {
  stroke: #d6d6d6;
  stroke-linecap: round;
}

.CircularProgressbar .CircularProgressbar-text {
  fill: #3e98c7;
  font-size: 20px;
  dominant-baseline: middle;
  text-anchor: middle;
}

.CircularProgressbar .CircularProgressbar-background {
  fill: #d6d6d6;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-background {
  fill: #3e98c7;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-text {
  fill: #fff;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-path {
  stroke: #fff;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-trail {
  stroke: #0000;
}


/* [project]/src/components/loader/questionGeneratorLoader.module.css [app-client] (css) */
.questionGeneratorLoader-module__dUgNXW__question_generator_loader_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000b3;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.questionGeneratorLoader-module__dUgNXW__loader_wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.questionGeneratorLoader-module__dUgNXW__loader_container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.questionGeneratorLoader-module__dUgNXW__loader_text {
  margin-top: 15px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  width: 100%;
  position: relative;
}

.questionGeneratorLoader-module__dUgNXW__loader {
  width: 8px;
  height: 40px;
  border-radius: 4px;
  display: block;
  margin: 20px auto;
  position: relative;
  background: currentColor;
  color: #fff;
  box-sizing: border-box;
  animation: .3s linear .3s infinite alternate questionGeneratorLoader-module__dUgNXW__loaderAnim;
}

.questionGeneratorLoader-module__dUgNXW__loader:after, .questionGeneratorLoader-module__dUgNXW__loader:before {
  content: "";
  width: 8px;
  height: 40px;
  border-radius: 4px;
  background: currentColor;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 20px;
  box-sizing: border-box;
  animation: .3s linear .45s infinite alternate questionGeneratorLoader-module__dUgNXW__loaderAnim;
}

.questionGeneratorLoader-module__dUgNXW__loader:before {
  left: -20px;
  animation-delay: 0s;
}

@keyframes questionGeneratorLoader-module__dUgNXW__loaderAnim {
  0% {
    height: 48px;
  }

  100% {
    height: 4px;
  }
}


/* [project]/src/styles/conductInterview.module.scss.module.css [app-client] (css) */
.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__question_info_box ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page {
  padding-bottom: 40px;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__question_info_box ul {
  display: flex;
  gap: 4rem;
  align-items: center;
  margin-bottom: 30px;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__question_info_box ul li {
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__question_info_box ul li span {
  width: 20px;
  height: 20px;
  border-radius: 100%;
  display: block;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__question_info_box ul li span.conductInterview-module-scss-module__yztraq__current {
  background: #cb9932;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__question_info_box ul li span.conductInterview-module-scss-module__yztraq__completed {
  background: #436eb6;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__question_info_box ul li span.conductInterview-module-scss-module__yztraq__additional {
  background: #073;
}

.conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__summary_card_height {
  min-height: 230px;
}

@media (width <= 767px) {
  .conductInterview-module-scss-module__yztraq__conduct_interview_page .conductInterview-module-scss-module__yztraq__summary_card_height {
    min-height: auto;
  }
}

.conductInterview-module-scss-module__yztraq__conduct_interview {
  padding-top: 40px;
}


/* [project]/node_modules/react-loading-skeleton/dist/skeleton.css [app-client] (css) */
@keyframes react-loading-skeleton {
  100% {
    transform: translateX(100%);
  }
}

.react-loading-skeleton {
  --base-color: #ebebeb;
  --highlight-color: #f5f5f5;
  --animation-duration: 1.5s;
  --animation-direction: normal;
  --pseudo-element-display: block;
  background-color: var(--base-color);
  width: 100%;
  border-radius: .25rem;
  display: inline-flex;
  line-height: 1;
  position: relative;
  user-select: none;
  overflow: hidden;
}

.react-loading-skeleton:after {
  content: " ";
  display: var(--pseudo-element-display);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-repeat: no-repeat;
  background-image: var(--custom-highlight-background, linear-gradient(90deg, var(--base-color) 0%, var(--highlight-color) 50%, var(--base-color) 100%));
  transform: translateX(-100%);
  animation-name: react-loading-skeleton;
  animation-direction: var(--animation-direction);
  animation-duration: var(--animation-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

@media (prefers-reduced-motion) {
  .react-loading-skeleton {
    --pseudo-element-display: none;
  }
}


/*# sourceMappingURL=_8d0bc4d6._.css.map*/
