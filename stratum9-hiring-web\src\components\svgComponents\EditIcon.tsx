import React from "react";

type EditIconProps = {
  className?: string;
  fillNone?: boolean;
  fillColor?: string;
};

function EditIcon({ className, fillNone, fillColor }: EditIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 27 26" fill="none" className={className}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.6677 25.2539H15.9971C15.4451 25.2539 14.9971 24.8059 14.9971 24.2539C14.9971 23.7019 15.4451 23.2539 15.9971 23.2539H25.6677C26.2197 23.2539 26.6677 23.7019 26.6677 24.2539C26.6677 24.8059 26.2197 25.2539 25.6677 25.2539Z"
        fill={!fillNone ? "#436EB6" : fillColor ? fillColor : ""}
      />
      <mask id="mask0_11116_355" style={{ maskType: "luminance" }} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="26">
        <path fillRule="evenodd" clipRule="evenodd" d="M0.666992 0H23.5744V25.2527H0.666992V0Z" fill="white" />
      </mask>
      <g mask="url(#mask0_11116_355)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.4807 2.68886L2.92736 18.3889C2.69936 18.6742 2.61536 19.0422 2.69936 19.3955L3.60736 23.2422L7.65936 23.1915C8.04469 23.1875 8.40069 23.0155 8.63669 22.7222C12.926 17.3555 21.1034 7.12352 21.3354 6.82352C21.554 6.46886 21.6394 5.96752 21.5247 5.48486C21.4074 4.99019 21.0994 4.57019 20.6554 4.30219C20.5607 4.23686 18.314 2.49286 18.2447 2.43819C17.3994 1.76086 16.166 1.87819 15.4807 2.68886ZM2.81802 25.2529C2.35536 25.2529 1.95269 24.9355 1.84469 24.4835L0.752691 19.8555C0.527358 18.8969 0.751358 17.9075 1.36602 17.1395L13.926 1.43019C13.9314 1.42486 13.9354 1.41819 13.9407 1.41286C15.318 -0.23381 17.8087 -0.476476 19.4887 0.871523C19.5554 0.923523 21.786 2.65686 21.786 2.65686C22.5967 3.13952 23.23 4.00219 23.47 5.02352C23.7087 6.03419 23.5354 7.07686 22.9794 7.95819C22.938 8.02352 22.902 8.07952 10.198 23.9729C9.58603 24.7355 8.66869 25.1795 7.68336 25.1915L2.83136 25.2529H2.81802Z"
          fill={!fillNone ? "#436EB6" : fillColor ? fillColor : ""}
        />
      </g>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.6316 11.5792C19.4182 11.5792 19.2049 11.5112 19.0222 11.3725L11.7529 5.78851C11.3156 5.45251 11.2329 4.82584 11.5689 4.38584C11.9062 3.94851 12.5329 3.86717 12.9716 4.20317L20.2422 9.78584C20.6796 10.1218 20.7622 10.7498 20.4249 11.1885C20.2289 11.4445 19.9316 11.5792 19.6316 11.5792Z"
        fill={!fillNone ? "#436EB6" : fillColor ? fillColor : ""}
      />
    </svg>
  );
}

export default EditIcon;
