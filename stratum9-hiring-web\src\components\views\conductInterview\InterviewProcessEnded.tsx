import Button from "@/components/formElements/Button";
import Link from "next/link";
import style from "../../../styles/conductInterview.module.scss";
import Image from "next/image";
import candidateProfile from "../../../../public/assets/images/doctor-strange.png";
const InterviewProcessEnded = () => {
  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="breadcrumb">
            <Link href="/">Home </Link>
            <Link href="/">Conduct Interview</Link>
            <Link href="/">Final Assessment</Link>
          </div>
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Final <span>Assessment Summary</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          {/* <AssessmentSummarySwiper/> */}
          <div className="summary-text-card">
            <div className="candidate-profile">
              <Image src={candidateProfile} alt="candidate image" className="candidate-image" width={100} height={100} />
              <div className="candidate-info">
                <h3 className="candidate-name">Benedict Cumberbatch</h3>
                <div className="info-container">
                  <div className="info-item">
                    <p className="info-title">Post Applied For</p>
                    <p className="info-value">Operations Admin</p>
                  </div>
                  <div className="info-item">
                    <p className="info-title">Rounds Cleared</p>
                    <p className="info-value">02</p>
                  </div>
                </div>
                {/* <Button className="secondary-btn rounded-md">Hire Candidate</Button> */}
              </div>
            </div>
            <p>
              You’ve ended the interview process for the above candidate. Now you have the option to either hire the candidate directly or have the
              candidate take a final assessment. This assessment is designed to help ensure the candidate is the right fit for the role.
            </p>
            <div className="button-align mt-5">
              <Button className="secondary-btn rounded-md">Hire Candidate</Button>
              <Button className="dark-outline-btn rounded-md">Take Final Assessment</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterviewProcessEnded;
