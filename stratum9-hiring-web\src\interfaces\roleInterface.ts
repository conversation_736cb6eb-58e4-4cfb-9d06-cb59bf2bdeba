import { ROLE_ALTER_MODE } from "@/components/views/accessManagement/RolesPermissions";
import { FindRoleResponse } from "@/services/roleService";

export interface Role {
  id: number;
  name: string;
  associatedOrganizationId?: number;
}

export interface RoleFormData {
  name: string;
}

export interface RoleModalProps {
  onClickCancel: () => void;
  onSubmitSuccess: (message?: string, responseData?: FindRoleResponse) => void;
  disabled?: boolean;
  role: Role | null;
  mode: (typeof ROLE_ALTER_MODE)[keyof typeof ROLE_ALTER_MODE];
}

export interface IRolePermission {
  id: number;
  name: string;
  isDefaultRole: boolean;
  permission_count: string;
  updated_ts: string;
}
