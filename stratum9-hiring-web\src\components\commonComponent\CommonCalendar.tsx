"use client";
import React, { useRef } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { DatesSetArg, DateSelectArg, EventClickArg, DayHeaderContentArg } from "@fullcalendar/core";
import { IGetInterviewsResponse } from "@/interfaces/interviewInterfaces";

interface CalendarProps {
  handleDatesSet: (info: DatesSetArg) => void;
  handleOnSelect: (info: DateSelectArg) => void;
  interviews: Array<IGetInterviewsResponse>;
  handleEventClick: (info: EventClickArg) => void;
}

const CommonCalendar: React.FC<CalendarProps> = ({ handleDatesSet, handleOnSelect, interviews, handleEventClick }) => {
  const calendarRef = useRef<FullCalendar>(null);

  const renderEventContent = (eventInfo: EventClickArg) => {
    return (
      <div className="fc-event-content">
        <div className="fc-event-time">
          {new Date(eventInfo.event.start!).toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          })}
        </div>
        <div className="fc-event-title">{eventInfo.event.title}</div>
      </div>
    );
  };

  const renderDayHeader = (args: DayHeaderContentArg) => {
    // Only apply custom header in week view
    const date = args.date;
    const dayNumber = date.getDate();
    const weekday = new Intl.DateTimeFormat("en-US", { weekday: "short" }).format(date);
    if (args.view.type === "timeGridWeek") {
      return (
        <div className="custom-day-header">
          <div className="day-number">{dayNumber}</div>
          <div className="weekday-name">{weekday}</div>
        </div>
      );
    } else {
      return (
        <div className="custom-day-header">
          <div className="weekday-name">{weekday}</div>
        </div>
      );
    }
  };

  return (
    <div className="calendar-container">
      <FullCalendar
        ref={calendarRef}
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        headerToolbar={{
          left: "prev,next",
          center: "title",
          right: "dayGridMonth,timeGridWeek",
        }}
        initialView="dayGridMonth"
        editable={false}
        selectable={true}
        fixedWeekCount={false}
        dayMaxEvents={true}
        weekends={true}
        events={interviews}
        select={handleOnSelect}
        eventClick={handleEventClick}
        height="66vh"
        datesSet={handleDatesSet}
        eventContent={renderEventContent}
        // dayHeaderFormat={{ weekday: "short", day: "numeric" }}
        dayHeaderContent={renderDayHeader}
      />
    </div>
  );
};

export default React.memo(CommonCalendar);
