// Internal libraries
import React from "react";

// External libraries
import Link from "next/link";
import Image from "next/image";
import { useForm } from "react-hook-form";

// Components
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import UploadFileIcon from "@/components/svgComponents/UploadFileIcon";
import DeleteDarkIcon from "@/components/svgComponents/DeleteDarkIcon";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textarea from "@/components/formElements/Textarea";
import UploadBox from "@/components/commonComponent/UploadBox";

// Assets
import operationAdminsImg from "@/../public/assets/images/operation-admins-img.png";

// CSS
import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";

function UploadResume() {
  const { control } = useForm();
  const t = useTranslations();
  return (
    <div className={`${style.resume_page} ${style.upload_resume_page}`}>
      <div className="container">
        <div className={style.inner_page}>
          <div className="row align-items-center">
            <div className="col-md-8">
              <div className="common-page-header">
                <div className="breadcrumb">
                  <Link href="#"> {t("home")} </Link>
                  <Link href="#"> {t("resume_screening")} </Link>
                </div>
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>
                      <BackArrowIcon />
                      {t("manual_resume_upload_for")} <span> {t("operational_admin")} </span>
                    </h2>
                  </div>
                </div>
              </div>
              <div className="mb-4">
                <InputWrapper>
                  <InputWrapper.Label htmlFor="email" required>
                    {t("upload_resume")}
                  </InputWrapper.Label>
                  {/* upload-box */}
                  <UploadBox />
                </InputWrapper>

                {/* uploded-item */}
                <div className="uploded-item">
                  <div className="item-name">
                    <UploadFileIcon />
                    <p>Kathryn Hahn CV.pdf</p>
                  </div>
                  <DeleteDarkIcon className="delete-item" />
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <Image className={`${style.operation_admins_img} mb-4`} src={operationAdminsImg} alt="operationAdminsImg" width={500} height={500} />
            </div>
          </div>
          <div className="mb-4">
            <InputWrapper>
              <InputWrapper.Label htmlFor="email" required>
                {t("upload_assesment")}
              </InputWrapper.Label>
              {/* upload-box */}
              <UploadBox />
            </InputWrapper>

            {/* uploded-item */}
            <div className="uploded-item">
              <div className="item-name">
                <UploadFileIcon />
                <p>Kathryn Hahn Test Assessment.pdf</p>
              </div>
              <DeleteDarkIcon className="delete-item" />
            </div>
          </div>

          <div className="mb-4">
            <InputWrapper>
              <InputWrapper.Label htmlFor="email" required>
                {t("additional_information")}
              </InputWrapper.Label>
              <Textarea rows={6} name="" control={control} placeholder="Enter additional Info About Candidate" className="form-control" />
            </InputWrapper>
          </div>
        </div>

        <div className="button-align py-5">
          <Button className="primary-btn rounded-md">Analyze</Button>
          <Button className="dark-outline-btn rounded-md">Cancel</Button>
        </div>
      </div>
    </div>
  );
}

export default UploadResume;
