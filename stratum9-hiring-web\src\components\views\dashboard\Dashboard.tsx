"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Image from "next/image";
import Skeleton from "react-loading-skeleton";
import dayjs from "dayjs";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";

import { useTranslations } from "next-intl";

import Textbox from "@/components/formElements/Textbox";
import InputWrapper from "@/components/formElements/InputWrapper";
import CandidateResumeIcon from "@/components/svgComponents/CandidateResumeIcon";
import QuestionnaireIcon from "@/components/svgComponents/QuestionnaireIcon";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import ResumeModal from "@/components/commonModals/ResumeModal";

import getDashboardCounts from "@/services/dsahboard/dashboardServies";
import { upcomigOrPastInterview } from "@/services/interviewServices";

import { AuthState } from "@/redux/slices/authSlice";
import { IDashboardStats } from "@/interfaces/screenResumeInterfaces";
import { InterviewTabType } from "@/constants/screenResumeConstant";
import { IUpcomingOrPastInterview } from "@/interfaces/interviewInterfaces";
import ROUTES from "@/constants/routes";

import dashboardImg from "../../../../public/assets/images/dashboard-topcard.png";
import crateJobImg from "../../../../public/assets/images/create-job.png";
import resumesImg from "../../../../public/assets/images/screen-resumes.png";
import conductImg from "../../../../public/assets/images/conduct-interviews.png";

import "react-loading-skeleton/dist/skeleton.css";
import style from "@/styles/commonPage.module.scss";
import styles from "../../../styles/commonPage.module.scss";
import { toastMessageError } from "@/utils/helper";
import { PerformanceCardSkeleton } from "../conductInterview/skeletons/PerformanceCardSkeleton";
import useDebounce from "@/hooks/useDebounce";

function Dashboard() {
  const router = useRouter();
  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const t = useTranslations();
  const { control, watch } = useForm();

  const [selectedTab, setSelectedTab] = useState<InterviewTabType>(InterviewTabType.UPCOMING);
  const [interviews, setInterviews] = useState<IUpcomingOrPastInterview[]>();
  const [dashboardStats, setDashboardStats] = useState<IDashboardStats>({
    totalJobs: 0,
    activeJobs: 0,
    candidatesHired: 0,
    scheduledInterviews: 0,
    resumeOnHold: 0,
    upcomingInterviews: 0,
  });

  // Combine resumePreview and selectedResumeLink into one state
  const [resumeModal, setResumeModal] = useState<{ isOpen: boolean; resumeLink: string | null }>({ isOpen: false, resumeLink: null });
  const tCommon = useTranslations("common");
  const tDashboard = useTranslations("dashboard");

  const [isLoading, setIsLoading] = useState(true);
  const [interviewsLoading, setInterviewsLoading] = useState(false);
  const initialFetchDone = useRef(false);

  const searchStr = watch("search");
  const debouncedSearchStr = useDebounce(searchStr, 1000);

  useEffect(() => {
    if (initialFetchDone.current) return;

    const fetchDashboardStats = async () => {
      setIsLoading(true);
      if (!userData?.orgId || !userData?.id) {
        setIsLoading(false);
        return;
      }

      try {
        const res = await getDashboardCounts();
        // Handle nested API response structure
        if (res.data && res.data.success) {
          const counts = res.data.data;
          setDashboardStats({
            totalJobs: counts.totalJobs || 0,
            activeJobs: counts.activeJobs || 0,
            candidatesHired: counts.candidatesHired || 0,
            scheduledInterviews: counts.scheduledInterviews || 0,
            resumeOnHold: counts.onHoldApplications || 0,
            upcomingInterviews: counts.upcomingInterviews || 0,
          });
        } else {
          toastMessageError(t(res?.data?.message));
        }
      } catch {
        toastMessageError(t("something_went_wrong"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardStats();
    fetchInterviewData();
    initialFetchDone.current = true;
  }, []);

  useEffect(() => {
    fetchInterviewData();
  }, [selectedTab, debouncedSearchStr]);

  const fetchInterviewData = useCallback(async () => {
    setInterviewsLoading(true);
    try {
      const res = await upcomigOrPastInterview({
        isPast: selectedTab === InterviewTabType.PAST,
        searchStr: debouncedSearchStr || "",
      });
      if (res?.data?.success) {
        setInterviews(res.data.data || []);
      } else {
        toastMessageError(t(res?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setInterviewsLoading(false);
    }
  }, [selectedTab, debouncedSearchStr]);

  return (
    <>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                {t("hiring_manager_dashboard")} - <span>{tCommon("home")}</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="common-box">
          {/* <Sidebar /> */}
          <main className="main-content">
            <div className={style.dashboard_page}>
              <section className={styles.dashboard__stats}>
                <div className={styles.dashboard__stat}>
                  <span className={styles.dashboard__stat_label}>{tDashboard("jobs_created")}</span>
                  <span className={styles.dashboard__stat_value}>{isLoading ? <Skeleton width={30} height={30} /> : dashboardStats?.totalJobs}</span>
                </div>
                <div className={styles.dashboard__stat} style={{ cursor: "pointer" }} onClick={() => router.push(ROUTES.JOBS.ACTIVE_JOBS)}>
                  <span className={styles.dashboard__stat_label}>{tDashboard("active_jobs")}</span>
                  <span className={styles.dashboard__stat_value}>{isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.activeJobs}</span>
                </div>
                <div className={styles.dashboard__stat}>
                  <span className={styles.dashboard__stat_label}>{tDashboard("upcoming_interviews")}</span>
                  <span className={styles.dashboard__stat_value}>
                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.upcomingInterviews}
                  </span>
                </div>
                <div className={styles.dashboard__stat}>
                  <span className={styles.dashboard__stat_label}>{tDashboard("candidates_hired")}</span>
                  <span className={styles.dashboard__stat_value}>
                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.candidatesHired}
                  </span>
                </div>
                <div className={styles.dashboard__stat}>
                  <span className={styles.dashboard__stat_label}>{tDashboard("scheduled_interviews")}</span>
                  <span className={styles.dashboard__stat_value}>
                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.scheduledInterviews}
                  </span>
                </div>
                <div className={`${styles.dashboard__stat} ${styles.border_none}`}>
                  <span className={styles.dashboard__stat_label}>{tDashboard("resume_on_hold")}</span>
                  <span className={styles.dashboard__stat_value}>
                    {isLoading ? <Skeleton width={30} height={30} /> : dashboardStats.resumeOnHold}
                  </span>
                </div>
                <div className={styles.dashboard__stat_design}>
                  <Image src={dashboardImg} alt="dashboard-icon" className={styles.dashboard__stat_image} />
                </div>
              </section>

              <div className="row">
                <div className="col-md-4">
                  <div className="announcement-card" onClick={() => router.push(ROUTES.JOBS.HIRING_TYPE)}>
                    <div className="announcement-content">
                      <h3>
                        {tDashboard("create")} <br /> {tDashboard("a_new_job")}
                      </h3>
                    </div>
                    <div className="announcement-image">
                      <Image src={crateJobImg} alt="dashboard-icon" className="announce-img" />
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="announcement-card" onClick={() => router.push(ROUTES.JOBS.ACTIVE_JOBS)}>
                    <div className="announcement-content">
                      <h3>
                        {tDashboard("screen")} <br /> {tDashboard("resumes")}
                      </h3>
                    </div>
                    <div className="announcement-image">
                      <Image src={conductImg} alt="dashboard-icon" className="announce-img" />
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="announcement-card">
                    <div className="announcement-content">
                      <h3>
                        {tDashboard("conduct")} <br /> {tDashboard("interviews")}
                      </h3>
                    </div>
                    <div className="announcement-image">
                      <Image src={resumesImg} alt="dashboard-icon" className="announce-img" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-md-12">
                <div className={styles.dashboard_inner_head}>
                  <ul className={styles.header_tab}>
                    <li
                      className={selectedTab === InterviewTabType.UPCOMING ? styles.active : ""}
                      onClick={() => setSelectedTab(InterviewTabType.UPCOMING)}
                      style={{ cursor: "pointer" }}
                    >
                      {tDashboard("upcoming_interviews")}
                    </li>
                    <li
                      className={selectedTab === InterviewTabType.PAST ? styles.active : ""}
                      onClick={() => setSelectedTab(InterviewTabType.PAST)}
                      style={{ cursor: "pointer" }}
                    >
                      {tDashboard("past_interviews")}
                    </li>
                  </ul>
                  <div className={styles.search_box}>
                    <InputWrapper className="mb-0 w-100">
                      <div className="icon-align right">
                        <Textbox
                          className="form-control w-100"
                          control={control}
                          name="search"
                          type="text"
                          placeholder={tDashboard("search_placeholder")}
                        >
                          <InputWrapper.Icon>
                            <SearchIcon />
                          </InputWrapper.Icon>
                        </Textbox>
                      </div>
                    </InputWrapper>
                  </div>
                </div>
              </div>

              <div className="row g-4 mb-5">
                {interviewsLoading ? (
                  <PerformanceCardSkeleton count={6} />
                ) : interviews && interviews.length > 0 ? (
                  interviews.map((interview, idx) => (
                    <div className="col-md-4" key={idx}>
                      <div className="candidate-card">
                        <h2>{interview.candidateName}</h2>
                        <p className="title">{interview.jobTitle}</p>
                        <p className="title">
                          {dayjs(interview.startTime).format("MMM DD, YYYY")} | {dayjs(interview.startTime).format("hh:mm A")} -{" "}
                          {dayjs(interview.endTime).format("hh:mm A")}
                        </p>

                        <div className="actions">
                          <a
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                              setResumeModal({ isOpen: true, resumeLink: interview.resumeFile });
                            }}
                            aria-label="Candidate Resume"
                          >
                            <CandidateResumeIcon />
                            {tDashboard("candidate_resume")}
                          </a>
                          <a
                            href={
                              selectedTab === InterviewTabType.UPCOMING
                                ? `${ROUTES.INTERVIEW.PRE_INTERVIEW_QUESTIONS_OVERVIEW}?interviewId=${interview.interviewId}&jobApplicationId=${interview.jobApplicationId}&interviewType=${encodeURIComponent(interview.roundType!)}&resumeLink=${encodeURIComponent(interview.resumeFile!)}&isEnded=${interview.isEnded}&date=${encodeURIComponent(interview.startTime)}`
                                : ROUTES.JOBS.ACTIVE_JOBS
                            }
                            aria-label={
                              selectedTab === InterviewTabType.UPCOMING ? tDashboard("set_view_questionarie") : tDashboard("view_your_summary")
                            }
                          >
                            <QuestionnaireIcon />
                            {selectedTab === InterviewTabType.UPCOMING ? tDashboard("set_view_questionarie") : tDashboard("view_your_summary")}
                          </a>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-12 text-center">
                    <p>{tDashboard("no_interviews_found")}</p>
                  </div>
                )}
              </div>
            </div>
          </main>
        </div>
      </div>

      {/* Resume Modal */}
      <ResumeModal
        isOpen={resumeModal.isOpen}
        onClose={() => setResumeModal({ ...resumeModal, isOpen: false })}
        resumeLink={resumeModal.resumeLink}
      />
    </>
  );
}

export default Dashboard;
