{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/styles/eventModal.scss.css"], "sourcesContent": [".modalOverlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);display:flex;justify-content:center;align-items:center;z-index:1000}.modalContent{background:#fff;padding:25px;border-radius:8px;width:90%;max-width:500px;box-shadow:0 4px 12px rgba(0,0,0,.15);position:relative}.modalHeaderWrapper{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.modalHeaderWrapper h3{margin:0;font-size:1.5rem;font-weight:700;color:#333}.closeButton{background:rgba(0,0,0,0);border:none;font-size:24px;cursor:pointer;color:#999;padding:0 5px;line-height:1;transition:color .2s}.closeButton:hover{color:#333}.modalBody{margin-bottom:20px}.modalBody p{margin:0 0 10px;color:#555;line-height:1.5}.modalFooter{display:flex;justify-content:flex-end;gap:12px}.modalFooter button{min-width:100px}.darkOutlineBtn{background-color:rgba(0,0,0,0);border:1px solid #555;color:#333;padding:10px 16px;font-weight:500;transition:all .2s}.darkOutlineBtn:hover{background-color:#f5f5f5}.primaryBtn{background-color:#2563eb;border:none;color:#fff;padding:10px 16px;font-weight:600;transition:all .2s}.primaryBtn:hover{background-color:#1d4ed8}.rounded-md{border-radius:4px}.formGroup{margin-bottom:16px}.formGroup label{display:block;margin-bottom:8px;font-weight:500}.formGroup input,.formGroup select{width:100%;padding:10px;border:1px solid #ddd;border-radius:4px;font-size:14px}.timeGroup{display:flex;gap:16px}.timeGroup .formGroup{flex:1}.timeInputContainer{display:flex;gap:8px}.timeInputContainer input{flex:3}.timeInputContainer select{flex:1}.buttonGroup{display:flex;justify-content:flex-end;gap:12px;margin-top:24px}.buttonGroup button{padding:10px 20px;border-radius:4px;font-weight:500;cursor:pointer}.saveButton{background-color:#4285f4;color:#fff;border:none}.cancelButton{background-color:rgba(0,0,0,0);border:1px solid #ddd}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAA+J;;;;;;;;;;AAA+I;;;;;;;AAAqG;;;;;;;AAA4E;;;;;;;;;;;AAA4I;;;;AAA8B;;;;AAA8B;;;;;;AAAwD;;;;;;AAA4D;;;;AAAoC;;;;;;;;;AAAqI;;;;AAA+C;;;;;;;;;AAAiH;;;;AAA2C;;;;AAA8B;;;;AAA8B;;;;;;AAAiE;;;;;;;;AAAkH;;;;;AAAiC;;;;AAA6B;;;;;AAAyC;;;;AAAiC;;;;AAAkC;;;;;;;AAA4E;;;;;;;AAAuF;;;;;;AAA4D", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/react-circular-progressbar/dist/styles.css"], "sourcesContent": ["/*\n * react-circular-progressbar styles\n * All of the styles in this file are configurable!\n */\n\n.CircularProgressbar {\n  /*\n   * This fixes an issue where the CircularProgressbar svg has\n   * 0 width inside a \"display: flex\" container, and thus not visible.\n   */\n  width: 100%;\n  /*\n   * This fixes a centering issue with CircularProgressbarWithChildren:\n   * https://github.com/kevinsqi/react-circular-progressbar/issues/94\n   */\n  vertical-align: middle;\n}\n\n.CircularProgressbar .CircularProgressbar-path {\n  stroke: #3e98c7;\n  stroke-linecap: round;\n  -webkit-transition: stroke-dashoffset 0.5s ease 0s;\n  transition: stroke-dashoffset 0.5s ease 0s;\n}\n\n.CircularProgressbar .CircularProgressbar-trail {\n  stroke: #d6d6d6;\n  /* Used when trail is not full diameter, i.e. when props.circleRatio is set */\n  stroke-linecap: round;\n}\n\n.CircularProgressbar .CircularProgressbar-text {\n  fill: #3e98c7;\n  font-size: 20px;\n  dominant-baseline: middle;\n  text-anchor: middle;\n}\n\n.CircularProgressbar .CircularProgressbar-background {\n  fill: #d6d6d6;\n}\n\n/*\n * Sample background styles. Use these with e.g.:\n *\n *   <CircularProgressbar\n *     className=\"CircularProgressbar-inverted\"\n *     background\n *     percentage={50}\n *   />\n */\n.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-background {\n  fill: #3e98c7;\n}\n\n.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-text {\n  fill: #fff;\n}\n\n.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-path {\n  stroke: #fff;\n}\n\n.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-trail {\n  stroke: transparent;\n}\n"], "names": [], "mappings": "AAKA;;;;;AAaA;;;;;;;AAOA;;;;;AAMA;;;;;;;AAOA;;;;AAaA;;;;AAIA;;;;AAIA;;;;AAIA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/loader/questionGeneratorLoader.module.css"], "sourcesContent": [".question_generator_loader_overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.loader_wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.loader_container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 60px;\r\n}\r\n\r\n.loader_text {\r\n  margin-top: 15px;\r\n  color: #fff;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  text-align: center;\r\n  width: 100%;\r\n  /* Fixed position to ensure stability */\r\n  position: relative;\r\n}\r\n\r\n.loader {\r\n  width: 8px;\r\n  height: 40px;\r\n  border-radius: 4px;\r\n  display: block;\r\n  margin: 20px auto;\r\n  position: relative;\r\n  background: currentColor;\r\n  color: #fff;\r\n  box-sizing: border-box;\r\n  animation: loaderAnim 0.3s 0.3s linear infinite alternate;\r\n}\r\n\r\n.loader::after,\r\n.loader::before {\r\n  content: \"\";\r\n  width: 8px;\r\n  height: 40px;\r\n  border-radius: 4px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  left: 20px;\r\n  box-sizing: border-box;\r\n  animation: loaderAnim 0.3s 0.45s linear infinite alternate;\r\n}\r\n\r\n.loader::before {\r\n  left: -20px;\r\n  animation-delay: 0s;\r\n}\r\n\r\n@keyframes loaderAnim {\r\n  0% {\r\n    height: 48px;\r\n  }\r\n  100% {\r\n    height: 4px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAWA;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;;AAeA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/styles/conductInterview.module.scss.module.css"], "sourcesContent": [".conduct_interview_page .question_info_box ul{list-style:none;padding:0;margin:0}.conduct_interview_page{padding-bottom:40px}.conduct_interview_page .question_info_box ul{display:flex;gap:4rem;align-items:center;margin-bottom:30px}.conduct_interview_page .question_info_box ul li{font-size:1.4rem;font-weight:500;color:#333;display:flex;align-items:center;gap:1rem}.conduct_interview_page .question_info_box ul li span{width:20px;height:20px;border-radius:100%;display:block}.conduct_interview_page .question_info_box ul li span.current{background:#cb9932}.conduct_interview_page .question_info_box ul li span.completed{background:#436eb6}.conduct_interview_page .question_info_box ul li span.additional{background:#073}.conduct_interview_page .summary_card_height{min-height:230px}@media(max-width: 767px){.conduct_interview_page .summary_card_height{min-height:auto}}.conduct_interview{padding-top:40px}"], "names": [], "mappings": "AAAA;;;;;;AAAiF;;;;AAA4C;;;;;;;AAA0G;;;;;;;;;AAAsI;;;;;;;AAA8G;;;;AAAiF;;;;AAAmF;;;;AAAiF;;;;AAA8D;EAAyB;;;;;AAA8D", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/react-loading-skeleton/dist/skeleton.css"], "sourcesContent": ["@keyframes react-loading-skeleton {\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n.react-loading-skeleton {\n  --base-color: #ebebeb;\n  --highlight-color: #f5f5f5;\n  --animation-duration: 1.5s;\n  --animation-direction: normal;\n  --pseudo-element-display: block; /* Enable animation */\n\n  background-color: var(--base-color);\n\n  width: 100%;\n  border-radius: 0.25rem;\n  display: inline-flex;\n  line-height: 1;\n\n  position: relative;\n  user-select: none;\n  overflow: hidden;\n}\n\n.react-loading-skeleton::after {\n  content: ' ';\n  display: var(--pseudo-element-display);\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background-repeat: no-repeat;\n  background-image: var(\n    --custom-highlight-background,\n    linear-gradient(\n      90deg,\n      var(--base-color) 0%,\n      var(--highlight-color) 50%,\n      var(--base-color) 100%\n    )\n  );\n  transform: translateX(-100%);\n\n  animation-name: react-loading-skeleton;\n  animation-direction: var(--animation-direction);\n  animation-duration: var(--animation-duration);\n  animation-timing-function: ease-in-out;\n  animation-iteration-count: infinite;\n}\n\n@media (prefers-reduced-motion) {\n  .react-loading-skeleton {\n    --pseudo-element-display: none; /* Disable animation */\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;;;;;AA2BA;EACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}