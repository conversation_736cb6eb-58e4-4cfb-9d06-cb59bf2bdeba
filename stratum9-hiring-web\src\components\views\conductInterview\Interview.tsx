import Button from "@/components/formElements/Button";
import PreviewResumeIcon from "@/components/svgComponents/PreviewResumeIcon";
import React from "react";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textarea from "@/components/formElements/Textarea";
import { useForm } from "react-hook-form";
import { useState } from "react";
import ArrowDownIcon from "@/components/svgComponents/ArrowDownIcon";
import ProgressTracker from "./ProgressTracker";
import LetterFoldIcon from "@/components/svgComponents/LetterFoldIcon";
import style from "../../../styles/conductInterview.module.scss";

const Interview = () => {
  const { control } = useForm();
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(open);
  };

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        {/* <div className="common-page-header">
          <div className="breadcrumb">
            <Link href="/">Home </Link>
            <Link href="/">Job Requirement Generations</Link>
          </div>
        </div> */}
        <div className="inner-section video_call_section mt-5">
          <div className="row">
            <div className="col-md-3">{/* <InterviewQuestion /> */}</div>
            <div className="col-md-9">
              <div className="interview-content">
                <div className="row">
                  <div className="col-md-12">
                    <ProgressTracker isRecording={false} />
                    <div className="common-page-head-section">
                      <div className="main-heading">
                        <h2>
                          Career-Based Skills (Hard Skills) and General Interview <span>Questions</span>
                        </h2>
                        <Button className="clear-btn text-btn primary p-0 m-0  ">
                          <PreviewResumeIcon className="me-2" />
                          Preview Candidate Resume
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-8">
                    <div className="interview-question-cards-height">
                      <div className="interview-question-card " onClick={() => handleOpen()}>
                        <p className="tittle">
                          Question 02 <ArrowDownIcon className={!open ? "rotate" : ""} />
                        </p>
                        <h5>Can you describe your previous experience managing schedules and coordinating teams?</h5>
                        {open && (
                          <div className="question-body ">
                            <InputWrapper>
                              <InputWrapper.Label htmlFor="additionalInfo" required>
                                Additional Info
                              </InputWrapper.Label>
                              <Textarea
                                rows={3}
                                name="additionalInfo"
                                control={control}
                                placeholder="Is there anything specific you'd like included in the job post or description?"
                                className="form-control"
                              />
                            </InputWrapper>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="behavioral-letter-card">
                      <h5>
                        Behavioral <span>Performance</span>
                      </h5>
                      <p>Describe Candidate’s Behaviours & Other Observations</p>

                      <LetterFoldIcon className="fold-svg" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Interview;
