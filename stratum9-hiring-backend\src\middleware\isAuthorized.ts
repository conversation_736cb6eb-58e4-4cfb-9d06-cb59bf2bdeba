import { NextFunction, Response, Request } from "express";
import * as <PERSON><PERSON> from "@sentry/node";
import AccessManagementServices from "../features/accessManagement/services";
import { AUTH_MSG, PERMISSION } from "../utils/constants";

/**
 * Middleware to check if the user has the required permission
 * @param permission - Permission slug to check
 * @returns Express middleware function
 */
export const isAuthorized =
  (permission: string) =>
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const roleId = req?.roleId;

      if (!roleId) {
        return res.status(401).json({
          success: false,
          message: AUTH_MSG.unauthorized_role,
          code: 401,
        });
      }

      // Find permission mappings for this role with related permission data
      const rolePermissions = await AccessManagementServices.getUserPermissions(
        roleId,
        true
      );

      if (rolePermissions.success) {
        // Check if the required permission exists in the user's permissions

        if (!rolePermissions.data.rolePermissions.includes(permission)) {
          return res.status(403).json({
            success: false,
            message: AUTH_MSG.permission_not_available,
            code: 403,
          });
        }

        next();
      } else {
        return res.status(403).json({
          success: false,
          message: AUTH_MSG.permission_not_available,
          code: 403,
        });
      }
    } catch (error) {
      Sentry.captureException(error);
      return res.status(500).json({
        success: false,
        message: AUTH_MSG.authorization_error,
        code: 500,
      });
    }
    return null;
  };

// Pre-defined middleware functions for common permissions
export const authorizedForCreateNewRole = isAuthorized(
  PERMISSION.CREATE_NEW_ROLE
);
export const authorizedForManageUserPermissions = isAuthorized(
  PERMISSION.MANAGE_USER_PERMISSIONS
);
export const authorizedForCreateNewDepartment = isAuthorized(
  PERMISSION.CREATE_NEW_DEPARTMENT
);
export const authorizedForManualResumeScreening = isAuthorized(
  PERMISSION.MANUAL_RESUME_SCREENING
);
export const authorizedForArchiveRestoreCandidates = isAuthorized(
  PERMISSION.ARCHIVE_RESTORE_CANDIDATES
);
export const authorizedForArchiveRestoreJobPosts = isAuthorized(
  PERMISSION.ARCHIVE_RESTORE_JOB_POSTS
);
export const authorizedForCreateOrEditJobPost = isAuthorized(
  PERMISSION.CREATE_OR_EDIT_JOB_POST
);
export const authorizedForManageTopCandidates = isAuthorized(
  PERMISSION.MANAGE_TOP_CANDIDATES
);

export const authorizedForViewCandidateProfileSummary = isAuthorized(
  PERMISSION.VIEW_CANDIDATE_PROFILE_SUMMARY
);

export const authorizedForAddAdditionalCandidateInfo = isAuthorized(
  PERMISSION.ADD_ADDITIONAL_CANDIDATE_INFO
);

export const authorizedForEditScheduledInterviews = isAuthorized(
  PERMISSION.EDIT_SCHEDULED_INTERVIEWS
);

export const authorizedForScheduleConductInterviews = isAuthorized(
  PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS
);

export const authorizedForManagePreInterviewQuestions = isAuthorized(
  PERMISSION.MANAGE_PRE_INTERVIEW_QUESTIONS
);

export const authorizedForManageSubscriptions = isAuthorized(
  PERMISSION.MANAGE_SUBSCRIPTIONS
);

// Export both the general middleware and specific middleware functions
export default isAuthorized;
