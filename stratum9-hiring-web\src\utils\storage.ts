import SecureLS from "secure-ls";

const ls = new SecureLS();

interface Storage {
  set: (key: string, data: unknown) => void;
  get: (key: string) => unknown;
  remove: (key: string) => void;
  removeAll: () => void;
  getAllKeys: () => string[];
}

const storage: Storage = {
  set: (key, data) => {
    if (typeof window !== "undefined") {
      ls.set(key, JSON.stringify(data));
    }
  },

  get: (key) => {
    if (typeof window !== "undefined") {
      const data = ls.get(key);
      if (data) {
        return data ? JSON.parse(data) : null;
      }
    }
    return null;
  },

  remove: (key) => {
    if (typeof window !== "undefined") {
      ls.remove(key);
    }
  },

  removeAll: () => {
    ls.removeAll();
    if (typeof window !== "undefined") {
      localStorage.clear();
    }
  },

  getAllKeys: () => {
    if (typeof window !== "undefined") {
      const keys: string[] = ls.getAllKeys();
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          keys.push(key);
        }
      }
      return keys;
    }
    return [];
  },
};

export default storage;
