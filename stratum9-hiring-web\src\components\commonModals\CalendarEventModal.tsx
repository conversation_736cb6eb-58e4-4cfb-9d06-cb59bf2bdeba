/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-explicit-any */
// src/components/commonModals/CalenderEventModal.tsx
"use client";
import React, { FormEventHandler, useCallback, useEffect, useRef, useState } from "react";
import { Control, FieldErrors, UseFormGetValues, UseFormSetError, UseFormSetValue } from "react-hook-form";
import { debounce } from "lodash";
import toast from "react-hot-toast";
import { useTranslations } from "next-intl";

import "../../styles/eventModal.scss";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
// import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Button from "../formElements/Button";
import {
  IGetCandidateListResponse,
  IGetInterviewsResponse,
  IGetJobListResponse,
  ScheduleInterviewFormValues,
} from "@/interfaces/interviewInterfaces";
import ReactCommonSelect from "../formElements/ReactCommonSelect";
import CommonDatePickerWrapper from "../formElements/CommonDatepicker";
import Textarea from "../formElements/Textarea";
import { FILE_EXTENSION, INTERVIEW_SCHEDULE_ROUND_TYPE, ScheduleInterviewFormSubmissionType } from "@/constants/commonConstants";
import { toastMessageError, uploadFileOnS3 } from "@/utils/helper";
import UploadBox from "../commonComponent/UploadBox";
import UploadFileIcon from "../svgComponents/UploadFileIcon";
import DeleteDarkIcon from "../svgComponents/DeleteDarkIcon";
import Select from "../formElements/Select";
import { getCandidateList } from "@/services/interviewServices";
import CommonTimePicker from "../formElements/CommonTimePicker";
import { removeAttachmentsFromS3 } from "@/services/commonService";

interface EventModalProps {
  onClose: () => void;
  handleSubmit: FormEventHandler<HTMLFormElement>;
  debouncedHandleSearchInputChange: (value: string) => void;
  setFileUrls: React.Dispatch<React.SetStateAction<string[]>>;
  fileUrls: string[];
  control: Control<ScheduleInterviewFormValues | any>;
  loading: boolean;
  errors: FieldErrors<ScheduleInterviewFormValues>;
  currentFileArrayLengthRef: React.RefObject<number>;
  interviewers: Array<{ label: string; value: number }>;
  loader: boolean;
  formType: string;
  getValues: UseFormGetValues<ScheduleInterviewFormValues>;
  setValue: UseFormSetValue<ScheduleInterviewFormValues>;
  setError: UseFormSetError<ScheduleInterviewFormValues>;
  setJobs: React.Dispatch<React.SetStateAction<Array<IGetJobListResponse>>>;
  interviewInfo: IGetInterviewsResponse | null;

  debouncedHandleJobSearchInputChange?: (value: string) => void;
  jobs?: Array<IGetJobListResponse>;
  jobLoader?: boolean;
  candidateName?: string;
}

const CalendarEventModal: React.FC<EventModalProps> = ({
  onClose,
  control,
  handleSubmit,
  loading,
  errors,
  interviewers,
  jobs,
  loader,
  debouncedHandleSearchInputChange,
  debouncedHandleJobSearchInputChange,
  setFileUrls,
  getValues,
  setValue,
  setError,
  fileUrls,
  jobLoader,
  formType,
  currentFileArrayLengthRef,
  interviewInfo,
  setJobs,
  candidateName,
}) => {
  const t = useTranslations();

  const inputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [candidateLoading, setCandidateLoading] = useState(false);
  const [candidates, setCandidates] = useState<IGetCandidateListResponse[]>([]);

  console.log("fileUrls====", fileUrls);

  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsLoading(true);
    const { files } = e.target;
    console.log("files", files);
    if (files?.length && Number(files?.[0].size) < 10854484) {
      const file = files[0];
      const uploadedUrls = [...(fileUrls || [])];

      if (file) {
        const Extension = file?.type?.split("/")[1];
        const fileNameArr = file.name.split(".");
        const filePath = `job-interviews/${fileNameArr[0]}-${new Date().getTime()}.${fileNameArr[1]}`;
        if (FILE_EXTENSION.includes(Extension?.toLowerCase())) {
          const uploadedFileUrl = (await uploadFileOnS3(file, filePath)) as string;

          uploadedUrls.push(uploadedFileUrl);
          setFileUrls((prev) => [...(prev ?? []), uploadedFileUrl]);
          currentFileArrayLengthRef.current++;
        } else {
          toastMessageError(t("invalid_file_format"));
        }
      }
    } else {
      toastMessageError(t("invalid_size_format"));
    }
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    setIsLoading(false);
  };

  const onModalClose = async () => {
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    currentFileArrayLengthRef.current = 0;

    onClose();
    fileUrls?.length && (await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify(fileUrls) }));
    setFileUrls([]);
  };

  const onHandleDelete = async (fileUrl: string) => {
    await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify(fileUrl) });
    setFileUrls((prev) => prev && prev.filter((file) => file !== fileUrl));
    currentFileArrayLengthRef.current--;
  };

  useEffect(() => {
    if (formType === ScheduleInterviewFormSubmissionType.UPDATE && interviewInfo) {
      const candidate = {
        label: interviewInfo?.candidateName,
        value: interviewInfo?.jobApplicationId,
      };
      setCandidates([candidate]);

      setJobs([{ label: interviewInfo.jobTitle, value: interviewInfo.jobId, jobId: interviewInfo.jobUniqueId }]);
    }
    if (!!candidateName) {
      setCandidates([{ label: candidateName, value: getValues("candidate") }]);
    }
  }, []);

  const getCandidates = useCallback(async (searchString: string, jobId?: number) => {
    setCandidateLoading(true);
    try {
      const response = await getCandidateList({
        searchString,
        jobId: jobId?.toString() || "",
      });

      if (response?.data?.success) {
        setCandidates(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching interviewers:", error);
    } finally {
      setCandidateLoading(false);
    }
  }, []);

  const handleCandidateSearchInputChange = (event: string) => {
    const searchString = event.trim();
    console.log("searchString", searchString);
    getCandidates(searchString, getValues("jobTitle"));
  };

  const debouncedHandleCandidateSearchInputChange = debounce(handleCandidateSearchInputChange, 1000);

  console.log("candidates", candidates);

  return (
    <>
      <div className="modal theme-modal show-modal modal-lg">
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <form onSubmit={handleSubmit}>
              <div className="modal-header secondary-header">
                <h4 className="m-0">{formType === ScheduleInterviewFormSubmissionType.UPDATE ? t("update_interview") : t("schedule_interview")}</h4>
                <div className="button-align">
                  <Button type="submit" disabled={loading} loading={loading} className="primary-btn rounded-md">
                    {formType === ScheduleInterviewFormSubmissionType.UPDATE ? t("update_interview") : t("schedule_interview")}
                  </Button>
                  <Button type="button" onClick={onModalClose} disabled={loading} className="dark-outline-btn rounded-md">
                    {t("cancel")}
                  </Button>
                </div>
              </div>
              <div className="modal-body" style={{ height: "calc(100vh - 350px)", overflowY: "auto" }}>
                <div className="row">
                  <div className="col-md-6">
                    <ReactCommonSelect
                      name="jobTitle"
                      control={control}
                      options={jobs?.length ? jobs : []}
                      isDisabled={formType === ScheduleInterviewFormSubmissionType.UPDATE || !!candidateName}
                      label={t("job_title")}
                      placeholder={t("please_select_job")}
                      onInputChange={(e) => {
                        if (debouncedHandleJobSearchInputChange) debouncedHandleJobSearchInputChange(e);
                      }}
                      onChange={(e) => {
                        setValue("jobId", e?.jobId);
                        setValue("candidate", -1);
                        setError("jobId", { type: "manual", message: "" });
                        setCandidates([]);
                        getCandidates("", e?.value);
                      }}
                      // value={formType === ScheduleInterviewFormSubmissionType.UPDATE ? jobs?.[0] : null}
                      isLoading={jobLoader}
                      errors={errors}
                    />
                  </div>
                  <div className="col-md-6">
                    <InputWrapper>
                      <InputWrapper.Label htmlFor="jobId" required>
                        {t("job_id")}
                      </InputWrapper.Label>
                      <Textbox className="form-control" disabled control={control} name="jobId" type="text" placeholder={t("job_id_desc")} />
                      <InputWrapper.Error message={errors?.jobId?.message || ""} />
                    </InputWrapper>
                  </div>
                </div>

                <InputWrapper>
                  <InputWrapper.Label htmlFor="eventTitle" required>
                    {t("title")}
                  </InputWrapper.Label>
                  <Textbox className="form-control" control={control} name="eventTitle" type="text" placeholder={t("title_desc")} />
                  <InputWrapper.Error message={errors?.eventTitle?.message || ""} />
                </InputWrapper>

                <ReactCommonSelect
                  name="candidate"
                  control={control}
                  options={candidates?.length ? candidates : []}
                  isDisabled={formType === ScheduleInterviewFormSubmissionType.UPDATE || !!candidateName}
                  label={t("candidate")}
                  // value={formType === ScheduleInterviewFormSubmissionType.UPDATE ? candidates[0] : null}
                  placeholder={t("candidate_placeholder")}
                  onInputChange={(e) => debouncedHandleCandidateSearchInputChange(e)}
                  isLoading={candidateLoading}
                  errors={errors}
                />

                <ReactCommonSelect
                  name="interviewer"
                  control={control}
                  options={interviewers?.length ? interviewers : []}
                  onChange={(val) => {
                    if (!val) {
                      console.log("val", val);
                      setValue("interviewer", -1);
                    }
                  }}
                  label={t("interviewer")}
                  placeholder={t("please_select_interviewer")}
                  onInputChange={(e) => debouncedHandleSearchInputChange(e)}
                  isLoading={loader}
                  errors={errors}
                />

                <div className="row">
                  <div className="col-md-6">
                    <InputWrapper>
                      <InputWrapper.Label htmlFor="interviewType" required>
                        {t("interview_type")}
                      </InputWrapper.Label>
                      <Select
                        options={INTERVIEW_SCHEDULE_ROUND_TYPE}
                        className="form-control"
                        control={control}
                        name="interviewType"
                        placeholder={t("please_select_interview_type")}
                      />
                      <InputWrapper.Error message={errors?.interviewType?.message || ""} />
                    </InputWrapper>
                  </div>
                  <div className="col-md-6">
                    <CommonDatePickerWrapper
                      control={control}
                      name="date"
                      label={t("interview_date")}
                      placeholder={t("please_select_date")}
                      error={errors?.date}
                      dateRange={[]}
                      required
                      defaultDate={new Date()}
                    />
                  </div>
                </div>
                <div className="row">
                  <div className="col-md-6">
                    <InputWrapper>
                      <InputWrapper.Label htmlFor="startTime" required>
                        {t("start_time")}
                      </InputWrapper.Label>
                      <CommonTimePicker name="startTime" className="form-control" control={control} />
                      <InputWrapper.Error message={errors?.startTime?.message || ""} />
                    </InputWrapper>
                  </div>
                  <div className="col-md-6">
                    <InputWrapper>
                      <InputWrapper.Label htmlFor="endTime" required>
                        {t("end_time")}
                      </InputWrapper.Label>
                      <CommonTimePicker name="endTime" className="form-control" control={control} />
                      <InputWrapper.Error message={errors?.endTime?.message || ""} />
                    </InputWrapper>
                  </div>
                  <InputWrapper>
                    <InputWrapper.Label htmlFor="attachments">{t("attachments")}</InputWrapper.Label>
                    <UploadBox
                      UploadBoxClassName="upload-card-sm"
                      onChange={(e) => {
                        if (currentFileArrayLengthRef.current >= 3) {
                          toast.dismiss();
                          toastMessageError(t("max_files_limit_msg"));
                        } else {
                          onFileChange(e);
                        }
                      }}
                      inputRef={inputRef}
                      isLoading={isLoading}
                    />
                    {/* uploaded-item */}
                    {fileUrls?.length
                      ? fileUrls.map((file) => {
                          const fileName = file?.split("/").pop()?.split("-")[1]?.split(".")[0];
                          const fileExt = file?.split(".").pop();

                          return (
                            <div className="uploded-item upload-card-sm" key={file}>
                              <div className="item-name">
                                <UploadFileIcon />
                                <p>
                                  {fileName
                                    ? fileName.length > 15
                                      ? `${fileName.slice(0, 15)}...${fileName.slice(fileName.length - 3, fileName.length)}.${fileExt}`
                                      : `${fileName}.${fileExt}`
                                    : `document.${fileExt}`}
                                </p>
                              </div>
                              <DeleteDarkIcon className="delete-item" onClick={() => !isLoading && onHandleDelete(file)} />
                            </div>
                          );
                        })
                      : null}
                  </InputWrapper>
                  <InputWrapper>
                    <InputWrapper.Label htmlFor="description">{t("additional_info")}</InputWrapper.Label>
                    <Textarea className="form-control" control={control} name="description" rows={5} placeholder={t("additional_info_desc_")} />
                    <InputWrapper.Error message={errors?.description?.message || ""} />
                  </InputWrapper>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(CalendarEventModal);
