"use client";

// Internal libraries
import React, { useEffect, useState } from "react";

// External libraries
import toast from "react-hot-toast";
import { redirect, useRouter, useSearchParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

// Components
import Loader from "@/components/loader/Loader";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import Button from "@/components/formElements/Button";
import CognitiveAbilitiesIcon from "@/components/svgComponents/CognitiveAbilitiesIcon";
import EmotionsIcon from "@/components/svgComponents/EmotionsIcon";
import MentalityIcon from "@/components/svgComponents/MentalityIcon";
import PersonalHealthIcon from "@/components/svgComponents/PersonalHealthIcon";
import SocialInteractionIcon from "@/components/svgComponents/SocialInteractionIcon";

// Services
import { getAllSkills } from "@/services/jobRequirements/skillsService";

// Redux slices and constants
import { setSkillsData } from "@/redux/slices/jobSkillsSlice";
import {
  fetchSkillsStart,
  fetchSkillsSuccess,
  fetchSkillsFailure,
  selectAllSkills,
  selectSkillsLoading,
  selectSkillsError,
} from "@/redux/slices/allSkillsSlice";
import { selectRoleSpecificSkills, selectCultureSpecificSkills } from "@/redux/slices/jobSkillsSlice";
import { ISkillCategory, ISkillData, ISkillItem } from "@/interfaces/jobRequirementesInterfaces";
import { SKILL_CONSTANTS } from "@/constants/commonConstants";
import { SKILL_CATEGORY, SKILL_TYPE } from "@/constants/jobRequirementConstant";

// CSS
import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";
import ROUTES from "@/constants/routes";
import SkillDetails from "@/components/commonModals/SkillDetails";

function EditSkills() {
  const router = useRouter();
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const skillType = searchParams?.get("skillType");

  // Get skills data from Redux store
  const skillCategories = useSelector(selectAllSkills);
  const isLoading = useSelector(selectSkillsLoading);
  const error = useSelector(selectSkillsError);

  // Get role-specific and culture-specific skills from Redux store
  const roleSpecificSkills = useSelector(selectRoleSpecificSkills);
  const cultureSpecificSkills = useSelector(selectCultureSpecificSkills);

  const [editLoading, setEditLoading] = useState(false);
  // Local state for selected skills - track by category and skill type
  const [selectedSkills, setSelectedSkills] = useState<{ [key: string]: number[] }>({});
  // Store already selected skills by type
  const [alreadySelectedRoleSkillIds, setAlreadySelectedRoleSkillIds] = useState<string[]>([]);
  const [alreadySelectedCultureSkillIds, setAlreadySelectedCultureSkillIds] = useState<string[]>([]);
  const [skillDetails, setSkillDetails] = useState<ISkillItem | null>(null);

  // State for selected skill type (role or culture) - initialize with prop if provided
  const [selectedSkillType, setSelectedSkillType] = useState<string>(skillType || SKILL_TYPE.ROLE);

  // Keep track of deselected skills from the already selected ones (for Redux)
  const [deselectedSkills, setDeselectedSkills] = useState<{ [key: string]: number[] }>({
    role: [],
    culture: [],
  });

  const t = useTranslations();
  /**
   * Redirects to career-based skills page if required data is missing
   *
   * @effect
   * @description Checks if the skill type is valid and if necessary Redux data is available
   *              Redirects to the career-based skills page if any validation fails
   *              Runs only once on component mount due to empty dependency array
   */

  useEffect(() => {
    if (!roleSpecificSkills || !cultureSpecificSkills || roleSpecificSkills.length === 0 || cultureSpecificSkills.length === 0)
      redirect(`${ROUTES.JOBS.CAREER_BASED_SKILLS}`);
  }, []);

  /**
   * Fetches skills data from the API and updates Redux store
   *
   * @async
   * @function fetchSkillsData
   * @returns {Promise<void>}
   * @throws {Error} If API request fails
   * @description Makes an API call to fetch all skills data and updates Redux store
   *              with the response. Handles both success and error cases.
   */
  const fetchSkillsData = async () => {
    try {
      dispatch(fetchSkillsStart());
      const response = await getAllSkills();
      if (response.data && response.data.success) {
        dispatch(fetchSkillsSuccess(response.data.data as ISkillCategory[]));
      } else {
        dispatch(fetchSkillsFailure(response.data?.message as string));
      }
    } catch (error: unknown) {
      console.error(t("error_fetching_skills"), error);
      dispatch(fetchSkillsFailure((error as Error)?.message || t("error_fetching_skills")));
    }
  };

  /**
   * Initializes skills data when component mounts
   *
   * @function
   * @description Checks if skills data is already loaded and fetches it if not
   *              Ensures the component has required data on initial render
   */
  useEffect(() => {
    if (skillCategories.length === 0 && !isLoading) {
      fetchSkillsData();
    }
  }, []);

  /**
   * Updates role-specific skills when they change in Redux store
   *
   * @function
   * @description Synchronizes role-specific skills from Redux store with component state
   *              Filters out any undefined skill IDs
   * @param {ISkillItem[]} roleSpecificSkills - Array of role-specific skills from Redux store
   */
  useEffect(() => {
    if (roleSpecificSkills && roleSpecificSkills.length > 0) {
      const roleSkillIds = roleSpecificSkills.map((skill) => skill.id).filter((id): id is string => id !== undefined);
      setAlreadySelectedRoleSkillIds(roleSkillIds);
    }
  }, [roleSpecificSkills]);

  /**
   * Updates culture-specific skills when they change in Redux store
   *
   * @function
   * @description Synchronizes culture-specific skills from Redux store with component state
   *              Filters out any undefined skill IDs
   * @param {ISkillItem[]} cultureSpecificSkills - Array of culture-specific skills from Redux store
   */
  useEffect(() => {
    if (cultureSpecificSkills && cultureSpecificSkills.length > 0) {
      const cultureSkillIds = cultureSpecificSkills.map((skill) => skill.id).filter((id): id is string => id !== undefined);
      setAlreadySelectedCultureSkillIds(cultureSkillIds);
    }
  }, [cultureSpecificSkills]);

  /**
   * Toggles skill selection state
   *
   * @function toggleSkillSelection
   * @param {string} categoryType - The category type of the skill (e.g., 'Mentality', 'Cognitive Abilities')
   * @param {number} skillId - The ID of the skill to toggle
   * @description Handles both new selections and deselections of skills
   *              Manages both local selections and deselections from Redux store
   */
  const toggleSkillSelection = (categoryType: string, skillId: number) => {
    // Check if this skill is already selected in Redux
    const isRoleSelected = alreadySelectedRoleSkillIds.includes(String(skillId));
    const isCultureSelected = alreadySelectedCultureSkillIds.includes(String(skillId));
    const isAlreadySelected = selectedSkillType === SKILL_TYPE.ROLE ? isRoleSelected : isCultureSelected;

    // Handle deselection of already selected skills from Redux
    if (isAlreadySelected) {
      // If it's an already selected skill from Redux, we need to mark it as deselected
      setDeselectedSkills((prev) => {
        const skillsType = selectedSkillType === SKILL_TYPE.ROLE ? SKILL_TYPE.ROLE : SKILL_TYPE.CULTURE;
        const currentDeselected = prev[skillsType] || [];

        // If already in deselected list, remove it (toggle back to selected)
        if (currentDeselected.includes(skillId)) {
          return {
            ...prev,
            [skillsType]: currentDeselected.filter((id) => id !== skillId),
          };
        } else {
          // Otherwise add to deselected list
          return {
            ...prev,
            [skillsType]: [...currentDeselected, skillId],
          };
        }
      });
      return;
    }

    // Handle normal selection/deselection for non-Redux skills
    setSelectedSkills((prev) => {
      const categorySkills = prev[categoryType] || [];
      const isCurrentlySelected = categorySkills.includes(skillId);

      // If already selected in our local state, deselect it
      if (isCurrentlySelected) {
        return {
          ...prev,
          [categoryType]: categorySkills.filter((id) => id !== skillId),
        };
      } else {
        // Otherwise select it (if not disabled)
        return {
          ...prev,
          [categoryType]: [...categorySkills, skillId],
        };
      }
    });
  };

  /**
   * Displays detailed information about a selected skill
   *
   * @function showSkillDetails
   * @param {ISkillItem} skill - The skill object to display details for
   * @description Updates the state with the selected skill's details
   */
  const showSkillDetails = (skill: ISkillItem) => {
    setSkillDetails(skill);
  };

  /**
   * Returns the appropriate icon component based on skill category
   *
   * @function getCategoryIcon
   * @param {string} type - The category type (e.g., 'Mentality', 'Cognitive Abilities')
   * @returns {React.ReactNode} The corresponding icon component
   * @description Maps skill categories to their respective icon components
   */
  const getCategoryIcon = (type: string) => {
    switch (type) {
      case SKILL_CATEGORY.Mentality:
        return <MentalityIcon />;
      case SKILL_CATEGORY.Cognitive_Abilities:
        return <CognitiveAbilitiesIcon />;
      case SKILL_CATEGORY.Mastery_Of_Emotions:
        return <EmotionsIcon />;
      case SKILL_CATEGORY.Social_Interaction:
        return <SocialInteractionIcon />;
      case SKILL_CATEGORY.Personal_Health:
        return <PersonalHealthIcon />;
      default:
        return null;
    }
  };

  /**
   * Saves selected skills after validation
   *
   * @function handleSaveChanges
   * @description Validates selected skills count and saves changes to Redux store
   *              Shows appropriate success/error messages based on validation
   */
  const handleSaveChanges = () => {
    setEditLoading(true);
    // Calculate total selected skills considering both new selections and
    // already selected ones that haven't been deselected
    let totalSelected = 0;
    const skillsToSave: ISkillData[] = [];

    // Count skills from local state selections and collect them for saving
    Object.entries(selectedSkills).forEach(([category, skillIds]) => {
      totalSelected += skillIds.length;

      // Find the skill details to save from category and ID
      skillIds.forEach((skillId) => {
        const categoryData = skillCategories.find((cat: ISkillCategory) => cat.type === category);
        if (categoryData) {
          const skillData = categoryData.items.find((item: ISkillItem) => item.id === skillId);
          if (skillData) {
            skillsToSave.push({
              id: String(skillData.id),
              name: skillData.title,
              description: skillData.description || skillData.short_description,
            });
          }
        }
      });
    });

    if (selectedSkillType === SKILL_TYPE.ROLE) {
      // Get deselected role skills
      const deselectedRoleSkills = deselectedSkills.role || [];

      // Add already selected skills that haven't been deselected
      alreadySelectedRoleSkillIds.forEach((idStr) => {
        const id = parseInt(idStr, 10);
        // Skip if this skill is in our deselected list
        if (deselectedRoleSkills.includes(id)) {
          return;
        }
        // Skip if already counted in our selections
        const isAlreadyCounted = Object.values(selectedSkills).some((categorySkills) => categorySkills.includes(id));
        if (!isAlreadyCounted) {
          totalSelected++;

          // Find this already selected skill in skillCategories and add to skillsToSave
          for (const category of skillCategories) {
            const skillData = category.items.find((item: ISkillItem) => item.id === id);
            if (skillData) {
              skillsToSave.push({
                id: String(skillData.id),
                name: skillData.title,
                description: skillData.description || skillData.short_description,
              });
              break;
            }
          }
        }
      });

      // Validate based on selected skill type
      if (totalSelected !== SKILL_CONSTANTS.REQUIRED_ROLE_SKILLS) {
        toast.error(`Please select exactly ${SKILL_CONSTANTS.REQUIRED_ROLE_SKILLS} role specific skills. You have selected ${totalSelected}.`);
        setEditLoading(false);
        return;
      }

      // Save role-specific skills to Redux
      dispatch(setSkillsData({ roleSpecificSkills: skillsToSave }));
    } else if (selectedSkillType === SKILL_TYPE.CULTURE) {
      // Get deselected culture skills
      const deselectedCultureSkills = deselectedSkills.culture || [];

      // Add already selected skills that haven't been deselected
      alreadySelectedCultureSkillIds.forEach((idStr) => {
        const id = parseInt(idStr, 10);
        // Skip if this skill is in our deselected list
        if (deselectedCultureSkills.includes(id)) {
          return;
        }
        // Skip if already counted in our selections
        const isAlreadyCounted = Object.values(selectedSkills).some((categorySkills) => categorySkills.includes(id));
        if (!isAlreadyCounted) {
          totalSelected++;

          // Find this already selected skill in skillCategories and add to skillsToSave
          for (const category of skillCategories) {
            const skillData = category.items.find((item: ISkillItem) => item.id === id);
            if (skillData) {
              skillsToSave.push({
                id: String(skillData.id),
                name: skillData.title,
                description: skillData.description || skillData.short_description,
              });
              break;
            }
          }
        }
      });

      // Validate for culture skills
      if (totalSelected !== SKILL_CONSTANTS.REQUIRED_CULTURE_SKILLS) {
        toast.error(`Please select exactly ${SKILL_CONSTANTS.REQUIRED_CULTURE_SKILLS} culture specific skills. You have selected ${totalSelected}.`);
        setEditLoading(false);
        return;
      }

      // Save culture-specific skills to Redux
      dispatch(setSkillsData({ cultureSpecificSkills: skillsToSave }));
    }

    // Show success message
    toast.success(`Successfully saved ${selectedSkillType} specific skills.`);
    setEditLoading(false);
    router.push(ROUTES.JOBS.ROLE_BASED_SKILLS);
  };

  // useEffect(() => {
  //   if (selectedSkillType === SKILL_TYPE.ROLE) {
  //     setSelectedSkillType("Role-Specific Skills");
  //   } else {
  //     setSelectedSkillType("Culture-Specific Skills");
  //   }
  // }, [selectedSkillType]);

  return (
    <div className={style.job_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon />
                {t("add_change_performance_based_skills")}
              </h2>
              <span className={`${style.job_info} ${style.text_xs}`}>
                <strong> {t("note")} </strong> {t("double_click_to_edit")}
              </span>
            </div>
          </div>
        </div>

        {isLoading && (
          <div className="inner-section skills-skeleton-wrapper">
            {/* Skeleton for skill categories layout matching the image */}
            <div className="row g-4">
              {/* Mentality Category */}
              <div className="col">
                <div className="assignments-card skeleton-card">
                  <div className="assignments-name">
                    <div className="skeleton-circle skeleton-icon"></div>
                    <div className="skeleton-text skeleton-title"></div>
                  </div>
                  <ul className="assignments-list">
                    {[1, 2, 3, 4, 5, 6, 7].map((item) => (
                      <li key={`skeleton-skill-1-${item}`} className={`skeleton-skill ${item % 3 === 0 ? "skeleton-selected" : ""}`}>
                        <div className="skeleton-text"></div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Cognitive Abilities Category */}
              <div className="col">
                <div className="assignments-card skeleton-card">
                  <div className="assignments-name">
                    <div className="skeleton-circle skeleton-icon"></div>
                    <div className="skeleton-text skeleton-title"></div>
                  </div>
                  <ul className="assignments-list">
                    {[1, 2, 3, 4, 5].map((item) => (
                      <li key={`skeleton-skill-2-${item}`} className={`skeleton-skill ${item % 4 === 0 ? "skeleton-selected" : ""}`}>
                        <div className="skeleton-text"></div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Mastery of Emotions Category */}
              <div className="col">
                <div className="assignments-card skeleton-card">
                  <div className="assignments-name">
                    <div className="skeleton-circle skeleton-icon"></div>
                    <div className="skeleton-text skeleton-title"></div>
                  </div>
                  <ul className="assignments-list">
                    {[1, 2, 3, 4].map((item) => (
                      <li key={`skeleton-skill-3-${item}`} className={`skeleton-skill ${item % 2 === 0 ? "skeleton-selected" : ""}`}>
                        <div className="skeleton-text"></div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Social Interaction Category */}
              <div className="col">
                <div className="assignments-card skeleton-card">
                  <div className="assignments-name">
                    <div className="skeleton-circle skeleton-icon"></div>
                    <div className="skeleton-text skeleton-title"></div>
                  </div>
                  <ul className="assignments-list">
                    {[1, 2, 3, 4, 5, 6].map((item) => (
                      <li key={`skeleton-skill-4-${item}`} className={`skeleton-skill ${item % 3 === 0 ? "skeleton-selected" : ""}`}>
                        <div className="skeleton-text"></div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Personal Health Category */}
              <div className="col">
                <div className="assignments-card skeleton-card">
                  <div className="assignments-name">
                    <div className="skeleton-circle skeleton-icon"></div>
                    <div className="skeleton-text skeleton-title"></div>
                  </div>
                  <ul className="assignments-list">
                    {[1, 2, 3, 4].map((item) => (
                      <li key={`skeleton-skill-5-${item}`} className={`skeleton-skill ${item % 2 === 0 ? "skeleton-selected" : ""}`}>
                        <div className="skeleton-text"></div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {!isLoading && !error && skillCategories.length > 0 && (
          <div className="inner-section">
            <div className={style.skills_info_box}>
              <ul>
                <li>
                  <span className={style.selecting} />
                  {t("your_selection")}
                </li>
                <li>
                  <span className={style.selected} />
                  {t("already_selected")}
                </li>
                <li>
                  <span className={style.selection} />
                  {t("available_for_selection")}
                </li>
              </ul>
              <ul className={style.skills_tab}>
                <li
                  className={selectedSkillType === SKILL_TYPE.ROLE ? style.active : ""}
                  onClick={() => {
                    setSelectedSkills({});
                    setDeselectedSkills({});
                    setSelectedSkillType(SKILL_TYPE.ROLE);
                  }}
                >
                  {t("role_specific_skills")}
                </li>
                <li
                  className={selectedSkillType === SKILL_TYPE.CULTURE ? style.active : ""}
                  onClick={() => {
                    setSelectedSkills({});
                    setDeselectedSkills({});
                    setSelectedSkillType(SKILL_TYPE.CULTURE);
                  }}
                >
                  {t("culture_specific_skills")}
                </li>
              </ul>
            </div>

            <div className="row g-4">
              {skillCategories.map((category: ISkillCategory) => (
                <div className="col" key={category.type}>
                  <div className="assignments-card">
                    <div className="assignments-name">
                      {getCategoryIcon(category.type)}
                      <h4>{category.type}</h4>
                    </div>
                    <ul className="assignments-list">
                      {category.items.map((skill) => {
                        const isSelected = (selectedSkills[category.type] || []).includes(skill.id);

                        // Check if this skill is already selected (from Redux)
                        const isRoleSelected = alreadySelectedRoleSkillIds.includes(String(skill.id));
                        const isCultureSelected = alreadySelectedCultureSkillIds.includes(String(skill.id));

                        // Check if this skill is in our deselected list
                        const skillTypeForDeselection = selectedSkillType === SKILL_TYPE.ROLE ? SKILL_TYPE.ROLE : SKILL_TYPE.CULTURE;
                        const isDeselected = deselectedSkills[skillTypeForDeselection]?.includes(skill.id) || false;

                        // Check if this skill is already selected in Redux for the current skill type
                        // but not deselected by the user
                        const isAlreadySelected =
                          selectedSkillType === SKILL_TYPE.ROLE ? isRoleSelected && !isDeselected : isCultureSelected && !isDeselected;

                        // Determine if the skill should be disabled
                        // Only disable if it's selected in the other skill type
                        const shouldDisable =
                          (selectedSkillType === SKILL_TYPE.ROLE && isCultureSelected) ||
                          (selectedSkillType === SKILL_TYPE.CULTURE && isRoleSelected);

                        return (
                          <li
                            key={skill.id}
                            className={`
                              ${isSelected ? "selecting" : isAlreadySelected ? "selected" : ""}
                              ${shouldDisable ? "disabled-skill" : ""}
                            `}
                            onClick={() => !shouldDisable && toggleSkillSelection(category.type, skill.id)}
                            onDoubleClick={() => showSkillDetails(skill)}
                            style={{ cursor: shouldDisable ? "not-allowed" : "pointer", opacity: shouldDisable && !isAlreadySelected ? 0.5 : 1 }}
                          >
                            {skill.title}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              ))}
            </div>

            {skillDetails && <SkillDetails skillDetails={skillDetails} onClose={() => setSkillDetails(null)} />}
          </div>
        )}

        <div className="dotted-border my-4" />
        <div className="button-align py-4">
          <Button className="primary-btn rounded-md minWidth" onClick={handleSaveChanges} disabled={editLoading}>
            {t("Save")} {editLoading && <Loader />}
          </Button>
          <Button className="dark-outline-btn rounded-md minWidth" onClick={() => router.back()} disabled={editLoading}>
            {t("Cancel")}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default EditSkills;
