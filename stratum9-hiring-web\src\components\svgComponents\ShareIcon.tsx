import React from "react";

function ShareIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="20" height="20" viewBox="0 0 22 26" fill="none">
      <path
        d="M17.6504 0.598633C19.9387 0.598633 21.8008 2.46074 21.8008 4.74902C21.8005 7.03708 19.9385 8.89844 17.6504 8.89844C16.4756 8.89832 15.4145 8.4059 14.6592 7.61914L8.23047 11.6006C8.38823 12.0383 8.47655 12.5094 8.47656 13.001C8.47656 13.4921 8.38799 13.9628 8.23047 14.4004L14.6592 18.3818C15.4145 17.5954 16.4759 17.1046 17.6504 17.1045C19.9387 17.1045 21.8008 18.9656 21.8008 21.2539C21.8007 23.5419 19.9386 25.4033 17.6504 25.4033C15.3624 25.4031 13.5011 23.5418 13.501 21.2539C13.501 20.762 13.5893 20.2906 13.7471 19.8525L7.31738 15.8721C6.56208 16.6586 5.50166 17.1503 4.32715 17.1504C2.03887 17.1504 0.176758 15.2893 0.176758 13.001C0.176835 10.713 2.03891 8.85156 4.32715 8.85156C5.50134 8.85164 6.56213 9.34293 7.31738 10.1289L13.7461 6.14844C13.5887 5.71091 13.501 5.24009 13.501 4.74902C13.501 2.46089 15.3623 0.598869 17.6504 0.598633ZM17.6504 18.835C16.3172 18.8352 15.2324 19.9207 15.2324 21.2539C15.2326 22.587 16.3173 23.6716 17.6504 23.6719C18.9837 23.6719 20.0691 22.5871 20.0693 21.2539C20.0693 19.9205 18.9838 18.835 17.6504 18.835ZM4.32715 10.583C2.9936 10.583 1.90828 11.6677 1.9082 13.001C1.9082 14.3344 2.99356 15.4199 4.32715 15.4199C5.6604 15.4198 6.74512 14.3343 6.74512 13.001C6.74504 11.6677 5.66035 10.5832 4.32715 10.583ZM17.6504 2.33008C16.3172 2.33031 15.2324 3.41577 15.2324 4.74902C15.2326 6.08214 16.3173 7.16676 17.6504 7.16699C18.9837 7.16699 20.0692 6.08229 20.0693 4.74902C20.0693 3.41563 18.9838 2.33008 17.6504 2.33008Z"
        stroke="#436EB6"
        strokeWidth="0.266667"
      />
    </svg>
  );
}

export default ShareIcon;
