import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

interface SkillListSkeletonProps {
  count?: number;
}

export const SkillListSkeleton = ({ count = 1 }: SkillListSkeletonProps) => {
  // Generate random widths between 70% and 100% for each skeleton
  const getRandomWidth = () => Math.floor(Math.random() * 31) + 70;

  return (
    <div className="role-list d-flex flex-wrap">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="role-item">
          <Skeleton height={70} width={`${getRandomWidth()}%`} borderRadius={24} />
        </div>
      ))}
    </div>
  );
};
