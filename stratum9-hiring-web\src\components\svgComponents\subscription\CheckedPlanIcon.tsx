import React from "react";

function CheckedPlanIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="33" height="32" viewBox="0 0 33 32" fill="none">
      <path
        d="M16.5 4C14.1266 4 11.8066 4.70379 9.83316 6.02236C7.85977 7.34094 6.3217 9.21508 5.41345 11.4078C4.5052 13.6005 4.26756 16.0133 4.73058 18.3411C5.1936 20.6689 6.33649 22.807 8.01472 24.4853C9.69295 26.1635 11.8311 27.3064 14.1589 27.7694C16.4867 28.2324 18.8995 27.9948 21.0922 27.0865C23.2849 26.1783 25.1591 24.6402 26.4776 22.6668C27.7962 20.6934 28.5 18.3734 28.5 16C28.4966 12.8184 27.2313 9.76814 24.9816 7.51843C22.7319 5.26872 19.6816 4.00336 16.5 4ZM21.7685 13.8838L15.3069 20.3454C15.2212 20.4312 15.1194 20.4993 15.0073 20.5457C14.8953 20.5922 14.7752 20.6161 14.6538 20.6161C14.5325 20.6161 14.4124 20.5922 14.3004 20.5457C14.1883 20.4993 14.0865 20.4312 14.0008 20.3454L11.2315 17.5762C11.0583 17.4029 10.961 17.168 10.961 16.9231C10.961 16.6781 11.0583 16.4432 11.2315 16.27C11.4047 16.0968 11.6397 15.9995 11.8846 15.9995C12.1296 15.9995 12.3645 16.0968 12.5377 16.27L14.6538 18.3873L20.4623 12.5777C20.5481 12.4919 20.6499 12.4239 20.7619 12.3775C20.874 12.3311 20.9941 12.3072 21.1154 12.3072C21.2367 12.3072 21.3568 12.3311 21.4688 12.3775C21.5809 12.4239 21.6827 12.4919 21.7685 12.5777C21.8542 12.6635 21.9223 12.7653 21.9687 12.8773C22.0151 12.9894 22.039 13.1095 22.039 13.2308C22.039 13.3521 22.0151 13.4722 21.9687 13.5842C21.9223 13.6963 21.8542 13.7981 21.7685 13.8838Z"
        fill="#436EB6"
      />
    </svg>
  );
}

export default CheckedPlanIcon;
