import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  <PERSON>in<PERSON>ol<PERSON>n,
  OneToMany,
} from "typeorm";
import DepartmentModel from "./departments";
import InterviewModel from "./interview";
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
export enum EmploymentType {
  FULL_TIME = "full_time",
  CONTRACT = "contract",
  INTERNSHIP = "internship",
  FREELANCE = "freelance",
  PART_TIME = "part_time",
}

export enum LocationType {
  ON_SITE = "onsite",
  REMOTE = "remote",
  HYBRID = "hybrid",
}

export enum SalaryCycle {
  HOURLY = "per hour",
  MONTHLY = "per month",
  YEARLY = "per annum",
}

export enum ToneStyle {
  ProfessionalFormal = "Professional_Formal",
  ConversationalApproachable = "Conversational_Approachable",
  BoldEnergetic = "Bold_Energetic",
  InspirationalMissionDriven = "Inspirational_Mission-Driven",
  TechnicalPrecise = "Technical_Precise",
  CreativeFun = "Creative_Fun",
  InclusiveHumanCentered = "Inclusive_Human-Centered",
  MinimalistStraightforward = "Minimalist_Straightforward",
}

export enum HiringType {
  INTERNAL = "Internal",
  EXTERNAL = "External",
}

export enum ExperienceLevel {
  General = "General",
  SpecializedExpert = "Specialized Expert",
  ManagerialExecutiveLevel = "Managerial/Executive Level",
  SeniorExperiencedProfessional = "Senior/Experienced Professional",
  MidLevelProfessional = "Mid-Level Professional",
  EntryLevelPosition = "Entry-Level Position",
  NoExperienceNecessary = "No experience necessary",
}

@Entity("jobs")
export class JobsModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "job_id", unique: true, nullable: true })
  jobId: string;

  @Column({ name: "job_detail_file", type: "text", nullable: true })
  jobDetailFile: string;

  @Column({ name: "user_id" })
  userId: number;

  @Column({ name: "org_id" })
  orgId: number;

  @Column()
  departmentId: number;

  @Column({
    name: "hiring_type",
    type: "enum",
    enum: HiringType,
    nullable: false,
  })
  hiringType: HiringType;

  @Column({ name: "title", length: 50, nullable: false })
  title: string;

  @Column({
    name: "employment_type",
    type: "enum",
    enum: EmploymentType,
    nullable: false,
  })
  employmentType: EmploymentType;

  @Column({ name: "salary_range", length: 50, nullable: false })
  salaryRange: string;

  @Column({
    name: "salary_cycle",
    type: "enum",
    enum: SalaryCycle,
    nullable: false,
  })
  salaryCycle: SalaryCycle;

  @Column({
    name: "location_type",
    type: "enum",
    enum: LocationType,
    nullable: false,
  })
  locationType: LocationType;

  @Column({ name: "location", length: 50, nullable: false })
  location: string;

  @Column({ name: "state", length: 50, nullable: false })
  state: string;

  @Column({ name: "city", length: 50, nullable: false })
  city: string;

  @Column({ name: "role_overview", length: 1500, nullable: false })
  roleOverview: string;

  @Column({
    name: "experience_level",
    nullable: true,
    type: "enum",
    enum: ExperienceLevel,
  })
  experienceLevel: ExperienceLevel;

  @Column({ name: "responsibilities", length: 1500, nullable: false })
  responsibilities: string;

  @Column({ name: "educations_requirement", length: 1500, nullable: false })
  educationsRequirement: string;

  @Column({
    name: "skills_and_software_expertise",
    length: 1500,
    nullable: false,
  })
  skillsAndSoftwareExpertise: string;

  @Column({ name: "certifications", length: 1500, nullable: true })
  certifications: string;

  @Column({ name: "experience_required", length: 1500, nullable: false })
  experienceRequired: string;

  @Column({ name: "ideal_candidate_traits", length: 1500, nullable: false })
  idealCandidateTraits: string;

  @Column({ name: "about_company", length: 1500, nullable: false })
  aboutCompany: string;

  @Column({ name: "perks_benefits", length: 1500, nullable: true })
  perksBenefits: string;

  @Column({
    name: "tone_style",
    type: "enum",
    enum: ToneStyle,
    nullable: false,
  })
  toneStyle: ToneStyle;

  @Column({ name: "additional_info", length: 1500, nullable: true })
  additionalInfo: string;

  @Column({ name: "show_compliance", type: "boolean", default: false })
  showCompliance: boolean;

  @Column({ name: "compliance_statement", type: "text", nullable: false })
  complianceStatement: string;

  @Column({ name: "final_job_description", type: "text", nullable: true })
  finalJobDescription: string;

  @Column({ name: "final_job_description_html", type: "text", nullable: false })
  finalJobDescriptionHtml: string;

  @Column({ name: "jd_link", type: "text", nullable: true })
  jdLink: string;

  @Column({ name: "is_active", type: "boolean", default: true })
  isActive: boolean;

  @OneToMany(() => InterviewModel, (interview) => interview.job)
  interviews: InterviewModel[];

  @ManyToOne(() => DepartmentModel)
  @JoinColumn({ name: "department_id" })
  department: DepartmentModel;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_ts",
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
  })
  updatedTs: Date;
}
