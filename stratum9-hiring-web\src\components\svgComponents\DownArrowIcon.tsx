import React from "react";

type DownArrowIconProps = {
  className?: string;
};

function DownArrowIcon({ className }: DownArrowIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none" className={className}>
      <g clipPath="url(#clip0_9881_3583)">
        <path
          d="M7.34102 10.9921C7.09012 10.9921 6.83924 10.8963 6.64794 10.7051L0.628262 4.68536C0.245332 4.30243 0.245332 3.68158 0.628262 3.2988C1.01104 2.91602 1.63177 2.91602 2.01473 3.2988L7.34102 8.6254L12.6673 3.29899C13.0503 2.91621 13.6709 2.91621 14.0537 3.29899C14.4368 3.68176 14.4368 4.30262 14.0537 4.68555L8.0341 10.7053C7.84272 10.8965 7.59184 10.9921 7.34102 10.9921Z"
          fill="#333333"
        />
      </g>
      <defs>
        <clipPath id="clip0_9881_3583">
          <rect width="14" height="14" fill="white" transform="translate(0.333252)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default DownArrowIcon;
