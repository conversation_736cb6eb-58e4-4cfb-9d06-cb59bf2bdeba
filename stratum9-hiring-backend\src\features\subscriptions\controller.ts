import { Request, Response } from "express";
import SubscriptionServices from "./services";
import envConfig from "../../config/envConfig";

/**
 * Get current subscription for the authenticated user's organization
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getCurrentSubscription = async (req: Request, res: Response) => {
  try {
    const { orgId } = req;
    const data = await SubscriptionServices.getCurrentSubscription(orgId);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get all available subscription plans with pricing options
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getAllPlans = async (req: Request, res: Response) => {
  try {
    const data = await SubscriptionServices.getAllPlans();
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Cancel the current subscription for the authenticated user's organization
 * After cancellation, the subscription will be downgraded to the free plan
 * at the end of the current billing period
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const cancelSubscription = async (req: Request, res: Response) => {
  try {
    const { orgId } = req;
    const data = await SubscriptionServices.cancelSubscription(orgId);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Handle Stripe webhook events for subscription lifecycle
 * Processes events like invoice.payment_succeeded, invoice.payment_failed,
 * customer.subscription.created, customer.subscription.updated, and
 * customer.subscription.deleted
 *
 * @param req - The HTTP request object containing the Stripe event
 * @param res - The HTTP response object
 */
export const handleStripeWebhook = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    // Use the validated Stripe event from our middleware
    // Check if req.body is a Buffer and parse it if needed
    let event = req.body;

    // Debug the incoming payload
    console.log("Incoming webhook payload type:", typeof event);

    // If it's a Buffer, parse it to JSON
    if (Buffer.isBuffer(event)) {
      console.log("Webhook payload is a Buffer, parsing to JSON");
      const rawBody = event.toString("utf8");
      try {
        event = JSON.parse(rawBody);
      } catch (parseError) {
        console.error("Failed to parse webhook payload:", parseError);
        return res.status(400).json({
          success: false,
          message: "Invalid JSON payload",
          code: 400,
        });
      }
    }

    // Log the event type being processed
    console.log(`Processing Stripe webhook event: ${event?.type}`);

    if (!event || !event.type) {
      console.error("Invalid webhook event format - missing type property");
      return res.status(400).json({
        success: false,
        message: "Invalid webhook format",
        code: 400,
      });
    }

    // Pass the validated event to the service
    const data = await SubscriptionServices.handleStripeWebhook(event);

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    console.error("Error handling Stripe webhook:", error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get all transaction details for the authenticated user's organization with pagination support
 *
 * @param req - The HTTP request object with query parameters for pagination
 *   - `page` (optional): The current page index (defaults to 0)
 *   - `limit` (optional): Number of items per page (defaults to 10)
 * @param res - The HTTP response object
 */
export const getAllTransactions = async (req: Request, res: Response) => {
  try {
    const { orgId } = req;
    const { offset, limit } = req.query;
    // Parse pagination parameters
    const parsedOffset = Number(offset) || 0;
    const parsedLimit = Number(limit) || 15; // Default limit of 15 if not specified
    // Get paginated transaction data from service with success and message already included
    const response = await SubscriptionServices.getAllTransactions(
      orgId,
      parsedOffset,
      parsedLimit
    );

    // Return formatted response with standard structure
    const statusCode = response.success ? 200 : 404;
    res.status(statusCode).json({
      success: response.success,
      message: response.message,
      data: response.data,
      code: statusCode,
    });
  } catch (error) {
    console.error("Error in getAllTransactions controller:", error);
    res.status(error.output?.statusCode ?? 500).json({
      success: false,
      message: error.message || "Failed to retrieve transactions",
      code: error.output?.statusCode ?? 500,
    });
  }
};

/**
 * Buy a subscription plan for the authenticated user's organization
 *
 * @param req - The HTTP request object with query parameters for pagination
 *   - `page` (optional): The current page index (defaults to 0)
 *   - `limit` (optional): Number of items per page (defaults to 10)
 * @param res - The HTTP response object
 */
export const buySubscription = async (req: Request, res: Response) => {
  try {
    const { userId, orgId } = req;
    const { planId, pricingId } = req.body;
    console.log(planId, pricingId, "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
    const config = envConfig();
    const successUrl = config.stripe?.success_url;
    const cancelUrl = config.stripe?.cancel_url;
    const data = await SubscriptionServices.buySubscription(
      userId,
      orgId,
      planId,
      pricingId,
      successUrl,
      cancelUrl
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
