"use client";
import React, { FC, useState } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textarea from "../formElements/Textarea";
import { useForm } from "react-hook-form";
import { changeApplicationStatus } from "@/services/screenResumeServices";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import { JobApplication } from "@/interfaces/jobRequirementesInterfaces";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";
import { toastMessageSuccess, toTitleCase } from "@/utils/helper";

interface IProps {
  onClickCancel: () => void;
  disabled?: boolean;
  candidate?: JobApplication;
  actionType?: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];
  onSuccess?: () => void;
  title?: string;
}

const CandidateQualifiedModal: FC<IProps> = ({ onClickCancel, candidate, actionType = APPLICATION_STATUS.ON_HOLD, onSuccess, title }) => {
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<{ reason: string }>({
    defaultValues: {
      reason: "",
    },
    // Add validation rules here for the component
    mode: "onSubmit",
    criteriaMode: "firstError",
    shouldFocusError: true,
    reValidateMode: "onChange",
    resolver: (values) => {
      const errors: Record<string, { type: string; message: string }> = {};

      // Required validation for reason field
      if (!values.reason || values.reason.trim() === "") {
        errors.reason = {
          type: "required",
          message: "Please provide a reason",
        };
      } else if (values.reason.trim().length < 5) {
        errors.reason = {
          type: "minLength",
          message: "Reason should be at least 5 characters long",
        };
      } else if (values.reason.trim().length > 50) {
        errors.reason = {
          type: "maxLength",
          message: "Reason should not exceed 50 characters",
        };
      }

      return {
        values,
        errors,
      };
    },
  });

  const onSubmit = async (formData: { reason: string }) => {
    if (!candidate || !authData) return;

    try {
      setIsSubmitting(true);
      setError("");

      const data = {
        job_id: candidate.job_id,
        candidate_id: candidate.candidate_id,
        hiring_manager_id: authData.id,
        status: actionType,
        hiring_manager_reason: formData.reason,
      };

      const response = await changeApplicationStatus(data);

      if (response.data && response.data.success) {
        setSuccess(true);
        toastMessageSuccess("Candidate has been placed on hold successfully!");
        // Call the onSuccess callback if provided
        if (onSuccess) {
          setTimeout(() => {
            onClickCancel();
            onSuccess();
          }, 1500);
        }
      } else {
        setError(response.data?.message || "Failed to update candidate status");
      }
    } catch (err) {
      console.error("Error updating candidate status:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-md">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>On-Hold Confirmation</h2>
            <p>You are about to place this candidate on hold for further review.</p>
            {isSubmitting && (
              <Button className="modal-close-btn" onClick={onClickCancel}>
                <ModalCloseIcon />
              </Button>
            )}
          </div>
          <div className="modal-body">
            {/* qualification-card */}
            <div className="qualification-card">
              <div className="qualification-card-top">
                <div className="name">
                  <h3>{toTitleCase(candidate?.candidate_name || "Candidate")}</h3>
                  <p>{candidate?.ai_decision || "Pending"} by S9 InnerView</p>
                </div>
                <div className="top-right">
                  <div className="on-hold-status">
                    <p>On-Hold For Review</p>
                  </div>
                </div>
              </div>
              <div className="qualification-card-mid">
                <p>
                  <b>{title}</b>
                </p>
                <p>{candidate?.ai_reason || "No reason provided by AI evaluation."}</p>
              </div>
            </div>

            {!success && (
              <form onSubmit={handleSubmit(onSubmit)}>
                <InputWrapper>
                  <InputWrapper.Label htmlFor="reason" required>
                    Reason for putting on-hold
                  </InputWrapper.Label>
                  <Textarea
                    rows={4}
                    name="reason"
                    control={control}
                    placeholder="Enter reason for putting candidate on-hold"
                    className="form-control"
                  />
                  {errors.reason && <p className="text-danger mt-1">{errors.reason.message as string}</p>}
                </InputWrapper>

                {error && <div className="error-message alert alert-danger my-3">{error}</div>}

                <Button type="submit" className="dark-outline-btn rounded-md w-100" disabled={isSubmitting}>
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
export default CandidateQualifiedModal;
