import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ISkillData, JobSkillsState } from "@/interfaces/jobRequirementesInterfaces";

// Define the initial state using that type
const initialState: JobSkillsState = {
  careerSkills: [],
  roleSpecificSkills: [],
  cultureSpecificSkills: [],
};

export const jobSkillsSlice = createSlice({
  name: "jobSkills",
  initialState,
  reducers: {
    setSkillsData: (
      state,
      action: PayloadAction<{
        careerSkills?: ISkillData[];
        roleSpecificSkills?: ISkillData[];
        cultureSpecificSkills?: ISkillData[];
      }>
    ) => {
      if (action.payload.careerSkills) {
        state.careerSkills = action.payload.careerSkills;
      }
      if (action.payload.roleSpecificSkills) {
        state.roleSpecificSkills = action.payload.roleSpecificSkills;
      }
      if (action.payload.cultureSpecificSkills) {
        state.cultureSpecificSkills = action.payload.cultureSpecificSkills;
      }
    },
    clearSkillsData: (state) => {
      state.careerSkills = [];
      state.roleSpecificSkills = [];
      state.cultureSpecificSkills = [];
    },
  },
});

export const { setSkillsData, clearSkillsData } = jobSkillsSlice.actions;

// Simple selectors to use directly with useSelector
export const selectJobSkillsState = (state: { jobSkills: JobSkillsState }) => state.jobSkills;
export const selectCareerSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.careerSkills;
export const selectRoleSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.roleSpecificSkills;
export const selectCultureSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.cultureSpecificSkills;

// Export the reducer directly for easier import in the store
export default jobSkillsSlice.reducer;
