import React, { useCallback, useEffect, useState } from "react";

import Link from "next/link";

import { AddQuestionModal } from "@/components/commonModals/AddQuestionModal";
import FinalAssessmentModal from "@/components/commonModals/FinalAssessmentModal";
import Button from "@/components/formElements/Button";
import ArrowDownIcon from "@/components/svgComponents/ArrowDownIcon";
import ShareIcon from "@/components/svgComponents/ShareIcon";
import { AssessmentData, Question } from "@/interfaces/finalAssessment";
import { getFinalAssessmentQuestions } from "@/services/assessmentService";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";

import style from "../../../styles/conductInterview.module.scss";
import { commonConstants } from "@/constants/commonConstants";
import { useTranslations } from "next-intl";
import Loader from "@/components/loader/Loader";
import router from "next/router";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import ROUTES from "@/constants/routes";

const FinalAssessment = () => {
  const t = useTranslations();
  const [loading, setLoading] = useState(false);
  const [assessmentData, setAssessmentData] = useState<AssessmentData | null>(null);
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);
  const [expandedQuestions, setExpandedQuestions] = useState<{ [key: number]: boolean }>({});
  const [finalAssessmentId, setFinalAssessmentId] = useState<number | null>(null);
  const [jobId, setJobId] = useState<number | null>(null);
  const [jobApplicationId, setJobApplicationId] = useState<number | null>(null);
  const [isShared, setIsShared] = useState<boolean>(false);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);

  // State for AddQuestionModal
  const [showAddQuestionModal, setShowAddQuestionModal] = useState(false);
  const [selectedSkill, setSelectedSkill] = useState<{ id: number; title: string } | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  // Get parameters from URL on component mount
  useEffect(() => {
    // Get parameters from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const finalAssessmentId = urlParams.get(commonConstants.finalAssessmentId);
    const jobId = urlParams.get(commonConstants.jobId);
    const jobApplicationId = urlParams.get(commonConstants.jobApplicationId);

    if (finalAssessmentId && !isNaN(+finalAssessmentId) && jobId && !isNaN(+jobId) && jobApplicationId && !isNaN(+jobApplicationId)) {
      setFinalAssessmentId(+finalAssessmentId);
      setJobId(+jobId);
      setJobApplicationId(+jobApplicationId);
    } else {
      toastMessageError(t("no_final_assessment_id_found_in_url"));

      return;
    }
  }, [t]);

  // Function to fetch assessment questions - extracted as a reusable function
  const fetchAssessmentQuestions = useCallback(async () => {
    if (!finalAssessmentId || !jobId || !jobApplicationId) {
      toastMessageError(t("invalid_or_missing_final_assessment_id"));
      return;
    }

    try {
      setLoading(true);
      const response = await getFinalAssessmentQuestions(finalAssessmentId, jobId, jobApplicationId);

      if (response.data && response.data.success) {
        setAssessmentData(response.data.data);

        // Set jobApplicationId from response data
        if (response.data.data.jobApplicationId) {
          setJobApplicationId(response.data.data.jobApplicationId);
        }

        // Initialize all questions as expanded
        const questions = response.data.data.questionGroups?.[0]?.questions || [];
        if (questions.length > 0) {
          const initialExpandState = Object.fromEntries(questions.map((question: Question) => [question.id, true]));

          setExpandedQuestions(initialExpandState);
        }

        // Update isShared state based on the latest data
        if (response.data.data.isAssessmentShared !== undefined && response.data.data.isAssessmentSubmitted !== undefined) {
          setIsShared(response.data.data.isAssessmentShared);
          setIsSubmitted(response.data.data.isAssessmentSubmitted);
        }
      } else {
        toastMessageError(t(response.data?.message || "failed_to_fetch_assessment_questions"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("failed_to_fetch_assessment_questions"));
    } finally {
      setLoading(false);
    }
  }, [finalAssessmentId, jobId, jobApplicationId, t]);

  const handleShareSuccess = useCallback(() => {
    // Update isShared state
    setIsShared(true);

    // Update URL parameter to reflect the change

    // Refresh the assessment data
    fetchAssessmentQuestions();

    // Close the share modal
    setShowShareModal(false);
  }, [fetchAssessmentQuestions, setIsShared]);

  useEffect(() => {
    if (finalAssessmentId) {
      fetchAssessmentQuestions();
    }
  }, [finalAssessmentId, fetchAssessmentQuestions]);

  // Toggle question expansion
  const handleToggleQuestion = (questionId: number) => {
    setExpandedQuestions((prev) => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  };

  // Handle navigation to next question group
  const handleNextGroup = () => {
    if (assessmentData && currentGroupIndex < assessmentData.questionGroups.length - 1) {
      const nextIndex = currentGroupIndex + 1;
      setCurrentGroupIndex(nextIndex);

      // Initialize expanded state for questions in the next group
      const nextGroupQuestions = assessmentData.questionGroups[nextIndex].questions;
      const initialExpandState = nextGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      setExpandedQuestions(initialExpandState);
    }
  };

  // Handle navigation to previous question group
  const handlePreviousGroup = () => {
    if (assessmentData && currentGroupIndex > 0) {
      const prevIndex = currentGroupIndex - 1;
      setCurrentGroupIndex(prevIndex);

      // Initialize expanded state for questions in the previous group
      const prevGroupQuestions = assessmentData.questionGroups[prevIndex].questions;
      const initialExpandState = prevGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      setExpandedQuestions(initialExpandState);
    }
  };

  // Get current question group
  const currentGroup = assessmentData?.questionGroups[currentGroupIndex];

  // Format group type for display
  const formatGroupType = (type: string) => {
    return type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Determine if this is the last group
  const isLastGroup = assessmentData ? currentGroupIndex === assessmentData.questionGroups.length - 1 : false;

  // Group questions by skillId
  const getQuestionsGroupedBySkill = () => {
    if (!currentGroup) return [];

    // Group questions by skillId
    const groupedQuestions: { [key: number]: { skillTitle: string; questions: Question[] } } = {};

    currentGroup.questions.forEach((question) => {
      if (!groupedQuestions[question.skillId]) {
        groupedQuestions[question.skillId] = {
          skillTitle: question.skillTitle,
          questions: [],
        };
      }

      groupedQuestions[question.skillId].questions.push(question);
    });

    // Convert to array for rendering
    return Object.values(groupedQuestions);
  };

  const skillGroups = getQuestionsGroupedBySkill();

  // Render loading state
  if (loading) {
    return (
      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="text-center py-5">
            <Loader />
            <h1 className="mt-3">{t("loading_assessment_questions")}</h1>
          </div>
        </div>
      </div>
    );
  }

  // Render error state if no assessment data
  if (!loading && !assessmentData) {
    return (
      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="text-center py-5">{t("no_assessment_data_found")}</div>
        </div>
      </div>
    );
  }

  // Handle modal actions
  const handleCloseAddQuestionModal = () => {
    setShowAddQuestionModal(false);
    setSelectedSkill(null);
  };

  const handleAddQuestionSuccess = () => {
    toastMessageSuccess(t("question_added_successfully"));
    setShowAddQuestionModal(false);
    setSelectedSkill(null);

    // Re-fetch assessment questions
    fetchAssessmentQuestions();
  };

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="breadcrumb">
            <Link href={ROUTES.DASHBOARD}>{t("dashboard_")}</Link>
            <Link href={ROUTES.INTERVIEW.INTERVIEW_SUMMARY}>{t("interview_summary")}</Link>
            <Link href={""}>{t("final_assessment")}</Link>
          </div>
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon onClick={() => router.push(ROUTES.INTERVIEW.INTERVIEW_SUMMARY)} />
                {t("final_assessment")}
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          <div className="section-heading d-flex justify-content-between mb-4">
            <h2 className="m-0">
              {currentGroup && (
                <>
                  {t("group")} {currentGroupIndex + 1} of {assessmentData?.questionGroups.length}: <span>{formatGroupType(currentGroup.type)}</span>
                </>
              )}
            </h2>
            {!isShared && !isSubmitted ? (
              <Button className="clear-btn text-btn primary p-0 m-0" onClick={() => setShowShareModal(true)}>
                <ShareIcon className="me-2" />
                {t("share_assessment_link_to_candidate")}
              </Button>
            ) : (
              <div className="d-flex align-items-center">
                <div className="color-legend d-flex align-items-center">
                  <div className="d-flex align-items-center me-3">
                    <div style={{ width: "15px", height: "15px", backgroundColor: "#f5b759", borderRadius: "3px", marginRight: "5px" }}></div>
                    <span style={{ fontSize: "12px" }}>{t("candidate_answer")}</span>
                  </div>
                  <div className="d-flex align-items-center">
                    <div style={{ width: "15px", height: "15px", backgroundColor: "#00a651", borderRadius: "3px", marginRight: "5px" }}></div>
                    <span style={{ fontSize: "12px" }}>{t("correct_answer")}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="row">
            <div className="col-md-12">
              {skillGroups.map((skillGroup, skillGroupIndex) => (
                <div key={`skill-${skillGroupIndex}`} className="skill-group mb-4">
                  <h3 className="skill-title mb-3" style={{ color: "#2c7be5", borderBottom: "1px solid #e3e6f0", paddingBottom: "10px" }}>
                    {t("skill")}: {skillGroup.skillTitle}
                  </h3>

                  {skillGroup.questions.map((question, index) => (
                    <div key={question.id} className="interview-question-card with-border" onClick={() => handleToggleQuestion(question.id)}>
                      <p className="tittle">
                        {t("question")} {index + 1} <ArrowDownIcon className={!expandedQuestions[question.id] ? "rotate" : ""} />
                      </p>
                      <h5>{question.question}</h5>
                      {expandedQuestions[question.id] && (
                        <div className="question-body" onClick={(e) => e.stopPropagation()}>
                          {question.options.options.map((option) => {
                            const isCorrectAnswer = option.id === question.correctAnswer;
                            const isCandidateAnswer = option.id === question.applicantAnswer;

                            return (
                              <div
                                key={option.id}
                                className={`answer-strap ${
                                  isCandidateAnswer === isCorrectAnswer
                                    ? isCorrectAnswer
                                      ? "right-answer"
                                      : ""
                                    : isCorrectAnswer
                                      ? "right-answer"
                                      : isCandidateAnswer
                                        ? "candidate-answer"
                                        : ""
                                }`}
                              >
                                <div className="radio-wrapper">
                                  <input
                                    className="radio-input form-check-input"
                                    type="radio"
                                    name={`question_${question.id}`}
                                    id={`question_${question.id}_option_${option.id}`}
                                    value={option.id}
                                    checked={isCorrectAnswer}
                                    readOnly
                                  />
                                  <label className="radio-label" htmlFor={`question_${question.id}_option_${option.id}`}>
                                    {option.text}
                                  </label>
                                </div>
                              </div>
                            );
                          })}
                          {/* <div className="answer-strap p-0 border-0">
                            <p className="note-text">Note: The candidate's answer is not visible to you.</p>
                          </div> */}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Add New Question button for each skill group - only show if not shared */}
                  {!isShared && !isSubmitted && (
                    <Button
                      className="clear-btn text-btn secondary p-0 mb-3 mt-2"
                      style={{ color: "#f5b759", fontWeight: 500 }}
                      onClick={() => {
                        setSelectedSkill({
                          id: skillGroup.questions[0].skillId, // Get skillId from the first question in the group
                          title: skillGroup.skillTitle,
                        });
                        setShowAddQuestionModal(true);
                      }}
                    >
                      {t("add_new_question")}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="button-align" style={{ display: "flex", justifyContent: "space-between" }}>
          {/* Show Previous button if not on the first group */}
          <div>
            {currentGroupIndex > 0 && (
              <Button className="secondary-btn rounded-md" onClick={handlePreviousGroup}>
                <span style={{ marginRight: "8px" }}>&#8592;</span> {t("previous_skill_assessment")}
              </Button>
            )}
          </div>

          {/* Show Next button if not on the last group, positioned on the right */}
          <div>
            {!isLastGroup ? (
              <Button className="primary-btn rounded-md" onClick={handleNextGroup}>
                {t("next_skill_assessment")}
              </Button>
            ) : !isSubmitted ? (
              <Button
                className="primary-btn rounded-md"
                onClick={() => {
                  // setShowShareModal(true);
                  router.push(ROUTES.INTERVIEW.INTERVIEW_SUMMARY);
                  // We'll navigate after sharing or if user cancels
                }}
              >
                {t("complete_assessment")}
              </Button>
            ) : null}
          </div>
        </div>
      </div>

      {/* Add Question Modal */}
      {showAddQuestionModal && selectedSkill && finalAssessmentId && (
        <AddQuestionModal
          onClickCancel={handleCloseAddQuestionModal}
          onSubmitSuccess={handleAddQuestionSuccess}
          skillId={selectedSkill.id}
          skillTitle={selectedSkill.title}
          finalAssessmentId={Number(finalAssessmentId)}
        />
      )}

      {/* Share Assessment Modal */}
      {showShareModal && finalAssessmentId && (
        <FinalAssessmentModal
          onClickCancel={() => setShowShareModal(false)}
          onSubmitSuccess={handleShareSuccess}
          finalAssessmentId={Number(finalAssessmentId)}
          jobApplicationId={Number(jobApplicationId)}
          isFromCompleteButton={isLastGroup}
        />
      )}
    </div>
  );
};

export default FinalAssessment;
