"use client";
import React, { FC, useState } from "react";
import Button from "../formElements/Button";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import Loader from "../loader/Loader";
import { toastMessageError } from "@/utils/helper";
import { useTranslations } from "next-intl";
import { verifyCandidateEmail } from "@/services/assessmentService";
import { createValidationSchema } from "@/validations/finalAssessmentValidations";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Image from "next/image";
import InterviewInfoImg from "../../../public/assets/images/interview-info.png";
import { ASSESSMENT_INSTRUCTIONS } from "@/constants/commonConstants";

// Interface for the form data
interface AssessmentInstructionsFormData {
  email: string;
}

// Interface for component props
interface AssessmentInstructionsModalProps {
  finalAssessmentToken: string;
  onVerificationSuccess: (email: string, finalAssessmentId: number) => void;
  onClickCancel?: () => void;
}

export const AssessmentInstructionsModal: FC<AssessmentInstructionsModalProps> = ({ finalAssessmentToken, onVerificationSuccess, onClickCancel }) => {
  const t = useTranslations();
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<AssessmentInstructionsFormData>({
    defaultValues: {
      email: "",
    },
    resolver: yupResolver(createValidationSchema(t)),
    mode: "onChange",
  });

  const onSubmit = async (data: AssessmentInstructionsFormData) => {
    if (!finalAssessmentToken) {
      toastMessageError(t("no_assessment_token_found"));
      return;
    }

    try {
      setIsVerifying(true);
      setVerificationError(null);

      const response = await verifyCandidateEmail({
        email: data.email,
        token: finalAssessmentToken,
      });

      if (response.data && response.data.success && response.data.data) {
        const { finalAssessmentId } = response.data.data;
        onVerificationSuccess(data.email, finalAssessmentId);
      } else {
        const errorMessage = t(response.data?.message || "failed_to_verify_email");
        toastMessageError(errorMessage);
        setVerificationError(errorMessage);
      }
    } catch (error) {
      console.error("Error verifying email:", error);
      toastMessageError(t("an_unexpected_error_occurred_while_verifying_email"));
      setVerificationError(t("an_unexpected_error_occurred_while_verifying_email"));
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content">
          <div className="modal-header text-start pb-0">
            <h4>
              Assessment <span>Instructions</span>
            </h4>
            <p className="m-0 textMd">Please read the following instructions carefully</p>
            {onClickCancel && (
              <Button className="modal-close-btn" onClick={onClickCancel}>
                <ModalCloseIcon />
              </Button>
            )}
          </div>
          <div className="modal-body position-relative">
            {/* assessment instructions image */}
            <div className="interview-info-img-container">
              <Image
                src={InterviewInfoImg}
                alt="InterviewInfoImg"
                className="interview-info-img"
                width={180}
                height={180}
                style={{ objectFit: "contain" }}
              />
            </div>

            <div className="interview-info">
              {ASSESSMENT_INSTRUCTIONS.instructions.map((instruction, index) => (
                <div className="info-item" key={index}>
                  <h4 className="info-title">
                    <span className="dot" />
                    {instruction}
                  </h4>
                </div>
              ))}
            </div>

            {verificationError && (
              <div className="alert alert-danger mb-3 d-flex align-items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  fill="currentColor"
                  className="bi bi-exclamation-circle-fill me-2"
                  viewBox="0 0 16 16"
                >
                  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z" />
                </svg>
                <span>{verificationError}</span>
              </div>
            )}

            <form onSubmit={handleSubmit(onSubmit)} className="mt-4">
              <div className="mb-3">
                <InputWrapper>
                  <InputWrapper.Label required>{t("email_address")}</InputWrapper.Label>
                  <Textbox name="email" control={control} placeholder={t("enter_your_email")} className="form-control" />
                  {errors.email?.message && <InputWrapper.Error message={errors.email?.message} />}
                </InputWrapper>
              </div>

              <div className="action-btn justify-content-end">
                {onClickCancel && (
                  <Button className="dark-outline-btn rounded-md" onClick={onClickCancel}>
                    Cancel
                  </Button>
                )}
                <Button type="submit" className="primary-btn rounded-md" disabled={isVerifying || !isValid}>
                  <div className="d-flex align-items-center justify-content-center">
                    {isVerifying && <Loader />}
                    <span className={isVerifying ? "ms-2" : ""}>{isVerifying ? t("verifying") : t("start_assessment")}</span>
                  </div>
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentInstructionsModal;
