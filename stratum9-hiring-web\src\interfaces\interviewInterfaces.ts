/* eslint-disable @typescript-eslint/no-empty-object-type */
import { SelectOption } from "@/components/formElements/ReactCommonSelect";
import { QuestionType } from "@/constants/commonConstants";

export interface IScheduleInterview extends IUpdateScheduleInterview {
  jobApplicationId: number;
  jobId: number;
}

export interface IUpdateScheduleInterview {
  title: string;
  interviewerId: number;
  scheduleAt: number;
  startTime: number;
  endTime: number;
  roundType: string;
  description?: string | null;
  interviewId?: number;
  fileUrlArray?: string;
}

export interface IGetInterviews {
  applicationId: string;
  jobId: string;
  monthYear: string;
  interviewerId?: number;
}

export interface IGetInterviewersResponse extends SelectOption {}

export interface IGetInterviewsResponse {
  id: string;
  roundNumber: number;
  roundType: string;
  start: string;
  end: string;
  title: string;
  isEnded: boolean;
  scheduleAt: string;
  jobTitle: string;
  jobId: number;
  jobUniqueId: string;
  jobApplicationId: number;
  interviewerName: string;
  interviewerId: number;
  description?: string;
  resumeLink: string;
  candidateName: string;
  attachments: string;
}

export interface ScheduleInterviewFormValues {
  eventTitle: string;
  jobTitle: number;
  interviewType: string;
  jobId: string;
  date: string;
  startTime: string;
  description: string | undefined | null;
  endTime: string;
  interviewer: number;
  candidate: number;
}

export interface IUpcomingOrPastInterview {
  interviewId: number;
  candidateName: string;
  jobTitle: string;
  startTime: string;
  endTime: string;
  resumeFile: string;
  jobApplicationId: number;
  roundType: string;
  isEnded: boolean;
}

export interface IUpdateInterviewSkillQuestion {
  interviewQuestionId: number;
  question: string;
}

export interface IGetInterviewSkillQuestions {
  jobApplicationId: number;
  interviewId?: number;
}

export interface IAddInterviewSkillQuestion {
  jobApplicationId: number;
  interviewId?: number;
  question: string;
  skillType: string;
  jobSkillId?: number;
}

export interface IInterviewQuestionResponse {
  id: number;
  jobSkillId: number;
  skillId: number;
  interviewId: number | null;
  jobApplicationId: number;
  question: string;
  answer?: string;
  questionType: QuestionType;
  skillTitle: string;
  createdTs: string;
  updatedTs: string;
}

export interface IQuestionCategory {
  questions: IInterviewQuestionResponse[];
  score: number;
  interviewerName?: string;
}

export interface IGetInterviewSkillQuestionsResponse {
  roleSpecificQuestions: Record<string, IQuestionCategory>;
  cultureSpecificQuestions: Record<string, IQuestionCategory>;
  careerBasedQuestions: IQuestionCategory;
}

export interface IGetJobListResponse extends IGetInterviewersResponse {
  jobId: string;
}

export interface IGetCandidateListResponse extends IGetInterviewersResponse {}

export interface IInterviewQuestionFormValues {
  behavioralInfo?: string;
  [key: `answer-${number}`]: string;
}

export interface IUpdateInterviewAnswers {
  interviewId: number;
  skillMarked: number;
  skillId?: number;
  jobSkillId?: number;
  skillType: string;
  answers: Array<{ questionId: number; answer: string }>;
}

export interface IInterviewStaticInformation {
  oneToOneInterviewInstructions: Array<string>;
  videoCallInterviewInstructions: Array<string>;
  startumDescription: Array<{ id: number; description: string }>;
}
