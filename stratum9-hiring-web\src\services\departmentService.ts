import endpoint from "@/constants/endpoint";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import * as http from "@/utils/http";

export interface IDepartmentAlter {
  id: number;
  name: string;
  organizationId: number;
}

export interface FindDepartmentResponse {
  id: number;
  name: string;
  isDefaultDepartment: boolean;
}

/**
 * Get all departments associated with an organization
 * @param search Optional search query string
 * @returns Promise with API response
 */
export const findDepartments = (search?: string): Promise<IApiResponseCommonInterface<FindDepartmentResponse[]>> => {
  return http.get<{ search?: string }, IApiResponseCommonInterface<FindDepartmentResponse[]>>(endpoint.departments.GET_DEPARTMENTS, {
    search,
  });
};

/**
 * Add a new department
 * @param departmentData Department data to add
 * @returns Promise with API response
 */
export const addDepartment = (departmentData: { name: string }): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {
  return http.post(endpoint.departments.ADD_DEPARTMENT, departmentData);
};

/**
 * Update an existing department
 * @param departmentId ID of the department to update
 * @param departmentData Updated department data
 * @returns Promise with API response
 */
export const updateDepartment = (departmentId: number, departmentData: { name: string }): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {
  const url = endpoint.departments.UPDATE_DEPARTMENT.replace(":departmentId", departmentId.toString());

  return http.put(url, departmentData);
};

/**
 * Delete a department
 * @param departmentId ID of the department to delete
 * @param organizationId ID of the organization
 * @returns Promise with API response
 */
export const deleteDepartment = (departmentId: number): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {
  const url = endpoint.departments.DELETE_DEPARTMENT.replace(":departmentId", departmentId.toString());

  return http.remove(url);
};
