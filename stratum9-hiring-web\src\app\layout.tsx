import type { Metadata } from "next";
import "../../node_modules/bootstrap/dist/css/bootstrap.css";
import "../styles/style.scss";

import { NextIntlClientProvider } from "next-intl";
import { getLocale } from "next-intl/server";
import { Toaster } from "react-hot-toast";

// Import Redux Provider
import ReduxProvider from "@/redux/ReduxProvider";
import HeaderWrapper from "@/components/header/HeaderWrapper";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" />
        <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap" rel="stylesheet" />
      </head>
      <body>
        <ReduxProvider>
          <NextIntlClientProvider locale={locale}>
            <HeaderWrapper />
            {children}
            <Toaster position="top-right" />
          </NextIntlClientProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
