import * as Sentry from "@sentry/node";
import <PERSON>e from "stripe";
import { getSecret<PERSON>eys } from "../../config/awsConfig";
import envConfig from "../../config/envConfig";
import sendSubscriptionEmail, {
  SUBSCRIPTION_EMAIL_TYPE,
} from "../../utils/subscriptionEmails";
import dbConnection from "../../db/dbConnection";
import {
  ISubscription,
  IPaginatedTransactionsResponse,
  ITransactionData,
  IInvoice,
} from "./interface";
import {
  API_RESPONSE_MSG,
  BENEFIT_SLUGS,
  SUBSCRIPTION_MSG,
} from "../../utils/constants";
import OrganizationSubscriptionModel, {
  SubscriptionType,
  SubscriptionStatus,
} from "../../schema/s9-innerview/organization_subscriptions";
import OrganizationSubscriptionBenefitModel from "../../schema/s9-innerview/organization_subscription_benefits";
import SubscriptionPlanModel, {
  PaymentType,
} from "../../schema/s9-innerview/subscription_plans";
import SubscriptionPricingModel from "../../schema/s9-innerview/subscription_pricing";
import SubscriptionTransactionModel, {
  TransactionType,
  PaymentStatus,
  TransactionMethod,
} from "../../schema/s9-innerview/subscription_transactions";
import StripeWebhookModel from "../../schema/s9-innerview/stripe_webhook_events";
import OrganizationModel from "../../schema/s9/organization";
import AuthServices from "../auth/services";

/**
 * Subscription Services class for handling subscription-related operations
 */
export class SubscriptionServices {
  /**
   * Synchronizes organization subscription benefits with plan data
   * Creates or updates organization subscription benefits based on subscription plan
   * Falls back to free plan if specified plan is not found or has no benefits
   * Creates default benefit values if no valid plan can be found
   *
   * @param organizationId - Organization ID to update benefits for
   * @param planId - Subscription plan ID containing benefits data
   * @returns Promise<void>
   */
  static async syncSubscriptionBenefits(
    organizationId: number,
    planId: number
  ): Promise<void> {
    try {
      console.log("organizationId<<<<<<<<<<<<<<<<<<", organizationId);
      // Get plan repository
      const planRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        SubscriptionPlanModel
      );

      // Get the benefits repository early to avoid duplicate code later
      const benefitsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionBenefitModel
      );
      console.log("organizationId<<<<<<<<<<<<<<<<<<", organizationId);
      // Get or create benefits record
      const benefits =
        (await benefitsRepo.findOne({
          where: { organizationId },
        })) || new OrganizationSubscriptionBenefitModel();

      console.log("benefits<<<<<<<<<<<<<<<<<<", benefits);
      // If it's a new record, set the organization ID
      if (!benefits.id) {
        benefits.organizationId = organizationId;
      }
      console.log("planId<<<<<<<<<<<<<<<<<<", planId);
      // Get the subscription plan with benefits
      const subscriptionPlan = await planRepo.findOne({
        where: { id: planId },
      });

      console.log("subscriptionPlan<<<<<<<<<<<<<<<<<<", subscriptionPlan);

      // Plan found with benefits data - use it to update organization benefits
      if (subscriptionPlan && subscriptionPlan.benefits) {
        // Process benefits from plan data
        await SubscriptionServices.processBenefitsData(
          benefits,
          subscriptionPlan.benefits
        );
        await benefitsRepo.save(benefits);
        console.log(
          "Subscription benefits saved for organization",
          organizationId,
          "using plan",
          planId
        );
      } else {
        console.warn(
          `Subscription plan with ID ${planId} not found or has no benefits. Falling back to free plan.`
        );

        // Find the free plan as a fallback
        const freePlan = await planRepo.findOne({
          where: {
            paymentType: PaymentType.FREE,
            isActive: true,
          },
        });

        console.log("freePlan<<<<<<<<<<<<<<<<<<", freePlan);

        // If free plan exists and has benefits, use those
        if (freePlan && freePlan.benefits) {
          // Process benefits from free plan data
          await SubscriptionServices.processBenefitsData(
            benefits,
            freePlan.benefits
          );
          await benefitsRepo.save(benefits);
          console.log(
            "Free plan subscription benefits saved for organization",
            organizationId
          );
        } else {
          // If even the free plan doesn't exist or has no benefits, just log an error
          console.error(
            "No valid benefits data found: Neither specified plan nor free plan have benefits"
          );
        }
      }
    } catch (benefitsError) {
      console.error(
        "Error synchronizing subscription benefits for organization:",
        organizationId,
        benefitsError
      );
      Sentry.captureException(benefitsError);
      // We don't rethrow so subscription process can continue even if benefits update fails
    }
  }

  /**
   * Helper method to process benefits data from plan JSON
   * Works with both array and object formats of benefits data
   * @param benefits - Organization benefits record to update
   * @param benefitsData - Raw benefits data from plan
   */
  private static async processBenefitsData(
    benefits: OrganizationSubscriptionBenefitModel,
    benefitsData: any
  ): Promise<void> {
    const updatedBenefits = { ...benefits };

    // Process benefits based on format (array or object)
    if (Array.isArray(benefitsData)) {
      // Handle benefits array format
      benefitsData.forEach((benefit) => {
        if (
          benefit.slug === BENEFIT_SLUGS.JOB_POSTINGS &&
          typeof benefit.value === "number"
        ) {
          updatedBenefits.jobPostings = benefit.value;
        }
        if (
          benefit.slug === BENEFIT_SLUGS.RESUME_SCREENING &&
          typeof benefit.value === "number"
        ) {
          updatedBenefits.resumeScreening = benefit.value;
        }
        if (
          benefit.slug === BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD &&
          typeof benefit.value === "number"
        ) {
          updatedBenefits.manualResumeUpload = benefit.value;
        }
      });
    } else if (typeof benefitsData === "object") {
      // Handle benefits as object format
      const jobPostingsKey = BENEFIT_SLUGS.JOB_POSTINGS.toLowerCase();
      const resumeScreeningKey = BENEFIT_SLUGS.RESUME_SCREENING.toLowerCase();
      const manualResumeUploadKey =
        BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD.toLowerCase();

      if (benefitsData[jobPostingsKey]?.limit) {
        updatedBenefits.jobPostings = benefitsData[jobPostingsKey].limit;
      }
      if (benefitsData[resumeScreeningKey]?.limit) {
        updatedBenefits.resumeScreening =
          benefitsData[resumeScreeningKey].limit;
      }
      if (benefitsData[manualResumeUploadKey]) {
        // If manual_resume_upload is a value, use it
        if (typeof benefitsData[manualResumeUploadKey] === "number") {
          updatedBenefits.manualResumeUpload =
            benefitsData[manualResumeUploadKey];
        }
        // If manual_resume_upload has properties like limit/value
        else if (typeof benefitsData[manualResumeUploadKey] === "object") {
          if ("limit" in benefitsData[manualResumeUploadKey]) {
            updatedBenefits.manualResumeUpload =
              benefitsData[manualResumeUploadKey].limit;
          }
        }
      }
      // Set unlimited if specified
      if (benefitsData[jobPostingsKey]?.unlimited) {
        updatedBenefits.jobPostings = -1; // -1 represents unlimited
      }
      if (benefitsData[resumeScreeningKey]?.unlimited) {
        updatedBenefits.resumeScreening = -1; // -1 represents unlimited
      }
    }

    // Copy updated properties back to the original benefits object
    Object.assign(benefits, updatedBenefits);
  }

  private static stripe: Stripe;

  /**
   * Initialize Stripe with the secret key from AWS Secrets Manager
   */
  static async initializeStripe() {
    try {
      // If Stripe is already initialized, return it
      if (SubscriptionServices.stripe) {
        return SubscriptionServices.stripe;
      }

      // Get secrets from AWS Secrets Manager
      const keys = await getSecretKeys();
      console.log("Initializing Stripe with AWS secret key");

      // Check if the stripe_secret_key is defined in AWS secrets
      if (!keys.stripe_secret_key) {
        throw new Error("Stripe secret key is missing in AWS Secrets Manager.");
      }

      // Get Stripe API version from environment config
      const config = envConfig();
      const apiVersion = config.stripe?.api_version;

      // Initialize Stripe with the secret key from AWS
      SubscriptionServices.stripe = new Stripe(keys.stripe_secret_key, {
        apiVersion,
      });

      return SubscriptionServices.stripe;
    } catch (error) {
      console.error("Error initializing Stripe:", error);
      throw error;
    }
  }

  /**
   * Validate subscription details including plan, pricing, and Stripe customer
   * @param orgId Organization ID
   * @param planId Plan ID to validate
   * @param pricingId Pricing ID to validate
   * @returns Object with validation results
   */
  static async validateSubscriptionDetails(
    orgId: number,
    planId: string,
    pricingId: string
  ) {
    try {
      // Get repositories
      const planRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        SubscriptionPlanModel
      );
      const pricingRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        SubscriptionPricingModel
      );
      // const orgRepo =
      //   await dbConnection.getS9DatabaseRepository(OrganizationModel);
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );

      // Validate Plan ID
      const subscriptionPlan = await planRepo.findOne({
        where: { id: parseInt(planId, 10), isActive: true },
      });

      if (!subscriptionPlan) {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.invalid_plan,
          data: null,
        };
      }

      // Validate Pricing ID
      const pricingOption = await pricingRepo.findOne({
        where: {
          id: parseInt(pricingId, 10),
          subscriptionId: parseInt(planId, 10),
        },
      });

      if (!pricingOption) {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.invalid_pricing,
          data: null,
        };
      }

      // Check if customer exists in Stripe
      // const organization = await orgRepo.findOne({
      //   where: { id: orgId },
      // });

      // if (!organization || !organization.stripeCustomerId) {
      //   return {
      //     success: false,
      //     message: SUBSCRIPTION_MSG.stripe_customer_not_found,
      //     data: null,
      //   };
      // }

      // Initialize Stripe API
      // const stripe = await SubscriptionServices.initializeStripe();

      // try {
      //   // Check if customer exists in Stripe
      //   const stripeCustomer = await stripe.customers.retrieve(
      //     organization.stripeCustomerId
      //   );

      //   if (!stripeCustomer || stripeCustomer.deleted) {
      //     return {
      //       success: false,
      //       message: SUBSCRIPTION_MSG.stripe_customer_deleted,
      //       data: null,
      //     };
      //   }
      // } catch (error) {
      //   return {
      //     success: false,
      //     message: SUBSCRIPTION_MSG.stripe_customer_error,
      //     data: null,
      //   };
      // }

      // Check if the current active plan is different from the selected plan
      const currentSubscription = await orgSubsRepo.findOne({
        where: {
          organizationId: orgId,
          isActive: true,
        },
        order: {
          createdTs: "DESC", // Get the most recently created subscription
        },
      });

      console.log("currentSubscription", currentSubscription);
      console.log("planId<<<<<<<<<<<<<", parseInt(planId, 10));

      if (
        currentSubscription &&
        currentSubscription.subscriptionId === parseInt(planId, 10)
      ) {
        console.log(
          "The selected plan is the same as the current active plan. No changes needed."
        );
        return {
          success: false,
          message: SUBSCRIPTION_MSG.plan_same,
          data: null,
        };
      }

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: {
          planId: subscriptionPlan.id,
          planName: subscriptionPlan.name,
          pricingId: pricingOption.id,
          price: pricingOption.price,
          // stripeCustomerId: organization.stripeCustomerId,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        data: null,
      };
    }
  }

  /**
   * Create a Stripe checkout session
   * @param userId User ID
   * @param orgId Organization ID
   * @param planId Plan ID to subscribe to
   * @param pricingId Pricing ID for the selected plan
   * @param successUrl URL to redirect to on successful payment
   * @param cancelUrl URL to redirect to if the user cancels
   * @returns Object with success status, message, session ID and URL
   */
  static async createCheckoutSession(
    userId: number,
    orgId: number,
    planId: string,
    pricingId: string,
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      console.log(
        "createCheckoutSession",
        userId,
        orgId,
        planId,
        pricingId,
        successUrl,
        cancelUrl
      );
      // Initialize Stripe
      const stripe = await SubscriptionServices.initializeStripe();

      // Get organization details
      const organizationRepo =
        await dbConnection.getS9DatabaseRepository(OrganizationModel);
      const organization = await organizationRepo.findOne({
        where: { id: Number(orgId) },
      });

      if (!organization) {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.organization_not_found,
          code: 404,
          data: null,
        };
      }

      // Check if customer already exists for this organization
      let customerId = organization.stripeCustomerId;

      // If customer doesn't exist, create one
      if (!customerId) {
        const customerResponse =
          await SubscriptionServices.createStripeCustomer(orgId);

        if (!customerResponse.success) {
          return {
            success: false,
            message: SUBSCRIPTION_MSG.customer_creation_error,
            code: 400,
            data: null,
          };
        }

        customerId = customerResponse.data.customerId;
      }

      // Get pricing details
      const pricingRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        SubscriptionPricingModel
      );
      const pricing = await pricingRepo.findOne({
        where: { id: Number(pricingId) },
      });

      if (!pricing || !pricing.stripePriceId) {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.invalid_pricing,
          code: 404,
          data: null,
        };
      }

      console.log("successUrl<<<<<<<<<<<<<<<<<<<<<<", successUrl);
      console.log("cancelUrl<<<<<<<<<<<<<<<<<<<<<<", cancelUrl);

      console.log("pricing<<<<<<<<<<<<<<<<<<<<<<", pricing);

      // Create checkout session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ["card"],
        line_items: [
          {
            price: pricing.stripePriceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: successUrl,
        cancel_url: cancelUrl,
        client_reference_id: orgId.toString(),
        customer: customerId,
        subscription_data: {
          metadata: {
            userId: userId.toString(),
            orgId: orgId.toString(),
            planId,
            pricingId,
          },
        },
      });

      console.log("session<<<<<<<<<<<<<<<<<<<<<<", session);

      return {
        success: true,
        message: SUBSCRIPTION_MSG.checkout_session_created,
        code: 200,
        data: {
          sessionId: session.id,
          url: session.url,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        code: 500,
        data: null,
      };
    }
  }

  /**
   * Creates a Stripe customer for the organization
   * @param orgId Organization ID
   * @returns Object with success status, message, and customer data
   */
  static async createStripeCustomer(orgId: number) {
    try {
      // First check if the organization exists
      const orgRepo =
        await dbConnection.getS9DatabaseRepository(OrganizationModel);
      const organization = await orgRepo.findOne({
        where: { id: orgId },
      });

      if (!organization) {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.organization_not_found,
          code: 404,
        };
      }

      // Check if the organization already has a Stripe customer ID
      if (organization.stripeCustomerId) {
        // Organization already has a Stripe customer ID, verify it exists on Stripe
        const stripe = await SubscriptionServices.initializeStripe();
        try {
          const customer = await stripe.customers.retrieve(
            organization.stripeCustomerId
          );
          if (customer && !customer.deleted) {
            return {
              success: true,
              message: SUBSCRIPTION_MSG.customer_exists,
              code: 200,
              data: {
                customerId: organization.stripeCustomerId,
                customer,
              },
            };
          }
          // If we get here, the customer exists in DB but was deleted in Stripe
          // We'll continue to create a new one below
        } catch (stripeError) {
          // Customer doesn't exist in Stripe, will create a new one
          console.log(
            `Stripe customer ID ${organization.stripeCustomerId} not found in Stripe, creating new one.`
          );
        }
      }

      // Get user details for the organization admin
      const { email, name } = organization;

      const stripe = await SubscriptionServices.initializeStripe();

      // Check if customer already exists in Stripe by email
      const existingCustomers = await stripe.customers.list({
        email,
        limit: 1, // Limit to 1 result, since email should be unique
      });

      let customerId: string;
      let customer: Stripe.Customer;

      if (existingCustomers.data.length > 0) {
        // If customer exists in Stripe, use the existing customer ID
        [customer] = existingCustomers.data;
        customerId = customer.id;
      } else {
        // Create a new customer in Stripe
        customer = await stripe.customers.create({
          email,
          name,
          metadata: {
            organizationId: orgId,
          },
        });
        customerId = customer.id;
      }

      // Save the Stripe customer ID into the organization table in the s9 database
      await SubscriptionServices.saveCustomerIdInOrganizationDb(
        orgId,
        customerId
      );

      return {
        success: true,
        message:
          existingCustomers.data.length > 0
            ? SUBSCRIPTION_MSG.customer_exists
            : SUBSCRIPTION_MSG.customer_created,
        code: existingCustomers.data.length > 0 ? 200 : 201,
        data: {
          customerId,
          customer,
        },
      };
    } catch (error) {
      // Capture exception using Sentry
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        code: 500,
        data: null,
      };
    }
  }

  /**
   * Send subscription email to both user and organization admin
   * @param userId - User ID to find user email
   * @param organizationId - Organization ID to find organization email
   * @param emailType - Type of subscription email to send
   * @param planName - Plan name for email content
   * @param oldPlanName - Old plan name for upgrade/downgrade emails
   */
  private static async sendSubscriptionEmailToBoth(
    userId: number,
    organizationId: number,
    emailType: string,
    planName: string,
    oldPlanName?: string
  ): Promise<void> {
    try {
      // Get user details using AuthServices
      const user = await AuthServices.getUserByUserId(userId);

      if (!user) {
        console.error(`User not found with ID: ${userId}`);
        return;
      }

      // Get organization details
      const orgRepo =
        await dbConnection.getS9DatabaseRepository(OrganizationModel);
      const organization = await orgRepo.findOne({
        where: { id: organizationId },
      });

      if (!organization) {
        console.error(`Organization not found with ID: ${organizationId}`);
        return;
      }

      console.log(
        `Sending ${emailType} email to user: ${user.email} and organization: ${organization.email}`
      );
      console.log("user<<<<<<<<<<<<<<<<<<", user);
      console.log("organization<<<<<<<<<<<<<<<<<<", organization);

      // Prepare email parameters with appropriate dates
      const emailParams = {
        planName,
        emailType,
        oldPlanName,
        startDate: new Date(), // Current date for subscription events
        interval: "Monthly", // Default billing cycle
        cancellationDate:
          emailType === SUBSCRIPTION_EMAIL_TYPE.CANCEL ? new Date() : undefined,
        upgradeDate:
          emailType === SUBSCRIPTION_EMAIL_TYPE.UPGRADE
            ? new Date()
            : undefined,
        downgradeDate:
          emailType === SUBSCRIPTION_EMAIL_TYPE.DOWNGRADE
            ? new Date()
            : undefined,
      };

      // Send email to organization admin (if different from user email)
      if (organization.email) {
        await sendSubscriptionEmail({
          email: organization.email,
          userName: organization.name, // Use organization name as userName for org admin
          ...emailParams,
        });
        console.log(
          `✅ Subscription email sent to organization: ${organization.email}`
        );
      }

      // Send email to user
      if (user.email && organization.email !== user.email) {
        await sendSubscriptionEmail({
          email: user.email,
          userName: `${user.first_name} ${user.last_name}`.trim(),
          ...emailParams,
        });
        console.log(`✅ Subscription email sent to user: ${user.email}`);
      }
    } catch (error) {
      console.error("Error sending subscription emails:", error);
      Sentry.captureException(error);
    }
  }

  /**
   * Save customer ID in organization table
   * @param orgId Organization ID
   * @param customerId Stripe customer ID
   */
  static async saveCustomerIdInOrganizationDb(
    orgId: number,
    customerId: string
  ) {
    try {
      const dataConnection = await dbConnection.getS9DataSource();
      const organizationRepository =
        dataConnection.getRepository(OrganizationModel);

      // Fetch the organization using the organizationId
      const organization = await organizationRepository.findOne({
        where: { id: orgId },
      });
      if (organization) {
        // Save the Stripe customer ID in the organization table
        organization.stripeCustomerId = customerId; // Assuming `stripeCustomerId` field exists in the organization table
        organization.updated_ts = new Date(); // Optionally update the timestamp

        // Save the updated organization data in the organization table
        await organizationRepository.save(organization);
      } else {
        console.error(`Organization not found for orgId: ${orgId}`);
      }
    } catch (error) {
      Sentry.captureException(error);
      console.error(
        "Error saving Stripe customer ID in organization table:",
        error
      );
    }
  }

  /**
   * Get current subscription for a user
   * @param orgId User ID
   * @returns Subscription data or null
   */

  static async getCurrentSubscription(orgId: number) {
    try {
      // Get repositories
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );

      const orgSubscriptionInfo = await orgSubsRepo
        .createQueryBuilder("orgSubscription")
        .innerJoinAndSelect(
          "orgSubscription.subscription",
          "subscriptionPlan",
          "subscriptionPlan.id = orgSubscription.subscriptionId"
        )
        .leftJoinAndSelect(
          "subscription_pricing",
          "subscriptionPricing",
          "subscriptionPricing.id = orgSubscription.subscriptionPricingId"
        )
        .where("orgSubscription.organizationId = :orgId", {
          orgId,
        })
        .andWhere("orgSubscription.isActive = true")
        .orderBy("orgSubscription.updatedTs", "DESC")
        .select([
          "orgSubscription.id as orgSubscriptionId",
          "orgSubscription.startDate as startDate",
          "orgSubscription.expiryDate as expiryDate",
          "orgSubscription.nextBillingDate as nextBillingDate",
          "orgSubscription.status as status",
          "subscriptionPlan.id as subscriptionPlanId",
          "subscriptionPlan.name as subscriptionPlanName",
          "subscriptionPlan.description as subscriptionPlanDescription",
          "subscriptionPlan.paymentType as subscriptionPlanPaymentType",
          "subscriptionPricing.id as pricingId",
          "subscriptionPricing.price as price",
        ])
        .getRawOne();

      console.log("orgSubscriptionInfo<<<<<<<<<<<<<<<<<<", orgSubscriptionInfo);

      return {
        success: true,
        message: SUBSCRIPTION_MSG.subscription_retrieved,
        data: orgSubscriptionInfo,
      };
    } catch (error) {
      // Capture the error with Sentry for monitoring
      console.error("Error fetching subscription:", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
        data: null,
      };
    }
  }

  /**
   * Get all available subscription plans
   * @returns List of subscription plans
   */
  static async getAllPlans() {
    try {
      // Get repositories
      const subscriptionPlanRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          SubscriptionPlanModel
        );
      // const pricingRepo = await dbConnection.getS9InnerViewDatabaseRepository(
      //   SubscriptionPricingModel
      // );

      // fetch all Plans with pricing options in one query
      const allSubscriptions = await subscriptionPlanRepo
        .createQueryBuilder("subscriptionPlan")
        .leftJoinAndSelect(
          "subscription_pricing",
          "subscriptionPricing",
          "subscriptionPricing.subscriptionId = subscriptionPlan.id"
        )
        .where("subscriptionPlan.isActive = true")
        .select([
          "subscriptionPlan.id as subscriptionPlanId",
          "subscriptionPlan.name as subscriptionPlanName",
          "subscriptionPlan.description as subscriptionPlanDescription",
          "subscriptionPlan.benefits as subscriptionPlanBenefits",
          "subscriptionPlan.paymentType as subscriptionPlanPaymentType",
          "subscriptionPricing.id as pricingId",
          "subscriptionPricing.price as price",
        ])
        .getRawMany();
      console.log("allSubscriptions<<<<<<<<<<<<<<<<<<", allSubscriptions);

      // // Fetch active subscription plans
      // const plans = allSubscriptions.filter((plan) => plan.isActive);

      // if (!plans || plans.length === 0) {
      //   return {
      //     success: false,
      //     message: "No active subscription plans found",
      //     data: [],
      //   };
      // }

      // // Fetch all pricing options for these plans using query builder
      // const planIds = plans.map((plan) => plan.id);
      // const pricingOptions = await pricingRepo
      //   .createQueryBuilder("pricing")
      //   .select([
      //     "pricing.id",
      //     "pricing.price",
      //     "pricing.pricingType",
      //     "pricing.stripePriceId",
      //     "pricing.subscriptionId",
      //   ])
      //   .where("pricing.subscriptionId IN (:...planIds)", { planIds })
      //   .getMany();

      // // Group pricing options by plan ID
      // const pricingByPlanId = pricingOptions.reduce(
      //   (acc, pricing) => {
      //     if (!acc[pricing.subscriptionId]) {
      //       acc[pricing.subscriptionId] = [];
      //     }
      //     acc[pricing.subscriptionId].push(pricing);
      //     return acc;
      //   },
      //   {} as Record<number, SubscriptionPricingModel[]>
      // );

      // // Format the plans data with their pricing options
      // const formattedPlans = plans.map((plan) => ({
      //   plan_id: plan.id,
      //   plan_name: plan.name,
      //   description: plan.description,
      //   status: plan.isActive ? "active" : "inactive",
      //   benefits: plan.benefits,
      //   pricing_options: pricingByPlanId[plan.id] || [],
      // }));

      const formattedSubscription = allSubscriptions.map((subscription) => ({
        ...subscription,
        subscriptionPlanBenefits: JSON.parse(
          subscription.subscriptionPlanBenefits
        ),
      }));
      return {
        success: true,
        message: SUBSCRIPTION_MSG.plans_retrieved,
        data: formattedSubscription,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error in getAllPlans:", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
        data: null,
      };
    }
  }

  /**
   * Handle Stripe webhook events
   * @param event Stripe webhook event
   * @returns Result of webhook handling
   */
  static async handleStripeWebhook(
    event: any
    // sig: string | string[] | undefined
  ) {
    try {
      console.log("event<<<<<<<<<<<<<<<<<<<<<<<<<<", event);

      // Extract event type and event data
      const eventType = event.type;
      const eventData = event.data.object;
      console.log("eventData<<<<<<<<<<<<<<<<<<<<<<<<<<", eventData);

      // Save the event in the database
      const stripeWebhookRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(StripeWebhookModel);
      await stripeWebhookRepo.save({
        eventType,
        eventData,
        stripePaymentId: eventData.id,
        receivedAt: new Date(),
      });
      console.log("eventData<<<<<<<<<<<<<<<<<<<<<<<<<<", eventData);
      // Handle the event based on its type
      switch (event.type) {
        case "invoice.payment_succeeded":
          await SubscriptionServices.handleInvoicePaymentSucceeded(
            event.data.object
          );
          break;
        case "invoice.payment_failed":
          await SubscriptionServices.handleInvoicePaymentFailed(
            event.data.object
          );
          break;
        case "customer.subscription.created":
          await SubscriptionServices.handleSubscriptionCreated(
            event.data.object
          );
          break;
        case "customer.subscription.updated":
          await SubscriptionServices.handleSubscriptionUpdated(
            event.data.object
          );
          break;
        case "customer.subscription.deleted":
          await SubscriptionServices.handleSubscriptionDeleted(
            event.data.object
          );
          break;
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return {
        success: true,
        message: SUBSCRIPTION_MSG.webhook_processed,
        data: null,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: SUBSCRIPTION_MSG.webhook_error,
        data: null,
      };
    }
  }

  /**
   * Handle invoice payment succeeded event
   * @param invoice Invoice object
   */

  /**
   * Handle invoice payment failed event
   * @param invoice Invoice object
   */

  /**
   * Helper method to wait for a specified time
   * @param ms Milliseconds to wait
   */
  private static async delay(ms: number): Promise<void> {
    return new Promise<void>((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  /**
   * Try to find subscription with retries and exponential backoff
   * @param stripeSubscriptionId The Stripe subscription ID to find
   * @param maxAttempts Maximum number of retry attempts
   * @param initialDelay Initial delay in milliseconds
   */
  private static async findSubscriptionWithRetry(
    stripeSubscriptionId: string,
    maxAttempts: number = 5,
    initialDelay: number = 1000
  ) {
    const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
      OrganizationSubscriptionModel
    );

    let attempts = 0;
    let subscription = null;

    while (attempts < maxAttempts) {
      console.log(
        `Attempt ${attempts + 1}/${maxAttempts} to find subscription ${stripeSubscriptionId}`
      );

      // eslint-disable-next-line no-await-in-loop
      subscription = await orgSubsRepo.findOne({
        where: { stripeSubscriptionId },
      });

      if (subscription) {
        console.log(`Subscription found on attempt ${attempts + 1}`);
        return subscription;
      }

      // Exponential backoff
      const delay = initialDelay * 2 ** attempts;
      console.log(`Subscription not found, waiting ${delay}ms before retry`);
      // eslint-disable-next-line no-await-in-loop
      await SubscriptionServices.delay(delay);
      attempts += 1;
    }

    return null;
  }

  static async handleInvoicePaymentSucceeded(invoice: IInvoice) {
    try {
      // Get the subscription ID from the invoice
      console.log("handleInvoicePaymentSucceeded called");
      console.log("invoice", invoice);
      const stripeSubscriptionId =
        invoice.parent.subscription_details.subscription;
      if (stripeSubscriptionId) {
        // Get repository for organization subscriptions
        // const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        //   OrganizationSubscriptionModel
        // );

        // Try to find the subscription with retries
        const subscription =
          await this.findSubscriptionWithRetry(stripeSubscriptionId);
        if (subscription) {
          // Update subscription status to ACTIVE since payment was successful
          // subscription.status = SubscriptionStatus.ACTIVE;
          // // Convert Unix timestamps from Stripe to JavaScript Date objects
          // if (invoice.period_end) {
          //   // Update next billing date and expiry date based on invoice period end
          //   const periodEnd = new Date(invoice.period_end * 1000);
          //   subscription.nextBillingDate = periodEnd;
          //   subscription.expiryDate = periodEnd;
          // }

          // if (invoice.period_start) {
          //   // Update start date if this is a new billing period
          //   const periodStart = new Date(invoice.period_start * 1000);
          //   // Only update if the current start date is null or if the new period starts after the current one
          //   if (
          //     !subscription.startDate ||
          //     subscription.startDate < periodStart
          //   ) {
          //     subscription.startDate = periodStart;
          //   }
          // }

          // subscription.updatedTs = new Date();
          // await orgSubsRepo.save(subscription);
          console.log("Subscription updated in Stripe: ", subscription);

          // Now handle the payment transaction
          const transactionRepo =
            await dbConnection.getS9InnerViewDatabaseRepository(
              SubscriptionTransactionModel
            );

          const { metadata } = invoice.parent.subscription_details;
          console.log("metadata<<<<<<<<<<<<<<<", metadata);

          const transaction = new SubscriptionTransactionModel();
          transaction.organizationSubscriptionId = subscription.id;
          transaction.organizationId = parseInt(metadata.orgId, 10);
          transaction.paymentStatus = PaymentStatus.SUCCESS;
          transaction.amount = invoice.amount_paid / 100; // Convert from cents to dollars
          transaction.transactionType = TransactionType.PURCHASE;
          transaction.transactionMethod = TransactionMethod.CARD;
          transaction.invoiceId = invoice.id; // Invoice ID
          transaction.invoiceUrl = invoice.hosted_invoice_url || null; // Invoice URL
          // Use invoice creation date if available
          transaction.transactionDate = invoice.created
            ? new Date(invoice.created * 1000)
            : new Date();
          console.log("transaction<<<<<<<<<<<<<<<", transaction);
          // Save the transaction
          await transactionRepo.save(transaction);

          console.log(
            `Subscription ${subscription.id} updated to ACTIVE and payment transaction recorded`
          );
        } else {
          console.warn(
            `Subscription not found in database for Stripe subscription ID: ${stripeSubscriptionId}`
          );
        }
      }
      console.log(`Invoice payment succeeded: ${invoice.id}`);
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error handling invoice payment succeeded event:", error);
    }
  }

  /**
   * Handle invoice payment failed event
   * @param invoice Invoice object
   */
  static async handleInvoicePaymentFailed(invoice: IInvoice) {
    try {
      // Get the subscription ID from the invoice
      const stripeSubscriptionId =
        invoice.parent.subscription_details.subscription;

      if (stripeSubscriptionId) {
        // Get repository for organization subscriptions
        const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
          OrganizationSubscriptionModel
        );

        // Get the subscription from our database
        const subscription = await orgSubsRepo.findOne({
          where: { stripeSubscriptionId },
        });

        if (subscription) {
          // Mark the subscription as PAST_DUE since payment failed
          subscription.status = SubscriptionStatus.PAST_DUE;
          subscription.isActive = false;
          subscription.updatedTs = new Date();
          await orgSubsRepo.save(subscription);

          // Now handle the payment failure transaction
          const transactionRepo =
            await dbConnection.getS9InnerViewDatabaseRepository(
              SubscriptionTransactionModel
            );

          const metadata = invoice.metadata || {};
          const orgId = parseInt(metadata.orgId, 10);

          const transaction = new SubscriptionTransactionModel();
          transaction.organizationSubscriptionId = subscription.id;
          transaction.organizationId = orgId;
          transaction.paymentStatus = PaymentStatus.FAILED;
          transaction.amount = 0; // We may not know the exact amount for failed payments
          transaction.transactionType = TransactionType.PURCHASE;
          transaction.transactionMethod = TransactionMethod.CARD;
          transaction.invoiceId = invoice.id; // Invoice ID
          transaction.invoiceUrl = invoice.hosted_invoice_url || null; // Invoice URL
          transaction.transactionDate = new Date();

          // Save the transaction
          await transactionRepo.save(transaction);

          console.log(
            `Subscription ${subscription.id} updated to PAST_DUE and payment failure recorded`
          );
        } else {
          console.warn(
            `Subscription not found in database for Stripe subscription ID: ${stripeSubscriptionId}`
          );
        }
      }
      console.log(`Invoice payment failed: ${invoice.id}`);
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error handling invoice payment failed event:", error);
    }
  }

  /**
   * Handle subscription created event
   * @param subscription Subscription object
   */

  static async handleSubscriptionCreated(subscription: ISubscription) {
    try {
      console.log("Subscription created event received:", subscription);
      // Get repositories
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );
      // const transactionRepo =
      //   await dbConnection.getS9InnerViewDatabaseRepository(
      //     SubscriptionTransactionModel
      //   ); // Add transaction repository
      const stripe = await SubscriptionServices.initializeStripe();

      // Get metadata from subscription
      const metadata = subscription.metadata || {};
      const orgId = parseInt(metadata.orgId, 10);
      const planId = parseInt(metadata.planId, 10);
      const pricingId = parseInt(metadata.pricingId, 10);
      const userId = parseInt(metadata.userId, 10);
      console.log("orgId", orgId);
      console.log("planId", planId);
      console.log("pricingId", pricingId);
      console.log("userId", userId);
      if (!orgId || !planId || !pricingId || !userId) {
        console.error(
          "Missing required metadata for subscription:",
          subscription.id
        );
        return;
      }

      // Variables to track subscription change type for email notification
      // let subscriptionChangeType = SUBSCRIPTION_EMAIL_TYPE.PURCHASE; // Default to purchase
      // let oldPlanName = "";
      // let oldPrice = 0;

      // Get the free plan ID from subscription plans
      const planRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        SubscriptionPlanModel
      );
      const freePlan = await planRepo.findOne({
        where: { paymentType: PaymentType.FREE, isActive: true },
      });

      if (!freePlan) {
        console.error("Free plan not found");
        return;
      }

      const freePlanId = freePlan ? freePlan.id : 1; // Default to 1 if not found
      console.log(`Free plan ID dynamically found: ${freePlanId}`);

      // Step 1: Check if there is an existing active subscription
      const existingSubscription = await orgSubsRepo.findOne({
        where: {
          organizationId: orgId,
          isActive: true, // Fetch active or cancel_at_period_end subscriptions
        },
        order: {
          createdTs: "DESC", // Get the most recently created subscription
        },
      });

      console.log("existingSubscription", existingSubscription);

      if (existingSubscription) {
        console.log(
          `Existing active subscription found for organization ${existingSubscription.organizationId}, determining if this is an upgrade or downgrade.`
        );

        // If the existing subscription is a free plan and has no subscription ID
        if (
          existingSubscription.subscriptionId === freePlanId &&
          !existingSubscription.stripeSubscriptionId
        ) {
          console.log(
            "Existing subscription is a free plan with no subscription ID. Marking as canceled."
          );

          // Update existing subscription to be inactive and canceled
          existingSubscription.status = SubscriptionStatus.CANCELED;
          existingSubscription.isActive = false;
          existingSubscription.expiryDate = new Date(); // Set expiry date to current UTC time
          existingSubscription.updatedTs = new Date();

          // Save the updated subscription
          await orgSubsRepo.save(existingSubscription);
          console.log(
            `Free plan subscription for organization ${orgId} has been marked as CANCELED and expired.`
          );
        } else {
          // Existing subscription is not a free plan, handle upgrades/downgrades

          // Cancel the old subscription on Stripe
          try {
            await stripe.subscriptions.cancel(
              existingSubscription.stripeSubscriptionId
            );
            console.log(
              `Existing subscription for organization ${orgId} has been canceled.`
            );
          } catch (cancelError) {
            console.error(
              "Error canceling previous subscription:",
              cancelError
            );
            Sentry.captureException(cancelError);
            // Continue with new subscription creation even if cancel fails
          }
        }
      } else {
        console.log(`No existing subscription found. This is a new purchase.`);
      }

      // Step 2: Create a new subscription record
      const newSubscription = new OrganizationSubscriptionModel();
      newSubscription.organizationId = orgId;
      newSubscription.subscriptionId = planId;
      newSubscription.subscriptionPricingId = pricingId;
      newSubscription.stripeSubscriptionId = subscription.id;

      // Convert Unix timestamps from Stripe to JavaScript Date objects (UTC)
      // Stripe timestamps are in UTC seconds, so we multiply by 1000 to get milliseconds
      const startDate = subscription.start_date
        ? new Date(subscription.start_date * 1000)
        : new Date();

      // Get period end/start from first subscription item if available, otherwise try root level
      // All Stripe timestamps are in UTC
      let currentPeriodEnd = new Date();
      if (subscription.items?.data?.[0]?.current_period_end) {
        currentPeriodEnd = new Date(
          subscription.items.data[0].current_period_end * 1000
        );
      } else if (subscription.current_period_end) {
        currentPeriodEnd = new Date(subscription.current_period_end * 1000);
      }
      console.log("startDate", startDate);
      console.log(
        "subscription.items.data[0].current_period_end",
        subscription.items?.data?.[0]?.current_period_end || "not found"
      );

      // Get period start similarly
      let currentPeriodStart = new Date();
      if (subscription.items?.data?.[0]?.current_period_start) {
        currentPeriodStart = new Date(
          subscription.items.data[0].current_period_start * 1000
        );
      } else if (subscription.current_period_start) {
        currentPeriodStart = new Date(subscription.current_period_start * 1000);
      }

      // Set the start date, expiry date, and next billing date using the Unix timestamps
      newSubscription.startDate = startDate;
      newSubscription.expiryDate = currentPeriodEnd;
      console.log("newSubscription.expiryDate", newSubscription.expiryDate);
      newSubscription.nextBillingDate = currentPeriodEnd;

      // Update subscription status based on the Stripe status
      const stripeStatus = subscription.status;
      switch (stripeStatus) {
        case "active":
          newSubscription.status = SubscriptionStatus.ACTIVE;
          break;
        case "canceled":
          newSubscription.status = SubscriptionStatus.CANCELED;
          break;
        case "incomplete":
          newSubscription.status = SubscriptionStatus.INCOMPLETE;
          break;
        case "incomplete_expired":
          newSubscription.status = SubscriptionStatus.INCOMPLETE;
          break;
        case "past_due":
          newSubscription.status = SubscriptionStatus.PAST_DUE;
          break;
        default:
          // Default to active if the status is unrecognized
          newSubscription.status = SubscriptionStatus.ACTIVE;
          break;
      }

      // Set isActive flag based on subscription status and expiry date
      // Use UTC for consistent timezone handling
      const today = new Date();
      console.log("today (UTC):", today.toISOString());
      console.log(
        "newSubscription.expiryDate (UTC):",
        newSubscription.expiryDate.toISOString()
      );
      const isExpired = today >= newSubscription.expiryDate;
      console.log("isExpired", isExpired);
      console.log("newSubscription", newSubscription);

      // Set isActive based on status and expiry
      if (isExpired) {
        console.log("newSubscription", newSubscription);
        // If expired, always set to false regardless of status
        newSubscription.isActive = false;
      } else if (newSubscription.status === SubscriptionStatus.CANCELED) {
        // If canceled, set isActive to false
        newSubscription.isActive = false;
      } else if (newSubscription.status === SubscriptionStatus.ACTIVE) {
        console.log("newSubscription", newSubscription);
        // If active and not expired, set isActive to true
        newSubscription.isActive = true;
      } else if (newSubscription.status === SubscriptionStatus.PAST_DUE) {
        // For past_due, keep isActive=true until expiry date since it's still usable until then
        newSubscription.isActive = true;
      } else {
        console.log("newSubscription", newSubscription);
        // For other statuses like INCOMPLETE, set isActive to false
        newSubscription.isActive = false;
      }

      console.log(
        `Setting subscription isActive to ${newSubscription.isActive} based on status ${newSubscription.status}`
      );

      // Set subscription type from metadata
      newSubscription.subscriptionType =
        SubscriptionType[metadata.pricingType as keyof typeof SubscriptionType];

      // Set created/updated timestamps
      newSubscription.createdTs = currentPeriodStart;
      newSubscription.updatedTs = new Date();

      // Save the new subscription
      await orgSubsRepo.save(newSubscription);
      console.log("New subscription saved for organization", newSubscription);

      // Step 3: Create organization subscription benefits based on the plan
      await SubscriptionServices.syncSubscriptionBenefits(orgId, planId);

      // Step 3: Create a transaction record
      // const transaction = new SubscriptionTransactionModel();
      // transaction.organizationId = orgId;
      // transaction.organizationSubscriptionId = newSubscription.id; // Set organization subscription ID
      // // transaction.stripePaymentId = subscription.latest_invoice.payment_intent; // Add the Stripe payment ID
      // transaction.paymentStatus = PaymentStatus.SUCCESS; // You can change this depending on the status of the payment
      // transaction.transactionDate = new Date();

      // // Save the transaction
      // await transactionRepo.save(transaction);
      // console.log("Transaction record saved for the new subscription");

      // Get the plan name for the new subscription
      // Use the existing planRepo repository
      const newPlan = await planRepo.findOne({
        where: { id: planId },
      });
      const newPlanName = newPlan?.name || "S9 InnerView Subscription";

      // Determine email type based on whether there was an existing subscription
      let emailType = SUBSCRIPTION_EMAIL_TYPE.PURCHASE; // Default to purchase
      let oldPlanName: string | undefined;

      if (existingSubscription) {
        // Get the old plan name for upgrade/downgrade emails
        const oldPlan = await planRepo.findOne({
          where: { id: existingSubscription.subscriptionId },
        });
        oldPlanName = oldPlan?.name || "Previous Plan";

        // Get pricing information to determine if it's upgrade or downgrade
        const pricingRepo = await dbConnection.getS9InnerViewDatabaseRepository(
          SubscriptionPricingModel
        );

        const newPricing = await pricingRepo.findOne({
          where: { id: pricingId },
        });

        const oldPricing = await pricingRepo.findOne({
          where: { id: existingSubscription.subscriptionPricingId },
        });

        if (newPricing && oldPricing) {
          // Compare prices to determine upgrade vs downgrade
          if (newPricing.price > oldPricing.price) {
            emailType = SUBSCRIPTION_EMAIL_TYPE.UPGRADE;
            console.log(
              `This is an UPGRADE from ${oldPlanName} ($${oldPricing.price}) to ${newPlanName} ($${newPricing.price})`
            );
          } else if (newPricing.price < oldPricing.price) {
            emailType = SUBSCRIPTION_EMAIL_TYPE.DOWNGRADE;
            console.log(
              `This is a DOWNGRADE from ${oldPlanName} ($${oldPricing.price}) to ${newPlanName} ($${newPricing.price})`
            );
          } else {
            // Same price, could be a plan change with same cost
            emailType = SUBSCRIPTION_EMAIL_TYPE.PURCHASE;
            console.log(
              `Plan change with same price from ${oldPlanName} to ${newPlanName}`
            );
          }
        } else {
          console.warn(
            "Could not fetch pricing information for upgrade/downgrade determination"
          );
          emailType = SUBSCRIPTION_EMAIL_TYPE.PURCHASE;
        }
      } else {
        console.log("This is a new subscription purchase");
      }

      // Send subscription email to both user and organization
      try {
        await this.sendSubscriptionEmailToBoth(
          userId,
          orgId,
          emailType,
          newPlanName,
          oldPlanName
        );
        console.log(
          `✅ Subscription ${emailType} emails sent for user ${userId} and organization ${orgId}`
        );
      } catch (emailError) {
        console.error(
          "Error sending subscription creation emails:",
          emailError
        );
        Sentry.captureException(emailError);
      }

      console.log(`Subscription created in Stripe: ${subscription.id}`);
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error handling subscription created event:", error);
    }
  }

  /**
   * Handle subscription updated event
   * @param subscription Subscription object
   */

  static async handleSubscriptionUpdated(subscription: ISubscription) {
    console.log("🔄 [DEBUG] Starting handleSubscriptionUpdated");
    console.log("🔄 [DEBUG] Subscription ID:", subscription.id);
    console.log("🔄 [DEBUG] Subscription Status:", subscription.status);
    console.log(
      "🔄 [DEBUG] Cancel at period end:",
      subscription.cancel_at_period_end
    );
    console.log(
      "🔄 [DEBUG] Full subscription object:",
      JSON.stringify(subscription, null, 2)
    );

    // Get metadata from subscription
    const metadata = subscription.metadata || {};
    const userId = parseInt(metadata.userId, 10);
    const orgId = parseInt(metadata.orgId, 10);
    console.log("🔄 [DEBUG] Extracted userId:", userId);
    console.log("🔄 [DEBUG] Extracted orgId:", orgId);

    try {
      console.log("🔄 [DEBUG] Getting organization subscription repository...");
      // Get repository for organization subscriptions
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );
      console.log("🔄 [DEBUG] Repository obtained successfully");

      console.log(
        "🔄 [DEBUG] Searching for subscription in database with Stripe ID:",
        subscription.id
      );
      // Get the subscription from our database using the stripe subscription ID
      const dbSubscription = await orgSubsRepo.findOne({
        where: { stripeSubscriptionId: subscription.id },
      });
      console.log("🔄 [DEBUG] Database subscription found:", !!dbSubscription);
      if (dbSubscription) {
        console.log("🔄 [DEBUG] DB Subscription details:", {
          id: dbSubscription.id,
          organizationId: dbSubscription.organizationId,
          currentStatus: dbSubscription.status,
          isActive: dbSubscription.isActive,
          expiryDate: dbSubscription.expiryDate,
          startDate: dbSubscription.startDate,
        });
      }

      if (dbSubscription) {
        console.log(
          "🔄 [DEBUG] Processing subscription update for organization:",
          dbSubscription.organizationId
        );
        // Determine the subscription status in our system based on the Stripe status
        let newStatus: SubscriptionStatus;
        console.log("🔄 [DEBUG] Starting status determination logic...");

        // CONDITION 1: Check if plan is being canceled at end of billing cycle
        console.log("🔄 [DEBUG] CONDITION 1: Checking cancel_at_period_end");
        console.log("cancel_at_period_end", subscription.cancel_at_period_end);
        console.log("cancellation_reason", subscription.cancellation_reason);
        console.log("status", subscription.status);

        if (subscription.cancel_at_period_end === true) {
          // Plan will be canceled at the end of the current billing cycle
          newStatus = SubscriptionStatus.CANCEL_AT_PERIOD_END;
          console.log(
            `Subscription ${dbSubscription.id} will be canceled at the end of billing cycle. Status: ${newStatus}`
          );
          const today = new Date();

          // Update local subscription status to CANCEL_AT_PERIOD_END
          dbSubscription.status = SubscriptionStatus.CANCEL_AT_PERIOD_END;
          dbSubscription.cancellationDate = today;

          // Keep isActive=true until expiry date since it's still usable until then
          // Only set isActive to false if the expiry date is in the past
          dbSubscription.isActive = today < dbSubscription.expiryDate;
          dbSubscription.updatedTs = today;

          // If we have cancellation date from Stripe, record it
          if (subscription.canceled_at) {
            dbSubscription.cancellationDate = new Date(
              subscription.canceled_at * 1000
            );
          }
        }
        // CONDITION 2: Check if plan is being renewed at billing cycle
        // Compare UTC timestamps from Stripe with UTC timestamps in database
        else if (
          subscription.status === "active" &&
          subscription.current_period_start &&
          new Date(subscription.current_period_start * 1000) >
            dbSubscription.startDate
        ) {
          // Subscription has been renewed for a new billing period
          newStatus = SubscriptionStatus.ACTIVE;
          console.log(
            `Subscription ${dbSubscription.id} has been renewed for a new billing cycle. Status: ${newStatus}`
          );

          // Clear any previous cancellation info since it's been renewed
          dbSubscription.cancellationDate = null;
        } else {
          // Handle other subscription status updates from Stripe
          switch (subscription.status) {
            case "active":
              newStatus = SubscriptionStatus.ACTIVE;
              break;
            case "canceled":
              newStatus = SubscriptionStatus.CANCELED;
              break;
            case "incomplete":
            case "incomplete_expired":
              newStatus = SubscriptionStatus.INCOMPLETE;
              break;
            case "past_due":
              newStatus = SubscriptionStatus.PAST_DUE;
              break;
            default:
              // For other statuses like 'trialing' or 'unpaid', use PAST_DUE as fallback
              newStatus = SubscriptionStatus.PAST_DUE;
              break;
          }
        }

        // Convert Unix timestamps from Stripe to JavaScript Date objects
        // Get period end from first subscription item if available, otherwise try root level
        let currentPeriodEnd = null;
        if (subscription.items?.data?.[0]?.current_period_end) {
          currentPeriodEnd = new Date(
            subscription.items.data[0].current_period_end * 1000
          );
        } else if (subscription.current_period_end) {
          currentPeriodEnd = new Date(subscription.current_period_end * 1000);
        }

        if (currentPeriodEnd) {
          console.log(
            "Updated subscription - currentPeriodEnd (UTC):",
            currentPeriodEnd.toISOString()
          );
          // Update expiry and billing dates with UTC timestamps from Stripe
          dbSubscription.expiryDate = currentPeriodEnd;
          dbSubscription.nextBillingDate = currentPeriodEnd;
        }

        // Get period start similarly - all Stripe timestamps are UTC
        let currentPeriodStart = null;
        if (subscription.items?.data?.[0]?.current_period_start) {
          currentPeriodStart = new Date(
            subscription.items.data[0].current_period_start * 1000
          );
        } else if (subscription.current_period_start) {
          currentPeriodStart = new Date(
            subscription.current_period_start * 1000
          );
        }

        if (currentPeriodStart) {
          console.log(
            "Updated subscription - currentPeriodStart (UTC):",
            currentPeriodStart.toISOString()
          );
          // Update startDate if this is a new billing period
          // Compare UTC timestamps for accurate billing period detection
          if (
            !dbSubscription.startDate ||
            dbSubscription.startDate < currentPeriodStart
          ) {
            dbSubscription.startDate = currentPeriodStart;
          }
        }

        // Update the subscription status
        dbSubscription.status = newStatus;

        // Update isActive based on status and expiry date
        const today = new Date();

        // Check if subscription is expired
        const isExpired = today >= dbSubscription.expiryDate;

        // Set isActive based on status and expiry
        if (isExpired) {
          // If expired, always set to false regardless of status
          dbSubscription.isActive = false;
        } else if (newStatus === SubscriptionStatus.CANCELED) {
          // If canceled, set isActive to false
          dbSubscription.isActive = false;
        } else if (newStatus === SubscriptionStatus.ACTIVE) {
          // If active and not expired, set isActive to true
          dbSubscription.isActive = true;
        } else if (newStatus === SubscriptionStatus.CANCEL_AT_PERIOD_END) {
          // If canceling at period end, keep isActive true until expiry
          dbSubscription.isActive = true;
        } else if (newStatus === SubscriptionStatus.PAST_DUE) {
          // For past_due, set isActive to false to prevent access during payment issues
          // This ensures users don't get access when they have insufficient payment
          dbSubscription.isActive = false;
        } else {
          // For other statuses like INCOMPLETE, set isActive to false
          dbSubscription.isActive = false;
        }

        dbSubscription.updatedTs = new Date();
        await orgSubsRepo.save(dbSubscription);

        // Update organization subscription benefits when subscription is renewed or changed
        // Only update benefits if subscription is still active
        if (
          dbSubscription.isActive &&
          dbSubscription.status === SubscriptionStatus.ACTIVE
        ) {
          await SubscriptionServices.syncSubscriptionBenefits(
            dbSubscription.organizationId,
            dbSubscription.subscriptionId // This is the planId since subscriptionId stores the plan ID in the database
          );
          console.log(
            `Updated benefits for organization ${dbSubscription.organizationId} based on subscription update.`
          );
        }

        // Send email notifications based on subscription status changes
        if (userId && orgId) {
          try {
            let emailType: string | null = null;
            let planName: string | null = null;

            // Get plan name from database
            try {
              const planRepo =
                await dbConnection.getS9InnerViewDatabaseRepository(
                  SubscriptionPlanModel
                );
              const plan = await planRepo.findOne({
                where: { id: dbSubscription.subscriptionId },
              });
              if (plan) {
                planName = plan.name;
              }
            } catch (planError) {
              console.warn("Could not fetch plan name:", planError);
            }

            // Determine email type based on subscription status change
            if (
              newStatus === SubscriptionStatus.ACTIVE &&
              subscription.current_period_start
            ) {
              // Check if this is a renewal by comparing current_period_start with previous startDate
              if (
                dbSubscription.startDate &&
                currentPeriodStart > dbSubscription.startDate
              ) {
                // This is a renewal
                emailType = SUBSCRIPTION_EMAIL_TYPE.RENEW;
              }
            } else if (newStatus === SubscriptionStatus.CANCEL_AT_PERIOD_END) {
              // For cancel at period end, we've already handled this in cancelSubscription method
              // No need to send another email here
            } else if (newStatus === SubscriptionStatus.PAST_DUE) {
              emailType = SUBSCRIPTION_EMAIL_TYPE.PAYMENT_FAILED;
            }

            // If email type is determined, send the email
            if (emailType) {
              await this.sendSubscriptionEmailToBoth(
                userId,
                orgId,
                emailType,
                planName
              );
              console.log(
                `✅ Subscription ${emailType} emails sent for user ${userId} and organization ${orgId}`
              );
            }
          } catch (emailError) {
            console.error(
              "Error sending subscription status change emails:",
              emailError
            );
            Sentry.captureException(emailError);
          }
        } else {
          console.warn(
            "Missing userId or orgId in subscription metadata, skipping email notifications"
          );
        }

        console.log(
          `Subscription ${dbSubscription.id} updated with status ${newStatus}, isActive: ${dbSubscription.isActive}`
        );
      } else {
        console.warn(
          `No matching subscription found for Stripe subscription ID: ${subscription.id}`
        );
      }

      console.log(`Subscription updated in Stripe: ${subscription.id}`);
    } catch (error) {
      console.error("Error handling subscription update:", error);
      Sentry.captureException(error);
    }
  }

  /**
   * Handle subscription deleted event
   * @param subscription Subscription object
   */
  static async handleSubscriptionDeleted(subscription: ISubscription) {
    try {
      console.log("Processing subscription deletion event:", subscription.id);

      // Get metadata from subscription
      const metadata = subscription.metadata || {};
      const userId = parseInt(metadata.userId, 10);
      const orgId = parseInt(metadata.orgId, 10);
      console.log("🗑️ [DEBUG] Extracted userId:", userId);
      console.log("🗑️ [DEBUG] Extracted orgId:", orgId);

      // Get repositories for plans
      const planRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        SubscriptionPlanModel
      );

      // Find the subscription in the database by Stripe subscription ID with retry
      // This helps handle race conditions where the webhook arrives before the subscription record is created
      console.log("Looking for subscription with Stripe ID:", subscription.id);
      const existingSubscription =
        await SubscriptionServices.findSubscriptionWithRetry(
          subscription.id,
          5, // Maximum 5 attempts
          1000 // Starting with 1 second delay
        );

      // Get repository for organization subscriptions (needed for updates)
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );

      if (existingSubscription) {
        console.log(
          "Found existing subscription in database:",
          existingSubscription.id
        );

        // Avoid double processing by checking the current status
        if (
          existingSubscription.status === SubscriptionStatus.CANCELED ||
          existingSubscription.status === SubscriptionStatus.EXPIRED
        ) {
          console.log(
            `Subscription with Stripe ID ${subscription.id} has already been processed.`
          );
          return;
        }

        // Convert Unix timestamp for cancellation if available, otherwise use current date
        const cancellationDate = subscription.canceled_at
          ? new Date(subscription.canceled_at * 1000)
          : new Date();

        // Initialize Stripe client to get detailed subscription information
        // const stripe = await SubscriptionServices.initializeStripe();

        // Determine cancellation type using cancel_at_period_end from Stripe
        let isImmediateCancellation = false;
        let emailType = SUBSCRIPTION_EMAIL_TYPE.DOWNGRADE;
        let statusMessage = "";
        let shouldCreateFreePlan = false;

        // Get period end from subscription
        let currentPeriodEnd = new Date();
        if (subscription.items?.data?.[0]?.current_period_end) {
          currentPeriodEnd = new Date(
            subscription.items.data[0].current_period_end * 1000
          );
        } else if (subscription.current_period_end) {
          currentPeriodEnd = new Date(subscription.current_period_end * 1000);
        }

        console.log("cancel_at_period_end:", subscription.cancel_at_period_end);
        console.log("Cancellation date:", cancellationDate);
        console.log("Current period end:", currentPeriodEnd);

        // Use cancel_at_period_end to determine cancellation behavior
        if (subscription.cancel_at_period_end === false) {
          // Immediate cancellation - don't create free plan
          isImmediateCancellation = true;
          shouldCreateFreePlan = false;
          console.log(
            "This is an immediate cancellation (cancel_at_period_end: false)"
          );
          statusMessage = "immediately canceled";
          emailType = SUBSCRIPTION_EMAIL_TYPE.CANCEL;
          existingSubscription.status = SubscriptionStatus.CANCELED;
        } else {
          // Expiration at period end - create free plan
          isImmediateCancellation = false;
          shouldCreateFreePlan = true;
          console.log(
            "This is a subscription expiration (cancel_at_period_end: true)"
          );
          statusMessage = "expired";
          emailType = SUBSCRIPTION_EMAIL_TYPE.EXPIRED;
          existingSubscription.status = SubscriptionStatus.EXPIRED;
        }

        // Update subscription fields
        existingSubscription.cancellationDate = cancellationDate;
        existingSubscription.expiryDate = currentPeriodEnd || cancellationDate; // Use period end if available
        existingSubscription.updatedTs = new Date();
        existingSubscription.isActive = false; // Set isActive to false in both cases
        await orgSubsRepo.save(existingSubscription);

        console.log(
          `Subscription with Stripe ID ${subscription.id} marked as ${statusMessage} in the database.`
        );

        // Create a new free plan subscription only for expiration (not immediate cancellation)
        if (shouldCreateFreePlan) {
          console.log("Creating free plan subscription after expiration...");
          try {
            const freePlan = await planRepo.findOne({
              where: {
                paymentType: PaymentType.FREE,
                isActive: true,
              },
            });

            if (freePlan) {
              // Calculate dates for the free plan (1 month from now)
              const today = new Date();
              const oneMonthFromNow = today;
              oneMonthFromNow.setMonth(today.getMonth() + 1); // Expiry date set to one month from today

              // Create a new subscription entry for the free plan
              const freeSubscription = new OrganizationSubscriptionModel();
              freeSubscription.organizationId =
                existingSubscription.organizationId;
              freeSubscription.subscriptionId = freePlan.id;
              freeSubscription.subscriptionType = SubscriptionType.MONTHLY;
              // freeSubscription.subscription = freePlan;
              freeSubscription.status = SubscriptionStatus.ACTIVE;
              freeSubscription.startDate = today;
              freeSubscription.expiryDate = oneMonthFromNow;
              freeSubscription.nextBillingDate = oneMonthFromNow;
              freeSubscription.isActive = true;
              freeSubscription.createdTs = today;
              freeSubscription.updatedTs = today;

              // Save the free subscription
              await orgSubsRepo.save(freeSubscription);

              // Update organization subscription benefits for the free plan
              await SubscriptionServices.syncSubscriptionBenefits(
                freeSubscription.organizationId,
                freeSubscription.subscriptionId // This is the planId since subscriptionId stores the plan ID in the database
              );
              console.log(
                `Free plan subscription created and benefits updated for organization ${existingSubscription.organizationId} after subscription deletion/expiration.`
              );

              // Send appropriate email notification
              if (userId && orgId) {
                try {
                  await this.sendSubscriptionEmailToBoth(
                    userId,
                    orgId,
                    emailType,
                    freePlan.name
                  );
                  console.log(
                    `✅ ${isImmediateCancellation ? "Cancellation" : "Downgrade"} emails sent for user ${userId} and organization ${orgId}`
                  );
                } catch (emailError) {
                  console.error(
                    `Error sending ${isImmediateCancellation ? "cancellation" : "downgrade"} emails:`,
                    emailError
                  );
                  Sentry.captureException(emailError);
                }
              } else {
                console.warn(
                  "Missing userId or orgId in subscription metadata, skipping email notifications"
                );
              }
            } else {
              console.warn(
                "No free plan found in the subscription plans table."
              );
            }
          } catch (freePlanError) {
            console.error(
              "Error creating free plan subscription:",
              freePlanError
            );
            Sentry.captureException(freePlanError);
          }
        } else {
          console.log(
            "Skipping free plan creation for immediate cancellation."
          );
        }
      } else {
        console.warn(
          `No matching subscription found for Stripe subscription ID: ${subscription.id}`
        );
      }
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error handling subscription deleted event:", error);
    }
  }

  /**
   * Cancel user's subscription
   * @param userId User ID whose subscription to cancel
   * @returns Result of cancellation
   */
  // Cancel user's subscription but expire at the end of the current billing cycle
  static async cancelSubscription(orgId: number) {
    try {
      // Get repository for organization subscriptions
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );

      // Get the user's current subscription
      const subscription = await orgSubsRepo.findOne({
        where: {
          organizationId: orgId,
          status: SubscriptionStatus.ACTIVE,
          isActive: true,
        },
        order: {
          createdTs: "DESC", // Get the most recently created subscription
        },
      });

      console.log(" subscription", subscription);

      if (!subscription) {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.no_active_subscription,
          data: null,
        };
      }

      // Initialize Stripe
      const stripe = await SubscriptionServices.initializeStripe();

      // Check if Stripe subscription ID exists
      if (subscription.stripeSubscriptionId) {
        try {
          // Don't cancel in Stripe yet, just update status locally
          const stripeSubscription = await stripe.subscriptions.retrieve(
            subscription.stripeSubscriptionId
          );

          console.log(
            "stripeSubscriptionsubscription<<<<<<<<<<<<<<<<",
            subscription
          );

          console.log(
            ">>>>>>>>>>>>>>>>>stripeSubscription",
            stripeSubscription
          );

          // Mark subscription as expired in the local system but keep it active until the expiry date
          if (stripeSubscription) {
            const metadata = {
              organization_id: orgId, // Only send the organizationId as metadata
            };

            await stripe.subscriptions.update(
              subscription.stripeSubscriptionId,
              {
                cancel_at_period_end: true,
                metadata,
              }
            );

            // Poll the database to verify the subscription status has been updated to CANCEL_AT_PERIOD_END
            console.log(
              "Polling database to verify subscription status update..."
            );
            let attempts = 0;
            const maxAttempts = 10;
            let statusUpdated = false;
            let updatedSubscription = null;

            while (attempts < maxAttempts && !statusUpdated) {
              console.log(
                `Checking subscription status in database (attempt ${attempts + 1}/${maxAttempts})`
              );

              // Wait for 1 second between attempts
              // We need to wait for the webhook to update the subscription status
              // eslint-disable-next-line no-await-in-loop
              await SubscriptionServices.delay(1000);

              // Fetch the latest subscription status from database
              // eslint-disable-next-line no-await-in-loop
              updatedSubscription = await orgSubsRepo.findOne({
                where: {
                  id: subscription.id,
                },
              });

              if (
                updatedSubscription &&
                updatedSubscription.status ===
                  SubscriptionStatus.CANCEL_AT_PERIOD_END
              ) {
                statusUpdated = true;
                console.log(
                  "Subscription status successfully updated to CANCEL_AT_PERIOD_END in database."
                );
                console.log(
                  `Subscription ${updatedSubscription.id} status: ${updatedSubscription.status}, isActive: ${updatedSubscription.isActive}`
                );
              } else {
                console.log(
                  `Subscription status not yet updated (current: ${updatedSubscription?.status || "unknown"}), retrying...`
                );
              }

              attempts += 1;
            }

            // Store polling result to return after email notification
            const pollingResult = statusUpdated
              ? {
                  success: true,
                  message: SUBSCRIPTION_MSG.subscription_canceled,
                  data: {
                    updatedSubscription,
                  },
                }
              : {
                  success: false,
                  message: API_RESPONSE_MSG.failed,
                  data: null,
                };

            // Send email notification using the unified helper method
            try {
              // Get userId from Stripe subscription metadata
              const metadatas = stripeSubscription.metadata || {};
              const userId = parseInt(metadatas.userId, 10);

              if (userId) {
                // Get plan details for email
                const planRepo =
                  await dbConnection.getS9InnerViewDatabaseRepository(
                    SubscriptionPlanModel
                  );
                const plan = await planRepo.findOne({
                  where: { id: subscription.subscriptionId },
                });

                const planName = plan?.name || "S9 InnerView Subscription";

                // Use the unified email helper to send to both user and org admin
                await this.sendSubscriptionEmailToBoth(
                  userId,
                  orgId,
                  SUBSCRIPTION_EMAIL_TYPE.CANCEL,
                  planName
                );

                console.log(
                  `✅ Subscription cancellation emails sent to both user ${userId} and organization ${orgId}`
                );
              } else {
                console.warn(
                  `No valid userId found in Stripe subscription metadata for subscription ${subscription.stripeSubscriptionId}. Skipping email notifications.`
                );
              }
            } catch (emailError) {
              console.error(
                "Error sending subscription cancellation emails:",
                emailError
              );
              Sentry.captureException(emailError);
              // Email failure should not prevent the subscription cancellation
            }

            console.log(
              `Subscription ${subscription.id} status updated to ${subscription.status}, isActive: ${subscription.isActive} until expiry date.`
            );

            // Return the result from polling
            return pollingResult;
          }
        } catch (error) {
          console.error("Error retrieving subscription from Stripe:", error);
          Sentry.captureException(error);
        }
      } else {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.stripeSubscriptionId_not_found,
          data: null,
        };
      }
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: SUBSCRIPTION_MSG.webhook_error,
        data: null,
      };
    }
    return {
      success: false,
      message: API_RESPONSE_MSG.failed,
      data: null,
    };
  }

  /**
   * Get all transaction details for an organization with pagination support
   * @param orgId Organization ID
   * @param offset Number of records to skip for pagination
   * @param limit Maximum number of records to return
   * @returns Paginated transaction details
   */
  static async getAllTransactions(
    orgId: number,
    offset: number = 0,
    limit: number = 10
  ): Promise<IPaginatedTransactionsResponse> {
    try {
      // Get organization subscription for this organization to check if it exists
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );

      const orgSubscriptions = await orgSubsRepo.find({
        where: { organizationId: orgId },
      });

      if (!orgSubscriptions || orgSubscriptions.length === 0) {
        // Return empty paginated response when no subscriptions exist
        return {
          success: false,
          message: SUBSCRIPTION_MSG.no_active_subscription,
          data: {
            transactions: [],
            pagination: {
              total: 0,
              offset,
              limit,
              hasMore: false,
            },
          },
        };
      }

      // Get transaction repository
      const transactionRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          SubscriptionTransactionModel
        );

      // Get all transactions for this organization directly using organizationId with pagination
      const transactions = await transactionRepo.find({
        where: { organizationId: orgId },
        order: { createdTs: "DESC" },
        skip: offset,
        take: limit,
      });

      // Get total count for pagination metadata
      const totalCount = await transactionRepo.count({
        where: { organizationId: orgId },
      });

      // Map to response format based on the database table columns
      // Adjusting property names to match the ITransactionData interface
      const formattedTransactions: ITransactionData[] = transactions.map(
        (transaction) => ({
          id: transaction.id,
          payment_status: transaction.paymentStatus,
          amount: transaction.amount,
          transaction_type: transaction.transactionType,
          transaction_method: transaction.transactionMethod,
          transaction_date:
            transaction.transactionDate || transaction.createdTs,
          invoice_id: transaction.invoiceId,
          invoice_url: transaction.invoiceUrl,
        })
      );

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: {
          transactions: formattedTransactions,
          pagination: {
            total: totalCount,
            offset,
            limit,
            hasMore: offset * limit + transactions.length < totalCount,
          },
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      // Return empty paginated response for error case
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        data: {
          transactions: [],
          pagination: {
            total: 0,
            offset,
            limit,
            hasMore: false,
          },
        },
      };
    }
  }

  /**
   * Creates a free subscription for a new organization if no active plan exists
   * @param organizationId - The ID of the organization
   * @returns void
   */
  static async createFreeSubscription(organizationId: number): Promise<void> {
    try {
      // Get repositories for plan and organization subscriptions
      const orgSubsRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        OrganizationSubscriptionModel
      );

      console.log("Organization subscription repository initialized.");
      // Check for existing active subscriptions
      const activeSubscription = await orgSubsRepo.findOne({
        where: {
          organizationId,
          isActive: true,
        },
        order: {
          createdTs: "DESC",
        },
      });

      console.log(
        `Checking for active subscription for organization ${organizationId}...`,
        activeSubscription
      );

      // If there's an active subscription, don't create a free one
      if (activeSubscription) {
        console.log(
          `Organization ${organizationId} already has an active subscription plan (ID: ${activeSubscription.subscriptionId}). Not creating a free plan.`
        );
        return; // Exit early, no need to create free plan
      }

      // At this point, we know there's no active subscription
      // Check if there are any expired subscriptions
      const expiredSubscriptions = await orgSubsRepo.find({
        where: {
          organizationId,
          isActive: false,
        },
        order: {
          updatedTs: "DESC", // Get the most recent expired subscription first
        },
      });

      console.log(
        "Checking for expired subscriptions...expiredSubscriptions",
        expiredSubscriptions
      );

      if (expiredSubscriptions && expiredSubscriptions.length > 0) {
        console.log(
          `Organization ${organizationId} has ${expiredSubscriptions.length} expired subscription(s). Creating free plan.`
        );
      } else {
        console.log(
          `Organization ${organizationId} has no subscriptions. Creating initial free plan.`
        );
      }

      // Find the free plan with ID 1
      const planRepo = await dbConnection.getS9InnerViewDatabaseRepository(
        SubscriptionPlanModel
      );

      const freePlan = await planRepo.findOne({
        where: {
          paymentType: PaymentType.FREE,
          isActive: true,
        },
      });

      console.log("Free plan found:", freePlan);

      if (freePlan) {
        // Calculate dates for the free plan (1 month from now)
        const today = new Date();
        const oneMonthFromNow = new Date(today); // Create a new date object to avoid mutation
        oneMonthFromNow.setMonth(today.getMonth() + 1); // Expiry date set to one month from today

        // Create a new subscription entry for the free plan
        const freeSubscription = new OrganizationSubscriptionModel();
        freeSubscription.organizationId = organizationId;
        freeSubscription.subscriptionId = freePlan.id;

        // Use enum values from imported SubscriptionType and SubscriptionStatus
        freeSubscription.subscriptionType = SubscriptionType.MONTHLY;
        freeSubscription.status = SubscriptionStatus.ACTIVE;

        // Set required dates
        freeSubscription.startDate = today;
        freeSubscription.expiryDate = oneMonthFromNow;
        freeSubscription.nextBillingDate = oneMonthFromNow;

        freeSubscription.isActive = true;
        freeSubscription.createdTs = today;
        freeSubscription.updatedTs = today;

        // Save the new free subscription
        await orgSubsRepo.save(freeSubscription);

        console.log(">>>>>>>>>>>>>>>>>> Free plan subscription created:");

        // Update organization subscription benefits for the free plan
        await SubscriptionServices.syncSubscriptionBenefits(
          organizationId,
          freePlan.id // Explicitly using freePlan.id as the plan ID
        );

        console.log(
          `Free plan subscription and benefits created for organization ${organizationId}.`
        );
      } else {
        console.warn("No free plan found in the subscription plans table.");
      }
    } catch (subscriptionError) {
      console.error(
        "Error creating free subscription for new organization:",
        subscriptionError
      );
      Sentry.captureException(subscriptionError);
      // Continue with the organization setup even if subscription creation fails
    }
  }

  // /**
  //  * Get appropriate error message for benefit quota
  //  * @param benefitSlug - Benefit slug
  //  * @param quotaValue - Current quota value
  //  * @returns Appropriate error message
  //  */
  // private static getBenefitErrorMessage(
  //   benefitSlug: string,
  //   quotaValue: number
  // ): string {
  //   if (benefitSlug === BENEFIT_SLUGS.JOB_POSTINGS) {
  //     return SUBSCRIPTION_MSG.job_posting_limit_reached;
  //   }
  //   if (benefitSlug === BENEFIT_SLUGS.RESUME_SCREENING) {
  //     return SUBSCRIPTION_MSG.resume_screening_limit_reached;
  //   }
  //   return SUBSCRIPTION_MSG.manual_resume_upload_limit_reached.replace(
  //     "{quota_value}",
  //     quotaValue.toString()
  //   );
  // }

  /**
   * Checks if an organization has available quota for a specific benefit (validation only)
   * @param organizationId The organization ID
   * @param benefitSlug The benefit slug to check (e.g., 'job_postings')
   * @param value The value to check availability for (default: 1 for job_postings, can be 2-3 for others)
   * @returns Object with success status, message, remaining quota, and benefit details
   */
  public static async checkBenefitQuota(
    organizationId: number,
    benefitSlug: string,
    value: number = 1
  ) {
    try {
      console.log("Checking benefit quota for organization:", organizationId);
      const s9InnerviewDataSource =
        await dbConnection.getS9InnerviewDataSource();
      const benefitsRepo = s9InnerviewDataSource.getRepository(
        OrganizationSubscriptionBenefitModel
      );

      // Get the organization's subscription benefits
      const benefits = await benefitsRepo.findOne({
        where: { organizationId },
      });

      // If no benefits found, return error
      if (!benefits) {
        return {
          success: false,
          message:
            SUBSCRIPTION_MSG.no_subscription_plan_found_and_no_free_plan_available,
          remainingQuota: 0,
        };
      }

      let quotaValue = 0;
      let propertyName = "";

      // Determine which property to check based on the benefit slug
      switch (benefitSlug) {
        case BENEFIT_SLUGS.JOB_POSTINGS:
          quotaValue = benefits.jobPostings;
          propertyName = "jobPostings";
          break;
        case BENEFIT_SLUGS.RESUME_SCREENING:
          quotaValue = benefits.resumeScreening;
          propertyName = "resumeScreening";
          break;
        case BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD:
          quotaValue = benefits.manualResumeUpload;
          propertyName = "manualResumeUpload";
          break;
        default:
          return {
            success: false,
            message: SUBSCRIPTION_MSG.invalid_benefit_type,
            remainingQuota: 0,
          };
      }

      console.log(
        `Checking quota for benefit: ${benefitSlug}, current value: ${quotaValue}, requested value: ${value}`
      );

      // Check if quota is available
      // Case 1: Unlimited quota (-1)
      if (quotaValue === -1) {
        return {
          success: true,
          message: SUBSCRIPTION_MSG.benefit_quota_available,
          remainingQuota: -1, // Unlimited
          benefitDetails: {
            propertyName,
            currentValue: quotaValue,
            benefits,
            isUnlimited: true,
          },
        };
      }

      // Case 2: No quota available (0)
      if (quotaValue === 0) {
        return {
          success: false,
          message: this.getBenefitErrorMessage(benefitSlug, quotaValue),
          remainingQuota: 0,
        };
      }

      // Case 2: Requested value is more than available quota (but quota > 0)
      if (quotaValue > 0 && quotaValue < value) {
        return {
          success: false,
          message: this.getBenefitErrorMessage(benefitSlug, quotaValue),
          remainingQuota: quotaValue,
          availableQuota: quotaValue,
          requestedValue: value,
        };
      }

      return {
        success: true,
        message: SUBSCRIPTION_MSG.benefit_quota_available,
        remainingQuota: quotaValue,
        benefitDetails: {
          propertyName,
          currentValue: quotaValue,
          benefits,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error checking benefit quota:", error);
      return {
        success: false,
        message: SUBSCRIPTION_MSG.subscription_benefit_check_error,
        remainingQuota: 0,
        error,
      };
    }
  }

  /**
   * Get appropriate error message for benefit quota limits
   * @param benefitSlug - The benefit slug to get message for
   * @param quotaValue - The current quota value for dynamic message replacement
   * @returns The appropriate error message
   */
  private static getBenefitErrorMessage(
    benefitSlug: string,
    quotaValue: number
  ): string {
    if (benefitSlug === BENEFIT_SLUGS.JOB_POSTINGS) {
      return SUBSCRIPTION_MSG.job_posting_limit_reached;
    }

    if (benefitSlug === BENEFIT_SLUGS.RESUME_SCREENING) {
      return SUBSCRIPTION_MSG.resume_screening_limit_reached;
    }

    // For MANUAL_RESUME_UPLOAD or any other benefit
    return SUBSCRIPTION_MSG.manual_resume_upload_limit_reached.replace(
      "{quota_value}",
      quotaValue.toString()
    );
  }

  /**
   * Decrement benefit quota for an organization
   * @param organizationId - Organization ID
   * @param benefitSlug - Benefit slug to decrement
   * @param value - Value to decrement (default: 1)
   * @returns Object with success status, remaining quota, and decremented amount
   */
  public static async decrementBenefitQuota(
    organizationId: number,
    benefitSlug: string,
    value: number = 1
  ) {
    try {
      console.log(
        `Decrementing benefit quota for organization: ${organizationId}, benefit: ${benefitSlug}, value: ${value}`
      );
      const s9InnerviewDataSource =
        await dbConnection.getS9InnerviewDataSource();
      const benefitsRepo = s9InnerviewDataSource.getRepository(
        OrganizationSubscriptionBenefitModel
      );

      // Get the organization's subscription benefits
      const benefits = await benefitsRepo.findOne({
        where: { organizationId },
      });

      if (!benefits) {
        return {
          success: false,
          message:
            SUBSCRIPTION_MSG.no_subscription_plan_found_and_no_free_plan_available,
          remainingQuota: 0,
        };
      }

      let quotaValue = 0;
      let propertyName = "";

      // Determine which property to decrement based on the benefit slug
      switch (benefitSlug) {
        case BENEFIT_SLUGS.JOB_POSTINGS:
          quotaValue = benefits.jobPostings;
          propertyName = "jobPostings";
          break;
        case BENEFIT_SLUGS.RESUME_SCREENING:
          quotaValue = benefits.resumeScreening;
          propertyName = "resumeScreening";
          break;
        case BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD:
          quotaValue = benefits.manualResumeUpload;
          propertyName = "manualResumeUpload";
          break;
        default:
          return {
            success: false,
            message: SUBSCRIPTION_MSG.invalid_benefit_type,
            remainingQuota: 0,
          };
      }

      console.log(
        `Decrementing quota for benefit: ${benefitSlug}, current value: ${quotaValue}, requested value: ${value}`
      );

      // Check if quota is available before decrementing
      // Case 1: Unlimited quota (-1) - always allow
      if (quotaValue === -1) {
        return {
          success: true,
          message: SUBSCRIPTION_MSG.benefit_quota_available,
          remainingQuota: -1, // Unlimited
          decrementedAmount: 0, // No actual decrement for unlimited
          isUnlimited: true,
        };
      }

      // Case 2: No quota available (0)
      if (quotaValue === 0) {
        return {
          success: false,
          message: this.getBenefitErrorMessage(benefitSlug, quotaValue),
          remainingQuota: 0,
        };
      }

      // Case 2: Requested value is more than available quota (but quota > 0)
      if (quotaValue > 0 && quotaValue < value) {
        return {
          success: false,
          message: this.getBenefitErrorMessage(benefitSlug, quotaValue),
          remainingQuota: quotaValue,
          availableQuota: quotaValue,
          requestedValue: value,
        };
      }

      // If quota is not unlimited (-1), decrement it - but don't decrement for manual_resume_upload
      if (
        quotaValue > 0 &&
        benefitSlug !== BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD
      ) {
        // Use TypeORM decrement function to directly decrement the value
        await benefitsRepo.decrement({ organizationId }, propertyName, value);

        console.log(
          `Decremented ${propertyName} for organization ${organizationId} by ${value}, previous value: ${quotaValue}`
        );
      }

      const newQuotaValue =
        benefitSlug !== BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD && quotaValue !== -1
          ? quotaValue - value
          : quotaValue;

      return {
        success: true,
        message: SUBSCRIPTION_MSG.benefit_quota_available,
        remainingQuota: newQuotaValue,
        decrementedAmount:
          benefitSlug !== BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD ? value : 0,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.error("Error decrementing benefit quota:", error);
      return {
        success: false,
        message: SUBSCRIPTION_MSG.subscription_benefit_check_error,
        remainingQuota: 0,
        error,
      };
    }
  }

  /**
   * Buy subscription - combines validation, customer creation, and checkout session creation
   * This is a streamlined API that handles the complete subscription purchase flow
   *
   * @param userId - User ID for the subscription
   * @param orgId - Organization ID for the subscription
   * @param planId - Plan ID to subscribe to
   * @param pricingId - Pricing ID (monthly/yearly)
   * @param successUrl - URL to redirect after successful checkout
   * @param cancelUrl - URL to redirect if checkout is cancelled
   * @returns Promise with checkout session details or error
   */
  static async buySubscription(
    userId: number,
    orgId: number,
    planId: number,
    pricingId: number,
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      console.log(
        `🛒 Starting buySubscription for user ${userId}, org ${orgId}, plan ${planId}, pricing ${pricingId}`
      );

      // Step 1: Validate subscription details
      console.log("🔍 Step 1: Validating subscription details...");
      const validationResult = await this.validateSubscriptionDetails(
        orgId,
        planId.toString(),
        pricingId.toString()
      );

      if (!validationResult.success) {
        console.error("❌ Validation failed:", validationResult.message);
        return {
          success: false,
          message: validationResult.message,
          data: null,
        };
      }

      console.log("✅ Validation passed successfully");
      const validatedData = validationResult.data;

      // Step 2: Create or ensure Stripe customer exists
      console.log("👤 Step 2: Creating/retrieving Stripe customer...");
      const customerResult = await this.createStripeCustomer(orgId);

      if (!customerResult.success) {
        console.error(
          "❌ Stripe customer creation failed:",
          customerResult.message
        );
        return {
          success: false,
          message: customerResult.message,
          data: null,
        };
      }

      console.log(
        `✅ Stripe customer ready: ${customerResult.data.customerId}`
      );
      const stripeCustomerId = customerResult.data.customerId;

      // Step 3: Create checkout session
      console.log("🛍️ Step 3: Creating checkout session...");
      const checkoutResult = await this.createCheckoutSession(
        userId,
        orgId,
        planId.toString(),
        pricingId.toString(),
        successUrl,
        cancelUrl
      );

      if (!checkoutResult.success) {
        console.error(
          "❌ Checkout session creation failed:",
          checkoutResult.message
        );
        return {
          success: false,
          message: checkoutResult.message,
          data: null,
        };
      }

      console.log("✅ Checkout session created successfully");

      // Return success response with checkout session details
      return {
        success: true,
        message:
          "Subscription purchase initiated successfully. Please complete the checkout process.",
        data: {
          sessionId: checkoutResult.data.sessionId,
          checkoutUrl: checkoutResult.data.url,
          planName: validatedData.planName,
          price: validatedData.price,
          stripeCustomerId,
        },
      };
    } catch (error) {
      console.error("❌ Error in buySubscription:", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: "Failed to initiate subscription purchase. Please try again.",
        data: null,
        error: error.message,
      };
    }
  }
}

export default SubscriptionServices;
