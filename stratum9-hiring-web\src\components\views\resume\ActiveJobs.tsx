/* eslint-disable react-hooks/exhaustive-deps */
"use client";

// Internal libraries
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";

// External libraries
import { useRouter } from "next/navigation";
import InfiniteScroll from "react-infinite-scroll-component";
import { debounce } from "lodash";

// Components
import ApplicationsSourcesModal from "@/components/commonModals/ApplicationsSourcesModal";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";

// Services
import { fetchJobsMeta, Job } from "@/services/jobRequirements/jobServices";
import { updateJobStatus } from "@/services/jobRequirements/updateJobServices";

// Constants
import { DEFAULT_LIMIT, PERMISSION } from "@/constants/commonConstants";

// CSS
import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";
import "react-loading-skeleton/dist/skeleton.css";
import TableSkeleton from "../skeletons/TableSkeleton";
// import Loader from "@/components/loader/Loader";
import ROUTES from "@/constants/routes";

const ActiveJobs = () => {
  const router = useRouter();
  const { control } = useForm({ mode: "onChange" });
  const [showApplicationsSourcesModal, setShowApplicationsSourcesModal] = useState(false);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [searchStr, setSearchStr] = useState("");
  const t = useTranslations();
  const dropdownRefs = React.useRef<{ [key: string]: HTMLUListElement | null }>({});

  console.log("offset==========", offset);

  // Get user permissions from Redux store
  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];

  // Check if user has permissions for various actions
  const hasManualResumeScreeningPermission = userPermissions.includes(PERMISSION.MANUAL_RESUME_SCREENING);
  const hasArchiveRestoreJobPermission = userPermissions.includes(PERMISSION.ARCHIVE_RESTORE_JOB_POSTS);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        openDropdownId &&
        dropdownRefs.current[openDropdownId] &&
        !dropdownRefs.current[openDropdownId]?.contains(event.target as Node) &&
        !(event.target as Element).closest(".applications-sources-modal")
      ) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside); // changed from "mousedown" to "click"
    return () => {
      document.removeEventListener("click", handleClickOutside); // changed from "mousedown" to "click"
    };
  }, [openDropdownId]);

  useEffect(() => {
    setJobs([]);
    setOffset(0);
    setHasMore(true);
    fetchMoreJobs("", 0, true);
  }, []);

  const fetchMoreJobs = async (searchValue: string, currentOffset = 0, reset = false) => {
    try {
      setLoading(true);
      // If it's a search or reset, clear the jobs first to show the skeleton
      if (reset) {
        setJobs([]);
      }
      const result = await fetchJobsMeta({
        page: currentOffset,
        limit: DEFAULT_LIMIT,
        searchStr: searchValue,
        isActive: true,
        applicationCount: 0, // Assuming you want to filter by application count
      });

      if (result?.data?.success && Array.isArray(result.data.data)) {
        const jobsFetched = result.data.data;

        setJobs((prevJobs) => (reset ? jobsFetched : [...prevJobs, ...jobsFetched]));

        if (jobsFetched.length < DEFAULT_LIMIT) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }

        setOffset(currentOffset + jobsFetched.length);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error(t("error_fetching_jobs"), error);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  const handleArchiveJob = async (jobId: number) => {
    setOpenDropdownId(null);
    try {
      await updateJobStatus(jobId, false);
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
    } catch {
      console.error(t("archive_job_failed"));
    }
  };

  const handleSearchInputChange = (event: string) => {
    const searchString = event.trim();
    setSearchStr(searchString);
    // Set loading to true here to show skeleton immediately during search
    setLoading(true);
    fetchMoreJobs(searchString, 0, true);
  };

  /**
   * Navigates to JobEditor with the selected job data for editing
   * @param {Job} job - The job to be edited
   */
  const handleEditJob = (job: Job) => {
    // Close the dropdown
    setOpenDropdownId(null);

    // Navigate to JobEditor with job data for editing
    router.push(`${ROUTES.JOBS.JOB_EDITOR}?jobId=${job.id}`);
  };

  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);

  return (
    <>
      <section className={`${style.resume_page} ${style.candidates_list_page}`}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  <BackArrowIcon onClick={() => router.back()} />
                  {t("application_for")} <span> {t("active_jobs")} </span>
                </h2>
                <div className="right-action">
                  <InputWrapper className="mb-0 w-100 search-input">
                    <div className="icon-align right">
                      <Textbox
                        className="form-control w-100"
                        control={control}
                        name="search"
                        type="text"
                        onChange={(e) => debouncedHandleSearchInputChange(e.target.value)}
                        placeholder={t("search_using_jobId_jobTitle")}
                      >
                        <InputWrapper.Icon>
                          <SearchIcon />
                        </InputWrapper.Icon>
                      </Textbox>
                    </div>
                  </InputWrapper>
                  <Button className="primary-btn rounded-md button-sm" onClick={() => router.push(ROUTES.JOBS.HIRING_TYPE)}>
                    {t("add_new_job")}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className={style.candidates_list_section}>
            <div className="table-responsive">
              <InfiniteScroll
                dataLength={jobs.length}
                next={() => fetchMoreJobs(searchStr, offset)}
                hasMore={hasMore}
                height={window.innerHeight - 250}
                loader={
                  loading && (
                    <table className="table w-100">
                      <TableSkeleton rows={3} cols={5} colWidths="120,80,100,24,24" />
                    </table>
                  )
                }
                endMessage={
                  !loading && jobs.length ? (
                    <table className="table w-100">
                      <tbody>
                        <tr>
                          <td colSpan={5} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                            {t("no_more_jobs_to_fetch")}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  ) : null
                }
              >
                <table className="table w-100 overflow-auto mb-0">
                  <thead>
                    <tr>
                      <th>{t("job_id")}</th>
                      <th>{t("job_title")}</th>
                      <th>{t("posted_on")}</th>
                      <th className="text-center">#{t("application_submitted")}</th>
                      <th className="text-center">{t("actions")}</th>
                    </tr>
                  </thead>
                  {jobs.length > 0 ? (
                    <tbody>
                      {jobs.map((job) => {
                        // Check if this is an empty job with null values
                        const isEmptyJob =
                          job.id === null &&
                          job.jobId === null &&
                          job.title === null &&
                          job.postedDate === null &&
                          job.isActive === null &&
                          job.finalJobDescriptionHtml === null;

                        if (isEmptyJob) {
                          return (
                            <tr key="empty-job">
                              <td colSpan={5} style={{ textAlign: "center" }}>
                                {t("no_active_job_found")}
                              </td>
                            </tr>
                          );
                        }

                        const date = job.postedDate ? new Date(job.postedDate) : null;
                        const formattedDate =
                          date && !isNaN(date.getTime())
                            ? date.toLocaleDateString("en-US", {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                              })
                            : "—";

                        return (
                          <tr key={job.id}>
                            <td>{job?.jobId}</td>
                            <td>{job?.title}</td>
                            <td>{formattedDate}</td>
                            <td align="center">{job?.applicationCount}</td>
                            <td className="text-center">
                              <div className="position-relative  ">
                                <Button
                                  className="clear-btn p-0 m-auto"
                                  onClick={() => setOpenDropdownId((prevId) => (prevId === String(job.id) ? null : String(job.id)))}
                                >
                                  <ThreeDotsIcon />
                                </Button>
                                {openDropdownId === String(job.id) && (
                                  <ul
                                    className="custom-dropdown"
                                    ref={(element) => {
                                      if (element) {
                                        dropdownRefs.current[String(job.id)] = element;
                                      }
                                    }}
                                  >
                                    {job && job.applicationCount && job.applicationCount > 0 && (
                                      <li
                                        onClick={() => {
                                          console.log("job", job);
                                          router.push(
                                            `${ROUTES.SCREEN_RESUME.CANDIDATE_LIST}/${job.id}?title=${encodeURIComponent(job.title)}&jobUniqueId=${encodeURIComponent(job.jobId)}`
                                          );
                                        }}
                                      >
                                        {t("view_all_candidates")}
                                      </li>
                                    )}
                                    {hasManualResumeScreeningPermission && (
                                      <li
                                        onClick={() =>
                                          router.push(
                                            `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${job.id}?title=${encodeURIComponent(job.title)}&jobUniqueId=${encodeURIComponent(job.jobId)}`
                                          )
                                        }
                                      >
                                        {t("screen_resume_manually")}
                                      </li>
                                    )}
                                    <li onClick={() => setShowApplicationsSourcesModal(true)}>View Application Sources</li>
                                    {job && job.applicationCount && job.applicationCount > 0 && (
                                      <li
                                        onClick={() => {
                                          router.push(
                                            `${ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION}/${job.id}` +
                                              `?title=${job.title}&jobUniqueId=${job.jobId}`
                                          );
                                        }}
                                      >
                                        {t("view_panding_action")}
                                      </li>
                                    )}
                                    {hasArchiveRestoreJobPermission && (
                                      <li onClick={() => handleArchiveJob(job.id)} style={{ cursor: "pointer" }}>
                                        {t("archive_job")}
                                      </li>
                                    )}
                                    <li onClick={() => handleEditJob(job)} style={{ cursor: "pointer" }}>
                                      {t("edit_job")}
                                    </li>
                                  </ul>
                                )}
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  ) : (
                    !loading && (
                      <tbody>
                        <tr>
                          <td colSpan={5} style={{ textAlign: "center" }}>
                            {t("no_active_job_found")}
                          </td>
                        </tr>
                      </tbody>
                    )
                  )}
                </table>
              </InfiniteScroll>
            </div>
          </div>
        </div>
      </section>

      {showApplicationsSourcesModal && <ApplicationsSourcesModal onCancel={() => setShowApplicationsSourcesModal(false)} />}
    </>
  );
};

export default ActiveJobs;
