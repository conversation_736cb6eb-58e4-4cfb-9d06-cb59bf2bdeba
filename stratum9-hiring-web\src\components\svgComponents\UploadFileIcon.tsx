import React from "react";

type UploadFileIconProps = {
  className?: string;
  onClick?: () => void;
};

function UploadFileIcon({ className, onClick }: UploadFileIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none" className={className} onClick={onClick}>
      <path
        d="M27.9128 7.7745L20.413 0.274687C20.2379 0.0995625 19.999 0 19.75 0H6.625C5.07419 0 3.8125 1.26169 3.8125 2.8125V29.1875C3.8125 30.7383 5.07419 32 6.625 32H25.375C26.9258 32 28.1875 30.7383 28.1875 29.1875V8.4375C28.1875 8.18188 28.0802 7.94181 27.9128 7.7745ZM20.6875 3.20081L24.9867 7.5H21.625C21.1081 7.5 20.6875 7.07944 20.6875 6.5625V3.20081ZM25.375 30.125H6.625C6.10806 30.125 5.6875 29.7044 5.6875 29.1875V2.8125C5.6875 2.29556 6.10806 1.875 6.625 1.875H18.8125V6.5625C18.8125 8.11331 20.0742 9.375 21.625 9.375H26.3125V29.1875C26.3125 29.7044 25.8919 30.125 25.375 30.125Z"
        fill="#333333"
      />
      <path
        d="M21.625 13.25H10.375C9.85725 13.25 9.4375 13.6697 9.4375 14.1875C9.4375 14.7053 9.85725 15.125 10.375 15.125H21.625C22.1427 15.125 22.5625 14.7053 22.5625 14.1875C22.5625 13.6697 22.1427 13.25 21.625 13.25Z"
        fill="#333333"
      />
      <path
        d="M21.625 17H10.375C9.85725 17 9.4375 17.4197 9.4375 17.9375C9.4375 18.4553 9.85725 18.875 10.375 18.875H21.625C22.1427 18.875 22.5625 18.4553 22.5625 17.9375C22.5625 17.4197 22.1427 17 21.625 17Z"
        fill="#333333"
      />
      <path
        d="M21.625 20.75H10.375C9.85725 20.75 9.4375 21.1697 9.4375 21.6875C9.4375 22.2053 9.85725 22.625 10.375 22.625H21.625C22.1427 22.625 22.5625 22.2053 22.5625 21.6875C22.5625 21.1697 22.1427 20.75 21.625 20.75Z"
        fill="#333333"
      />
      <path
        d="M17.875 24.5H10.375C9.85725 24.5 9.4375 24.9197 9.4375 25.4375C9.4375 25.9553 9.85725 26.375 10.375 26.375H17.875C18.3927 26.375 18.8125 25.9553 18.8125 25.4375C18.8125 24.9197 18.3927 24.5 17.875 24.5Z"
        fill="#333333"
      />
    </svg>
  );
}

export default UploadFileIcon;
