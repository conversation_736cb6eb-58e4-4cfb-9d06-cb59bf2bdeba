{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobSkillsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\nimport { ISkillData, JobSkillsState } from \"@/interfaces/jobRequirementesInterfaces\";\r\n\r\n// Define the initial state using that type\r\nconst initialState: JobSkillsState = {\r\n  careerSkills: [],\r\n  roleSpecificSkills: [],\r\n  cultureSpecificSkills: [],\r\n};\r\n\r\nexport const jobSkillsSlice = createSlice({\r\n  name: \"jobSkills\",\r\n  initialState,\r\n  reducers: {\r\n    setSkillsData: (\r\n      state,\r\n      action: PayloadAction<{\r\n        careerSkills?: ISkillData[];\r\n        roleSpecificSkills?: ISkillData[];\r\n        cultureSpecificSkills?: ISkillData[];\r\n      }>\r\n    ) => {\r\n      if (action.payload.careerSkills) {\r\n        state.careerSkills = action.payload.careerSkills;\r\n      }\r\n      if (action.payload.roleSpecificSkills) {\r\n        state.roleSpecificSkills = action.payload.roleSpecificSkills;\r\n      }\r\n      if (action.payload.cultureSpecificSkills) {\r\n        state.cultureSpecificSkills = action.payload.cultureSpecificSkills;\r\n      }\r\n    },\r\n    clearSkillsData: (state) => {\r\n      state.careerSkills = [];\r\n      state.roleSpecificSkills = [];\r\n      state.cultureSpecificSkills = [];\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setSkillsData, clearSkillsData } = jobSkillsSlice.actions;\r\n\r\n// Simple selectors to use directly with useSelector\r\nexport const selectJobSkillsState = (state: { jobSkills: JobSkillsState }) => state.jobSkills;\r\nexport const selectCareerSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.careerSkills;\r\nexport const selectRoleSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.roleSpecificSkills;\r\nexport const selectCultureSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.cultureSpecificSkills;\r\n\r\n// Export the reducer directly for easier import in the store\r\nexport default jobSkillsSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,2CAA2C;AAC3C,MAAM,eAA+B;IACnC,cAAc,EAAE;IAChB,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;AAC3B;AAEO,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,eAAe,CACb,OACA;YAMA,IAAI,OAAO,OAAO,CAAC,YAAY,EAAE;gBAC/B,MAAM,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY;YAClD;YACA,IAAI,OAAO,OAAO,CAAC,kBAAkB,EAAE;gBACrC,MAAM,kBAAkB,GAAG,OAAO,OAAO,CAAC,kBAAkB;YAC9D;YACA,IAAI,OAAO,OAAO,CAAC,qBAAqB,EAAE;gBACxC,MAAM,qBAAqB,GAAG,OAAO,OAAO,CAAC,qBAAqB;YACpE;QACF;QACA,iBAAiB,CAAC;YAChB,MAAM,YAAY,GAAG,EAAE;YACvB,MAAM,kBAAkB,GAAG,EAAE;YAC7B,MAAM,qBAAqB,GAAG,EAAE;QAClC;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,eAAe,OAAO;AAGjE,MAAM,uBAAuB,CAAC,QAAyC,MAAM,SAAS;AACtF,MAAM,qBAAqB,CAAC,QAAyC,MAAM,SAAS,CAAC,YAAY;AACjG,MAAM,2BAA2B,CAAC,QAAyC,MAAM,SAAS,CAAC,kBAAkB;AAC7G,MAAM,8BAA8B,CAAC,QAAyC,MAAM,SAAS,CAAC,qBAAqB;uCAG3G,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/commonConstants.ts"], "sourcesContent": ["import { ExtendedFormValues } from \"@/types/types\";\r\n\r\nexport const ACCESS_TOKEN_KEY = \"__ATK__\";\r\n\r\nexport const EMAIL_REGEX = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\r\n\r\nexport const PASSWORD_REGEX = /^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\\s).{8,16}$/;\r\nexport const NAME_REGEX = /^[a-zA-Z0-9\\s.'-]+$/;\r\n\r\nexport const MAX_IMAGE_SIZE = 5242880;\r\n\r\nexport const ScheduleInterviewFormSubmissionType = {\r\n  SCHEDULE: \"schedule\",\r\n  UPDATE: \"update\",\r\n};\r\n\r\nexport const S3_PATHS = {\r\n  PROFILE_IMAGE: \"profile-images/:path\",\r\n};\r\n\r\nexport const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [\r\n  \"Arrive at the interview location on time with a government-issued ID.\",\r\n  \"Ensure your phone is on silent mode and distractions are minimized.\",\r\n  \"Bring a printed copy of your resume and any supporting documents.\",\r\n  \"Dress professionally and maintain proper body language.\",\r\n  \"Listen carefully, answer honestly, and ask for clarification if needed.\",\r\n  \"Respect the interview flow and do not interrupt the interviewer.\",\r\n  \"Take brief notes if necessary, but focus on active conversation.\",\r\n  \"If you need assistance or face any issues, notify the interview coordinator.\",\r\n];\r\n\r\nexport const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [\r\n  \"Join the interview on time using the link provided.\",\r\n  \"Ensure a stable internet connection and a quiet, well-lit space.\",\r\n  \"Test your camera, microphone, and audio settings in advance.\",\r\n  \"Keep your video on unless instructed otherwise by the interviewer.\",\r\n  \"Minimize background noise and avoid multitasking during the session.\",\r\n  \"Use headphones if possible for better audio clarity.\",\r\n  \"Be attentive, respond clearly, and maintain professional posture.\",\r\n  \"Contact support if you face technical difficulties before or during the interview.\",\r\n];\r\n\r\n/**\r\n * Permission Constants\r\n */\r\nexport const PERMISSION = {\r\n  CREATE_OR_EDIT_JOB_POST: \"create-or-edit-job-post\",\r\n  SCHEDULE_CONDUCT_INTERVIEWS: \"schedule-conduct-interviews\",\r\n  VIEW_HIRED_CANDIDATES: \"view-hired-candidates\",\r\n  ARCHIVE_RESTORE_CANDIDATES: \"archive-restore-candidates\",\r\n  ARCHIVE_RESTORE_JOB_POSTS: \"archive-restore-job-posts\",\r\n  MANUAL_RESUME_SCREENING: \"manual-resume-screening\",\r\n  EDIT_SCHEDULED_INTERVIEWS: \"edit-scheduled-interviews\",\r\n  ADD_ADDITIONAL_CANDIDATE_INFO: \"add-additional-candidate-info\",\r\n  ADD_OR_EDIT_INTERVIEW_NOTES: \"add-or-edit-interview-notes\",\r\n  MANAGE_TOP_CANDIDATES: \"manage-top-candidates\",\r\n  MANAGE_PRE_INTERVIEW_QUESTIONS: \"manage-pre-interview-questions\",\r\n  CREATE_FINAL_ASSESSMENT: \"create-final-assessment\",\r\n  VIEW_FINAL_ASSESSMENT: \"view-final-assessment\",\r\n  VIEW_CANDIDATE_PROFILE_SUMMARY: \"view-candidate-profile-summary\",\r\n  HIRE_CANDIDATE: \"hire-candidate\",\r\n  CREATE_NEW_ROLE: \"create-new-role\",\r\n  MANAGE_USER_PERMISSIONS: \"manage-user-permissions\",\r\n  CREATE_NEW_DEPARTMENT: \"create-new-department\",\r\n  ADD_INTERVIEW_PARTICIPANTS: \"add-interview-participants\",\r\n  VIEW_SUBSCRIPTION_PLAN: \"view-subscription-plan\",\r\n  MANAGE_SUBSCRIPTIONS: \"manage-subscriptions\",\r\n  VIEW_AUDIT_LOGS_UPCOMING: \"view-audit-logs-upcoming\",\r\n  VIEW_ALL_SCHEDULED_INTERVIEWS: \"view-all-scheduled-interviews\",\r\n};\r\n\r\n/**\r\n * Skill Constants\r\n */\r\nexport const SKILL_CONSTANTS = {\r\n  REQUIRED_ROLE_SKILLS: 10,\r\n  REQUIRED_CULTURE_SKILLS: 5,\r\n};\r\nexport const commonConstants = {\r\n  finalAssessmentId: \"finalAssessmentId\",\r\n  token: \"token\",\r\n  isShared: \"isShared\",\r\n  isSubmitted: \"isSubmitted\",\r\n  jobId: \"jobId\",\r\n  jobApplicationId: \"jobApplicationId\",\r\n};\r\n\r\nexport const QuestionType = {\r\n  MCQ: \"mcq\",\r\n  TRUE_FALSE: \"true_false\",\r\n};\r\n\r\n// Constants for option IDs\r\nexport const OPTION_ID = {\r\n  A: \"A\",\r\n  B: \"B\",\r\n  C: \"C\",\r\n  D: \"D\",\r\n  TRUE: \"true\",\r\n  FALSE: \"false\",\r\n} as const;\r\n\r\n// Constants for question types\r\nexport const QUESTION_TYPE = {\r\n  MCQ: \"mcq\" as const,\r\n  TRUE_FALSE: \"true_false\" as const,\r\n};\r\n\r\n// Constants for default options\r\nexport const DEFAULT_MCQ_OPTIONS = [\r\n  { id: OPTION_ID.A, text: \"\" },\r\n  { id: OPTION_ID.B, text: \"\" },\r\n  { id: OPTION_ID.C, text: \"\" },\r\n  { id: OPTION_ID.D, text: \"\" },\r\n];\r\n\r\nexport const DEFAULT_TRUE_FALSE_OPTIONS = [\r\n  { id: OPTION_ID.TRUE, text: \"True\" },\r\n  { id: OPTION_ID.FALSE, text: \"False\" },\r\n];\r\n\r\nexport const INTERVIEW_SCHEDULE_ROUND_TYPE = [\r\n  {\r\n    label: \"One-On-One\",\r\n    value: \"One-On-One\",\r\n  },\r\n  {\r\n    label: \"Video Call\",\r\n    value: \"Video Call\",\r\n  },\r\n];\r\n\r\n/**\r\n * Interview Question Types\r\n */\r\nexport const QUESTION_TYPES = {\r\n  ROLE_SPECIFIC: \"role_specific\",\r\n  CULTURE_SPECIFIC: \"culture_specific\",\r\n  CAREER_BASED: \"career_based\",\r\n} as const;\r\n\r\nexport type QuestionType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES];\r\n/**\r\n * Empty Content Patterns\r\n */\r\nexport const EMPTY_CONTENT_PATTERNS = [\"<p><br></p>\", \"<p></p>\", \"<div><br></div>\", \"<div></div>\", \"<p>&nbsp;</p>\"];\r\n\r\n// Define the initial state using FormValues type\r\nexport const initialState: ExtendedFormValues = {\r\n  title: \"\",\r\n  employment_type: \"\",\r\n  department_id: \"\",\r\n  salary_range: \"\",\r\n  salary_cycle: \"\",\r\n  location_type: \"\",\r\n  state: \"\",\r\n  city: \"\",\r\n  role_overview: \"\",\r\n  experience_level: \"\",\r\n  responsibilities: \"\",\r\n  educations_requirement: \"\",\r\n  certifications: undefined,\r\n  skills_and_software_expertise: \"\",\r\n  experience_required: \"\",\r\n  ideal_candidate_traits: \"\",\r\n  about_company: \"\",\r\n  perks_benefits: undefined,\r\n  tone_style: \"\",\r\n  additional_info: undefined,\r\n  compliance_statement: [],\r\n  show_compliance: false,\r\n  hiring_type: \"\",\r\n};\r\n\r\n// Define the skill item interface\r\nexport interface ISkillItem {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  short_description: string;\r\n}\r\n\r\n// Define a skill category interface\r\nexport interface ISkillCategory {\r\n  type: string;\r\n  items: ISkillItem[];\r\n}\r\n\r\n// Define the slice state type\r\nexport interface AllSkillsState {\r\n  categories: ISkillCategory[];\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nexport const FILE_EXTENSION = [\r\n  \"pdf\",\r\n  \"plain\",\r\n  \"csv\",\r\n  \"vnd.ms-excel.sheet.macroEnabled.12\",\r\n  \"vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n  \"vnd.openxmlformats-officedocument.wordprocessingml.document\",\r\n  \"vnd.openxmlformats-officedocument.presentationml.presentation\",\r\n];\r\n\r\nexport const ACTIVE = \"active\";\r\nexport const TOKEN_EXPIRED = \"Session Expired! Please log in again.\";\r\nexport const DEFAULT_LIMIT = 15;\r\nexport const STANDARD_LIMIT = 18;\r\nexport const DEFAULT_OFFSET = 0;\r\n\r\nexport enum MessageType {\r\n  success = \"success\",\r\n  error = \"error\",\r\n}\r\n\r\nexport const IMAGE_EXTENSIONS = [\"png\", \"jpg\", \"jpeg\", \"gif\", \"webp\"];\r\n\r\nexport const ASSESSMENT_INSTRUCTIONS = {\r\n  instructions: [\r\n    \"Do not refresh or close the browser\",\r\n    \"Check your internet connection\",\r\n    \"Ensure a distraction-free environment\",\r\n    \"Click 'Submit' only once when finished\",\r\n    \"Read each question carefully\",\r\n    \"Manage your time efficiently\",\r\n    \"Avoid any form of plagiarism\",\r\n    \"Reach out to support if needed\",\r\n  ],\r\n};\r\nexport const PERMISSIONS_COOKIES_KEY = \"permissions_data\";\r\n\r\nexport const PDF_FILE_NAME = \"pdf\";\r\nexport const PDF_FILE_TYPE = \"application/pdf\";\r\nexport const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;\r\nexport const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAM,mBAAmB;AAEzB,MAAM,cAAc;AAEpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AAEnB,MAAM,iBAAiB;AAEvB,MAAM,sCAAsC;IACjD,UAAU;IACV,QAAQ;AACV;AAEO,MAAM,WAAW;IACtB,eAAe;AACjB;AAEO,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAKM,MAAM,aAAa;IACxB,yBAAyB;IACzB,6BAA6B;IAC7B,uBAAuB;IACvB,4BAA4B;IAC5B,2BAA2B;IAC3B,yBAAyB;IACzB,2BAA2B;IAC3B,+BAA+B;IAC/B,6BAA6B;IAC7B,uBAAuB;IACvB,gCAAgC;IAChC,yBAAyB;IACzB,uBAAuB;IACvB,gCAAgC;IAChC,gBAAgB;IAChB,iBAAiB;IACjB,yBAAyB;IACzB,uBAAuB;IACvB,4BAA4B;IAC5B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,+BAA+B;AACjC;AAKO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,yBAAyB;AAC3B;AACO,MAAM,kBAAkB;IAC7B,mBAAmB;IACnB,OAAO;IACP,UAAU;IACV,aAAa;IACb,OAAO;IACP,kBAAkB;AACpB;AAEO,MAAM,eAAe;IAC1B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,YAAY;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM;IACN,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,sBAAsB;IACjC;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;CAC7B;AAEM,MAAM,6BAA6B;IACxC;QAAE,IAAI,UAAU,IAAI;QAAE,MAAM;IAAO;IACnC;QAAE,IAAI,UAAU,KAAK;QAAE,MAAM;IAAQ;CACtC;AAEM,MAAM,gCAAgC;IAC3C;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAKM,MAAM,iBAAiB;IAC5B,eAAe;IACf,kBAAkB;IAClB,cAAc;AAChB;AAMO,MAAM,yBAAyB;IAAC;IAAe;IAAW;IAAmB;IAAe;CAAgB;AAG5G,MAAM,eAAmC;IAC9C,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,cAAc;IACd,eAAe;IACf,OAAO;IACP,MAAM;IACN,eAAe;IACf,kBAAkB;IAClB,kBAAkB;IAClB,wBAAwB;IACxB,gBAAgB;IAChB,+BAA+B;IAC/B,qBAAqB;IACrB,wBAAwB;IACxB,eAAe;IACf,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,sBAAsB,EAAE;IACxB,iBAAiB;IACjB,aAAa;AACf;AAuBO,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AAEvB,IAAA,AAAK,qCAAA;;;WAAA;;AAKL,MAAM,mBAAmB;IAAC;IAAO;IAAO;IAAQ;IAAO;CAAO;AAE9D,MAAM,0BAA0B;IACrC,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AACO,MAAM,0BAA0B;AAEhC,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,sBAAsB,IAAI,OAAO;AACvC,MAAM,kCAAkC", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobDetailsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\nimport { ExtendedFormValues } from \"@/types/types\";\r\nimport { initialState } from \"@/constants/commonConstants\";\r\n\r\nexport const jobDetailsSlice = createSlice({\r\n  name: \"jobDetails\",\r\n  initialState,\r\n  reducers: {\r\n    // Set all job details at once\r\n    setJobDetails: (state, action: PayloadAction<ExtendedFormValues>) => {\r\n      return { ...state, ...action.payload };\r\n    },\r\n    // Clear job details\r\n    clearJobDetails: () => {\r\n      return initialState;\r\n    },\r\n    // Update a specific field in job details\r\n    updateJobDetail: <T extends keyof ExtendedFormValues>(\r\n      state: ExtendedFormValues,\r\n      action: PayloadAction<{ field: T; value: ExtendedFormValues[T] }>\r\n    ) => {\r\n      const { field, value } = action.payload;\r\n      state[field] = value;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setJobDetails, clearJobDetails, updateJobDetail } = jobDetailsSlice.actions;\r\n\r\n// Simple selectors to use directly with useSelector\r\nexport const selectJobDetails = (state: { jobDetails: ExtendedFormValues }) => state.jobDetails;\r\n\r\n// Export the reducer directly for easier import in the store\r\nexport default jobDetailsSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAEA;;;AAEO,MAAM,kBAAkB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IACzC,MAAM;IACN,cAAA,sIAAA,CAAA,eAAY;IACZ,UAAU;QACR,8BAA8B;QAC9B,eAAe,CAAC,OAAO;YACrB,OAAO;gBAAE,GAAG,KAAK;gBAAE,GAAG,OAAO,OAAO;YAAC;QACvC;QACA,oBAAoB;QACpB,iBAAiB;YACf,OAAO,sIAAA,CAAA,eAAY;QACrB;QACA,yCAAyC;QACzC,iBAAiB,CACf,OACA;YAEA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;YACvC,KAAK,CAAC,MAAM,GAAG;QACjB;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,gBAAgB,OAAO;AAGnF,MAAM,mBAAmB,CAAC,QAA8C,MAAM,UAAU;uCAGhF,gBAAgB,OAAO", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/allSkillsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\nimport { RootState } from \"../store\";\r\nimport { AllSkillsState, ISkillCategory, ISkillItem } from \"@/interfaces/jobRequirementesInterfaces\";\r\n\r\n// Define the initial state\r\nconst initialState: AllSkillsState = {\r\n  categories: [],\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\n// Create the slice\r\nexport const allSkillsSlice = createSlice({\r\n  name: \"allSkills\",\r\n  initialState,\r\n  reducers: {\r\n    fetchSkillsStart: (state: AllSkillsState) => {\r\n      state.loading = true;\r\n      state.error = null;\r\n    },\r\n    fetchSkillsSuccess: (state: AllSkillsState, action: PayloadAction<ISkillCategory[]>) => {\r\n      state.categories = action.payload;\r\n      state.loading = false;\r\n      state.error = null;\r\n    },\r\n    fetchSkillsFailure: (state: AllSkillsState, action: PayloadAction<string>) => {\r\n      state.loading = false;\r\n      state.error = action.payload;\r\n    },\r\n    updateSkillItem: (\r\n      state: AllSkillsState,\r\n      action: PayloadAction<{\r\n        categoryType: string;\r\n        skillId: number;\r\n        updatedSkill: Partial<ISkillItem>;\r\n      }>\r\n    ) => {\r\n      const { categoryType, skillId, updatedSkill } = action.payload;\r\n      const categoryIndex = state.categories.findIndex((cat) => cat.type === categoryType);\r\n\r\n      if (categoryIndex !== -1) {\r\n        const skillIndex = state.categories[categoryIndex].items.findIndex((item) => item.id === skillId);\r\n\r\n        if (skillIndex !== -1) {\r\n          state.categories[categoryIndex].items[skillIndex] = {\r\n            ...state.categories[categoryIndex].items[skillIndex],\r\n            ...updatedSkill,\r\n          };\r\n        }\r\n      }\r\n    },\r\n  },\r\n});\r\n\r\n// Export actions\r\nexport const { fetchSkillsStart, fetchSkillsSuccess, fetchSkillsFailure, updateSkillItem } = allSkillsSlice.actions;\r\n\r\n// Define selectors\r\nexport const selectAllSkills = (state: RootState) => state.allSkills.categories;\r\nexport const selectSkillsLoading = (state: RootState) => state.allSkills.loading;\r\nexport const selectSkillsError = (state: RootState) => state.allSkills.error;\r\n\r\n// Export specific category selector\r\nexport const selectSkillsByCategory = (categoryType: string) => (state: RootState) =>\r\n  state.allSkills.categories.find((cat: ISkillCategory) => cat.type === categoryType)?.items || [];\r\n\r\n// Export reducer\r\nexport default allSkillsSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAIA,2BAA2B;AAC3B,MAAM,eAA+B;IACnC,YAAY,EAAE;IACd,SAAS;IACT,OAAO;AACT;AAGO,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,kBAAkB,CAAC;YACjB,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC,OAAuB;YAC1C,MAAM,UAAU,GAAG,OAAO,OAAO;YACjC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC,OAAuB;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QACA,iBAAiB,CACf,OACA;YAMA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,OAAO,OAAO;YAC9D,MAAM,gBAAgB,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;YAEvE,IAAI,kBAAkB,CAAC,GAAG;gBACxB,MAAM,aAAa,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAEzF,IAAI,eAAe,CAAC,GAAG;oBACrB,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW,GAAG;wBAClD,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW;wBACpD,GAAG,YAAY;oBACjB;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,eAAe,OAAO;AAG5G,MAAM,kBAAkB,CAAC,QAAqB,MAAM,SAAS,CAAC,UAAU;AACxE,MAAM,sBAAsB,CAAC,QAAqB,MAAM,SAAS,CAAC,OAAO;AACzE,MAAM,oBAAoB,CAAC,QAAqB,MAAM,SAAS,CAAC,KAAK;AAGrE,MAAM,yBAAyB,CAAC,eAAyB,CAAC,QAC/D,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAwB,IAAI,IAAI,KAAK,eAAe,SAAS,EAAE;uCAGnF,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/authSlice.ts"], "sourcesContent": ["import { Permission, Role, IUserData, IDepartment } from \"@/interfaces/authInterfaces\";\r\nimport { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\n\r\n// interface IPlanFeature {\r\n//   slug: string;\r\n//   text: string;\r\n//   value: number | string | boolean;\r\n//   feature: string;\r\n//   is_active: boolean;\r\n//   description: string;\r\n// }\r\n\r\nexport interface ICurrentPlan {\r\n  orgSubscriptionId?: number;\r\n  startDate?: string;\r\n  expiryDate?: string;\r\n  nextBillingDate?: string;\r\n  status?: string;\r\n  subscriptionPlanId?: number;\r\n  subscriptionPlanName?: string;\r\n  subscriptionPlanDescription?: string;\r\n  pricingId?: number;\r\n  price?: number;\r\n  subscriptionPlanPaymentType?: string;\r\n}\r\n\r\nexport interface AuthState {\r\n  authData: IUserData | null;\r\n  department: IDepartment | null;\r\n  role: Role | null;\r\n  permissions: Permission[];\r\n  currentPlan: ICurrentPlan | null;\r\n}\r\n\r\nconst initialState: AuthState = {\r\n  authData: {\r\n    id: -1,\r\n    account_type: \"\",\r\n    email: \"\",\r\n    isVerified: false,\r\n    sms_notification: false,\r\n    allow_notification: false,\r\n    is_deleted: false,\r\n    image: \"\",\r\n    orgId: -1,\r\n    departmentId: -1,\r\n    organizationName: \"\",\r\n    organizationCode: \"\",\r\n    createdTs: \"\",\r\n    first_name: \"\",\r\n    last_name: \"\",\r\n  },\r\n  department: null,\r\n  role: null,\r\n  permissions: [],\r\n  currentPlan: null,\r\n};\r\n\r\nexport const authSlice = createSlice({\r\n  name: \"auth\",\r\n  initialState,\r\n  reducers: {\r\n    setAuthData: (state, action) => {\r\n      state.authData = action.payload;\r\n    },\r\n    setRole: (state, action) => {\r\n      state.role = action.payload;\r\n    },\r\n    setDepartment: (state, action) => {\r\n      state.department = action.payload;\r\n    },\r\n    setPermissions: (state, action) => {\r\n      state.permissions = action.payload;\r\n    },\r\n    setCurrentPlan: (state, action) => {\r\n      state.currentPlan = action.payload;\r\n    },\r\n    updateUserProfileData: (\r\n      state,\r\n      action: PayloadAction<{\r\n        first_name?: string;\r\n        last_name?: string;\r\n        image?: string | null;\r\n      }>\r\n    ) => {\r\n      if (state.authData) {\r\n        const { first_name, last_name, image } = action.payload;\r\n\r\n        // Update firstName and lastName separately\r\n        if (first_name !== undefined) {\r\n          state.authData.first_name = first_name;\r\n        }\r\n\r\n        if (last_name !== undefined) {\r\n          state.authData.last_name = last_name;\r\n        }\r\n\r\n        // Update image if provided\r\n        if (image !== undefined) {\r\n          state.authData.image = image;\r\n        }\r\n      }\r\n    },\r\n  },\r\n});\r\n\r\nexport const selectRole = (state: { auth: AuthState }) => state.auth.role;\r\nexport const selectDepartment = (state: { auth: AuthState }) => state.auth.department;\r\nexport const selectPermissions = (state: { auth: AuthState }) => state.auth.permissions;\r\nexport const selectProfileData = (state: { auth: AuthState }) => state.auth.authData;\r\nexport const selectCurrentPlan = (state: { auth: AuthState }) => state.auth.currentPlan;\r\n\r\nexport const { setAuthData, setRole, setDepartment, setPermissions, updateUserProfileData, setCurrentPlan } = authSlice.actions;\r\n\r\nexport default authSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;;AAiCA,MAAM,eAA0B;IAC9B,UAAU;QACR,IAAI,CAAC;QACL,cAAc;QACd,OAAO;QACP,YAAY;QACZ,kBAAkB;QAClB,oBAAoB;QACpB,YAAY;QACZ,OAAO;QACP,OAAO,CAAC;QACR,cAAc,CAAC;QACf,kBAAkB;QAClB,kBAAkB;QAClB,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA,YAAY;IACZ,MAAM;IACN,aAAa,EAAE;IACf,aAAa;AACf;AAEO,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM;IACN;IACA,UAAU;QACR,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;QAC7B;QACA,eAAe,CAAC,OAAO;YACrB,MAAM,UAAU,GAAG,OAAO,OAAO;QACnC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,uBAAuB,CACrB,OACA;YAMA,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;gBAEvD,2CAA2C;gBAC3C,IAAI,eAAe,WAAW;oBAC5B,MAAM,QAAQ,CAAC,UAAU,GAAG;gBAC9B;gBAEA,IAAI,cAAc,WAAW;oBAC3B,MAAM,QAAQ,CAAC,SAAS,GAAG;gBAC7B;gBAEA,2BAA2B;gBAC3B,IAAI,UAAU,WAAW;oBACvB,MAAM,QAAQ,CAAC,KAAK,GAAG;gBACzB;YACF;QACF;IACF;AACF;AAEO,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI;AAClE,MAAM,mBAAmB,CAAC,QAA+B,MAAM,IAAI,CAAC,UAAU;AAC9E,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW;AAChF,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;AAC7E,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW;AAEhF,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB,EAAE,cAAc,EAAE,GAAG,UAAU,OAAO;uCAEhH,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobRequirementSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\nimport { JobRequirementState } from \"@/interfaces/jobRequirementesInterfaces\";\r\n\r\nconst initialState: JobRequirementState = {\r\n  content: \"\",\r\n  isGenerated: false,\r\n  generatedAt: null,\r\n};\r\n\r\nexport const jobRequirementSlice = createSlice({\r\n  name: \"jobRequirement\",\r\n  initialState,\r\n  reducers: {\r\n    // Set job requirement content\r\n    setJobRequirement: (state, action: PayloadAction<string>) => {\r\n      state.content = action.payload;\r\n      state.isGenerated = true;\r\n      state.generatedAt = new Date().toISOString();\r\n    },\r\n    // Clear job requirement data\r\n    clearJobRequirement: (state) => {\r\n      state.content = \"\";\r\n      state.isGenerated = false;\r\n      state.generatedAt = null;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setJobRequirement, clearJobRequirement } = jobRequirementSlice.actions;\r\n\r\n// Selector to use with useSelector\r\nexport const selectJobRequirement = (state: { jobRequirement: JobRequirementState }) => state.jobRequirement;\r\n\r\nexport default jobRequirementSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGA,MAAM,eAAoC;IACxC,SAAS;IACT,aAAa;IACb,aAAa;AACf;AAEO,MAAM,sBAAsB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC7C,MAAM;IACN;IACA,UAAU;QACR,8BAA8B;QAC9B,mBAAmB,CAAC,OAAO;YACzB,MAAM,OAAO,GAAG,OAAO,OAAO;YAC9B,MAAM,WAAW,GAAG;YACpB,MAAM,WAAW,GAAG,IAAI,OAAO,WAAW;QAC5C;QACA,6BAA6B;QAC7B,qBAAqB,CAAC;YACpB,MAAM,OAAO,GAAG;YAChB,MAAM,WAAW,GAAG;YACpB,MAAM,WAAW,GAAG;QACtB;IACF;AACF;AAEO,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,GAAG,oBAAoB,OAAO;AAG9E,MAAM,uBAAuB,CAAC,QAAmD,MAAM,cAAc;uCAE7F,oBAAoB,OAAO", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 561, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/interviewSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\nimport { QUESTION_TYPES, QuestionType } from \"@/constants/commonConstants\";\r\nimport { IGetInterviewSkillQuestionsResponse, IInterviewStaticInformation } from \"@/interfaces/interviewInterfaces\";\r\n\r\nexport interface IQuestionAnswer {\r\n  questionId: number;\r\n  answer: string;\r\n}\r\n\r\nexport interface IUpdateQuestionAnswerPayload {\r\n  questionType: QuestionType;\r\n  questionAnswers: IQuestionAnswer[];\r\n  stratumScore: number;\r\n  category?: string;\r\n  interviewerName?: string;\r\n}\r\n\r\nconst initialState: IGetInterviewSkillQuestionsResponse & { interviewStaticInformation: IInterviewStaticInformation } = {\r\n  roleSpecificQuestions: {},\r\n  cultureSpecificQuestions: {},\r\n  careerBasedQuestions: {\r\n    questions: [],\r\n    score: 0,\r\n  },\r\n  interviewStaticInformation: {\r\n    oneToOneInterviewInstructions: [],\r\n    videoCallInterviewInstructions: [],\r\n    startumDescription: [],\r\n  },\r\n};\r\n\r\nexport const interviewSlice = createSlice({\r\n  name: \"interview\",\r\n  initialState,\r\n  reducers: {\r\n    setInterviewQuestions: (state, action: PayloadAction<IGetInterviewSkillQuestionsResponse>) => {\r\n      // Handle role-specific questions\r\n      if (action.payload.roleSpecificQuestions !== undefined) {\r\n        state.roleSpecificQuestions = action.payload.roleSpecificQuestions;\r\n      }\r\n\r\n      // Handle culture-specific questions\r\n      if (action.payload.cultureSpecificQuestions !== undefined) {\r\n        state.cultureSpecificQuestions = action.payload.cultureSpecificQuestions;\r\n      }\r\n\r\n      // Handle career-based questions\r\n      if (action.payload.careerBasedQuestions !== undefined) {\r\n        state.careerBasedQuestions = action.payload.careerBasedQuestions;\r\n      }\r\n    },\r\n\r\n    setInterviewStaticInformation: (state, action: PayloadAction<IInterviewStaticInformation>) => {\r\n      state.interviewStaticInformation = action.payload;\r\n    },\r\n\r\n    updateQuestionAnswer: (state, action: PayloadAction<IUpdateQuestionAnswerPayload>) => {\r\n      const { questionType, category, questionAnswers, stratumScore, interviewerName } = action.payload;\r\n\r\n      // Create a Map for O(1) lookups\r\n      const answerMap = new Map(questionAnswers.map((qa) => [qa.questionId, qa.answer]));\r\n\r\n      switch (questionType) {\r\n        case QUESTION_TYPES.CAREER_BASED:\r\n          // Update answers\r\n          state.careerBasedQuestions.questions = state.careerBasedQuestions.questions.map((question) => {\r\n            const answer = answerMap.get(question.id);\r\n            if (answer !== undefined) {\r\n              return { ...question, answer };\r\n            }\r\n            return question;\r\n          });\r\n\r\n          // Update score\r\n          state.careerBasedQuestions.score = stratumScore;\r\n          break;\r\n\r\n        case QUESTION_TYPES.ROLE_SPECIFIC:\r\n          if (category) {\r\n            // Initialize category if it doesn't exist\r\n            if (!state.roleSpecificQuestions[category]) {\r\n              state.roleSpecificQuestions[category] = {\r\n                questions: [],\r\n                score: 0,\r\n              };\r\n            }\r\n\r\n            // Update answers\r\n            state.roleSpecificQuestions[category].questions = state.roleSpecificQuestions[category].questions.map((question) => {\r\n              const answer = answerMap.get(question.id);\r\n              if (answer !== undefined) {\r\n                return { ...question, answer };\r\n              }\r\n              return question;\r\n            });\r\n\r\n            // Update score and interviewer name\r\n            state.roleSpecificQuestions[category].score = stratumScore;\r\n            state.roleSpecificQuestions[category].interviewerName = interviewerName;\r\n          }\r\n          break;\r\n\r\n        case QUESTION_TYPES.CULTURE_SPECIFIC:\r\n          if (category) {\r\n            // Initialize category if it doesn't exist\r\n            if (!state.cultureSpecificQuestions[category]) {\r\n              state.cultureSpecificQuestions[category] = {\r\n                questions: [],\r\n                score: 0,\r\n              };\r\n            }\r\n\r\n            // Update answers\r\n            state.cultureSpecificQuestions[category].questions = state.cultureSpecificQuestions[category].questions.map((question) => {\r\n              const answer = answerMap.get(question.id);\r\n              if (answer !== undefined) {\r\n                return { ...question, answer };\r\n              }\r\n              return question;\r\n            });\r\n\r\n            // Update score and interviewer name\r\n            state.cultureSpecificQuestions[category].score = stratumScore;\r\n            state.cultureSpecificQuestions[category].interviewerName = interviewerName;\r\n          }\r\n          break;\r\n      }\r\n    },\r\n\r\n    clearInterview: (state) => {\r\n      // Reset state to initial values\r\n      state.roleSpecificQuestions = initialState.roleSpecificQuestions;\r\n      state.cultureSpecificQuestions = initialState.cultureSpecificQuestions;\r\n      state.careerBasedQuestions = initialState.careerBasedQuestions;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setInterviewQuestions, updateQuestionAnswer, clearInterview, setInterviewStaticInformation } = interviewSlice.actions;\r\n\r\nexport default interviewSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAgBA,MAAM,eAAkH;IACtH,uBAAuB,CAAC;IACxB,0BAA0B,CAAC;IAC3B,sBAAsB;QACpB,WAAW,EAAE;QACb,OAAO;IACT;IACA,4BAA4B;QAC1B,+BAA+B,EAAE;QACjC,gCAAgC,EAAE;QAClC,oBAAoB,EAAE;IACxB;AACF;AAEO,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,uBAAuB,CAAC,OAAO;YAC7B,iCAAiC;YACjC,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,WAAW;gBACtD,MAAM,qBAAqB,GAAG,OAAO,OAAO,CAAC,qBAAqB;YACpE;YAEA,oCAAoC;YACpC,IAAI,OAAO,OAAO,CAAC,wBAAwB,KAAK,WAAW;gBACzD,MAAM,wBAAwB,GAAG,OAAO,OAAO,CAAC,wBAAwB;YAC1E;YAEA,gCAAgC;YAChC,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,WAAW;gBACrD,MAAM,oBAAoB,GAAG,OAAO,OAAO,CAAC,oBAAoB;YAClE;QACF;QAEA,+BAA+B,CAAC,OAAO;YACrC,MAAM,0BAA0B,GAAG,OAAO,OAAO;QACnD;QAEA,sBAAsB,CAAC,OAAO;YAC5B,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,OAAO,OAAO;YAEjG,gCAAgC;YAChC,MAAM,YAAY,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAC,KAAO;oBAAC,GAAG,UAAU;oBAAE,GAAG,MAAM;iBAAC;YAEhF,OAAQ;gBACN,KAAK,sIAAA,CAAA,iBAAc,CAAC,YAAY;oBAC9B,iBAAiB;oBACjB,MAAM,oBAAoB,CAAC,SAAS,GAAG,MAAM,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBAC/E,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;wBACxC,IAAI,WAAW,WAAW;4BACxB,OAAO;gCAAE,GAAG,QAAQ;gCAAE;4BAAO;wBAC/B;wBACA,OAAO;oBACT;oBAEA,eAAe;oBACf,MAAM,oBAAoB,CAAC,KAAK,GAAG;oBACnC;gBAEF,KAAK,sIAAA,CAAA,iBAAc,CAAC,aAAa;oBAC/B,IAAI,UAAU;wBACZ,0CAA0C;wBAC1C,IAAI,CAAC,MAAM,qBAAqB,CAAC,SAAS,EAAE;4BAC1C,MAAM,qBAAqB,CAAC,SAAS,GAAG;gCACtC,WAAW,EAAE;gCACb,OAAO;4BACT;wBACF;wBAEA,iBAAiB;wBACjB,MAAM,qBAAqB,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,qBAAqB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BACrG,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;4BACxC,IAAI,WAAW,WAAW;gCACxB,OAAO;oCAAE,GAAG,QAAQ;oCAAE;gCAAO;4BAC/B;4BACA,OAAO;wBACT;wBAEA,oCAAoC;wBACpC,MAAM,qBAAqB,CAAC,SAAS,CAAC,KAAK,GAAG;wBAC9C,MAAM,qBAAqB,CAAC,SAAS,CAAC,eAAe,GAAG;oBAC1D;oBACA;gBAEF,KAAK,sIAAA,CAAA,iBAAc,CAAC,gBAAgB;oBAClC,IAAI,UAAU;wBACZ,0CAA0C;wBAC1C,IAAI,CAAC,MAAM,wBAAwB,CAAC,SAAS,EAAE;4BAC7C,MAAM,wBAAwB,CAAC,SAAS,GAAG;gCACzC,WAAW,EAAE;gCACb,OAAO;4BACT;wBACF;wBAEA,iBAAiB;wBACjB,MAAM,wBAAwB,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,wBAAwB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC3G,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;4BACxC,IAAI,WAAW,WAAW;gCACxB,OAAO;oCAAE,GAAG,QAAQ;oCAAE;gCAAO;4BAC/B;4BACA,OAAO;wBACT;wBAEA,oCAAoC;wBACpC,MAAM,wBAAwB,CAAC,SAAS,CAAC,KAAK,GAAG;wBACjD,MAAM,wBAAwB,CAAC,SAAS,CAAC,eAAe,GAAG;oBAC7D;oBACA;YACJ;QACF;QAEA,gBAAgB,CAAC;YACf,gCAAgC;YAChC,MAAM,qBAAqB,GAAG,aAAa,qBAAqB;YAChE,MAAM,wBAAwB,GAAG,aAAa,wBAAwB;YACtE,MAAM,oBAAoB,GAAG,aAAa,oBAAoB;QAChE;IACF;AACF;AAEO,MAAM,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,cAAc,EAAE,6BAA6B,EAAE,GAAG,eAAe,OAAO;uCAErH,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/notificationSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\nimport { NotificationItem } from \"@/interfaces/notificationInterface\";\r\n\r\nconst initialState: {\r\n  notifications: NotificationItem[];\r\n  hasUnreadNotifications: boolean;\r\n} = {\r\n  notifications: [],\r\n  hasUnreadNotifications: false,\r\n};\r\n\r\nexport const notificationSlice = createSlice({\r\n  name: \"notification\",\r\n  initialState,\r\n  reducers: {\r\n    setNotificationsData: (state, action: PayloadAction<NotificationItem[]>) => {\r\n      state.notifications = action.payload;\r\n    },\r\n    setHasUnreadNotification: (state, action: PayloadAction<boolean>) => {\r\n      state.hasUnreadNotifications = action.payload;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setNotificationsData, setHasUnreadNotification } = notificationSlice.actions;\r\nexport default notificationSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,MAAM,eAGF;IACF,eAAe,EAAE;IACjB,wBAAwB;AAC1B;AAEO,MAAM,oBAAoB,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC3C,MAAM;IACN;IACA,UAAU;QACR,sBAAsB,CAAC,OAAO;YAC5B,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QACA,0BAA0B,CAAC,OAAO;YAChC,MAAM,sBAAsB,GAAG,OAAO,OAAO;QAC/C;IACF;AACF;AAEO,MAAM,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,GAAG,kBAAkB,OAAO;uCAC5E,kBAAkB,OAAO", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/store.ts"], "sourcesContent": ["import { configureStore } from \"@reduxjs/toolkit\";\r\nimport { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from \"redux-persist\";\r\nimport storage from \"redux-persist/lib/storage\"; // defaults to localStorage for web\r\n\r\n// Import the reducers directly to avoid circular dependency\r\nimport jobSkillsReducer from \"./slices/jobSkillsSlice\";\r\nimport jobDetailsReducer from \"./slices/jobDetailsSlice\";\r\nimport allSkillsReducer from \"./slices/allSkillsSlice\";\r\nimport authReducer from \"./slices/authSlice\";\r\nimport jobRequirementReducer from \"./slices/jobRequirementSlice\";\r\nimport interviewReducer from \"./slices/interviewSlice\";\r\nimport notificationReducer from \"./slices/notificationSlice\";\r\n\r\n// Configure persist options for job skills slice\r\nconst jobSkillsPersistConfig = {\r\n  key: \"jobSkills\",\r\n  storage,\r\n};\r\n\r\n// Configure persist options for job details slice\r\nconst jobDetailsPersistConfig = {\r\n  key: \"jobDetails\",\r\n  storage,\r\n};\r\n\r\n// Configure persist options for all skills slice\r\nconst allSkillsPersistConfig = {\r\n  key: \"allSkills\",\r\n  storage,\r\n  blacklist: [\"loading\", \"error\"], // Don't persist loading and error states\r\n};\r\n\r\n// Configure persist options for auth slice\r\nconst authPersistConfig = {\r\n  key: \"auth\",\r\n  storage,\r\n};\r\n// Configure persist options for job requirement slice\r\nconst jobRequirementPersistConfig = {\r\n  key: \"jobRequirement\",\r\n  storage,\r\n};\r\n\r\n// Configure persist options for interview questions slice\r\nconst interviewPersistConfig = {\r\n  key: \"interview\",\r\n  storage,\r\n};\r\n\r\n// Configure persist options for notification slice\r\nconst notificationPersistConfig = {\r\n  key: \"notification\",\r\n  storage,\r\n};\r\n\r\n// Create persisted reducers\r\nconst persistedJobSkillsReducer = persistReducer(jobSkillsPersistConfig, jobSkillsReducer);\r\nconst persistedJobDetailsReducer = persistReducer(jobDetailsPersistConfig, jobDetailsReducer);\r\nconst persistedAllSkillsReducer = persistReducer(allSkillsPersistConfig, allSkillsReducer);\r\nconst persistedAuthReducer = persistReducer(authPersistConfig, authReducer);\r\nconst persistedJobRequirementReducer = persistReducer(jobRequirementPersistConfig, jobRequirementReducer);\r\nconst persistedInterviewReducer = persistReducer(interviewPersistConfig, interviewReducer);\r\nconst persistedNotificationReducer = persistReducer(notificationPersistConfig, notificationReducer);\r\n\r\n// Create store with the persisted reducers\r\nexport const store = configureStore({\r\n  reducer: {\r\n    jobSkills: persistedJobSkillsReducer,\r\n    jobDetails: persistedJobDetailsReducer,\r\n    allSkills: persistedAllSkillsReducer,\r\n    auth: persistedAuthReducer,\r\n    jobRequirement: persistedJobRequirementReducer,\r\n    interview: persistedInterviewReducer,\r\n    notification: persistedNotificationReducer,\r\n  },\r\n  middleware: (getDefaultMiddleware) =>\r\n    getDefaultMiddleware({\r\n      serializableCheck: {\r\n        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],\r\n      },\r\n    }),\r\n});\r\n\r\n// Create persistor\r\nexport const persistor = persistStore(store);\r\n\r\n// Infer the `RootState` and `AppDispatch` types from the store itself\r\nexport type RootState = ReturnType<typeof store.getState>;\r\nexport type AppDispatch = typeof store.dispatch;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA,qRAAiD,mCAAmC;AAEpF,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,iDAAiD;AACjD,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;AACT;AAEA,kDAAkD;AAClD,MAAM,0BAA0B;IAC9B,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;IACP,WAAW;QAAC;QAAW;KAAQ;AACjC;AAEA,2CAA2C;AAC3C,MAAM,oBAAoB;IACxB,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;AACT;AACA,sDAAsD;AACtD,MAAM,8BAA8B;IAClC,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;AACT;AAEA,0DAA0D;AAC1D,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;AACT;AAEA,mDAAmD;AACnD,MAAM,4BAA4B;IAChC,KAAK;IACL,SAAA,8JAAA,CAAA,UAAO;AACT;AAEA,4BAA4B;AAC5B,MAAM,4BAA4B,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,2IAAA,CAAA,UAAgB;AACzF,MAAM,6BAA6B,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,yBAAyB,4IAAA,CAAA,UAAiB;AAC5F,MAAM,4BAA4B,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,2IAAA,CAAA,UAAgB;AACzF,MAAM,uBAAuB,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,mBAAmB,sIAAA,CAAA,UAAW;AAC1E,MAAM,iCAAiC,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,6BAA6B,gJAAA,CAAA,UAAqB;AACxG,MAAM,4BAA4B,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,2IAAA,CAAA,UAAgB;AACzF,MAAM,+BAA+B,CAAA,GAAA,wMAAA,CAAA,iBAAc,AAAD,EAAE,2BAA2B,8IAAA,CAAA,UAAmB;AAG3F,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,WAAW;QACX,YAAY;QACZ,WAAW;QACX,MAAM;QACN,gBAAgB;QAChB,WAAW;QACX,cAAc;IAChB;IACA,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,YAAS;oBAAE,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,UAAO;oBAAE,sJAAA,CAAA,QAAK;oBAAE,sJAAA,CAAA,WAAQ;iBAAC;YACrE;QACF;AACJ;AAGO,MAAM,YAAY,CAAA,GAAA,oMAAA,CAAA,eAAY,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/ReduxProvider.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Provider } from \"react-redux\";\r\nimport { PersistGate } from \"redux-persist/integration/react\";\r\nimport { store, persistor } from \"./store\";\r\n\r\ninterface ReduxProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst ReduxProvider: React.FC<ReduxProviderProps> = ({ children }) => {\r\n  return (\r\n    <Provider store={store}>\r\n      <PersistGate persistor={persistor}>{children}</PersistGate>\r\n    </Provider>\r\n  );\r\n};\r\n\r\nexport default ReduxProvider;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IAC/D,qBACE,6LAAC,4JAAA,CAAA,WAAQ;QAAC,OAAO,wHAAA,CAAA,QAAK;kBACpB,cAAA,6LAAC,iKAAA,CAAA,cAAW;YAAC,WAAW,wHAAA,CAAA,YAAS;sBAAG;;;;;;;;;;;AAG1C;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/syncReduxToCookies.ts"], "sourcesContent": ["import { PERMISSIONS_COOKIES_KEY } from \"@/constants/commonConstants\";\r\nimport { Permission } from \"@/interfaces/authInterfaces\";\r\nimport { store } from \"@/redux/store\";\r\nimport Cookies from \"js-cookie\";\r\n\r\n// Serialize specific parts of Redux state to cookies\r\nexport const syncReduxStateToCookies = (permissions?: Permission[], forceSync = false) => {\r\n  try {\r\n    const permissionData = Cookies.get(PERMISSIONS_COOKIES_KEY);\r\n    if (!forceSync && permissionData) {\r\n      return;\r\n    }\r\n    const state = store.getState();\r\n\r\n    // Sync auth state to cookies (permissions are in auth state)\r\n    if (state.auth) {\r\n      Cookies.set(PERMISSIONS_COOKIES_KEY, JSON.stringify(permissions?.length ? permissions : state.auth.permissions), {\r\n        expires: 4, // 4 day\r\n        path: \"/\",\r\n        sameSite: \"strict\",\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error syncing Redux state to cookies:\", error);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAGO,MAAM,0BAA0B,CAAC,aAA4B,YAAY,KAAK;IACnF,IAAI;QACF,MAAM,iBAAiB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sIAAA,CAAA,0BAAuB;QAC1D,IAAI,CAAC,aAAa,gBAAgB;YAChC;QACF;QACA,MAAM,QAAQ,wHAAA,CAAA,QAAK,CAAC,QAAQ;QAE5B,6DAA6D;QAC7D,IAAI,MAAM,IAAI,EAAE;YACd,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sIAAA,CAAA,0BAAuB,EAAE,KAAK,SAAS,CAAC,aAAa,SAAS,cAAc,MAAM,IAAI,CAAC,WAAW,GAAG;gBAC/G,SAAS;gBACT,MAAM;gBACN,UAAU;YACZ;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACzD;AACF", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/logo.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 257, height: 70, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/down-arrow.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 15, height: 8, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAG,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/user.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 51, height: 50, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AAICAgEzMzM5iIiHuaqckfWjl472i4qKvDU1NT4CAgICAC8vLzSXl5fQq6ai/tCpjv/HpIr/q6el/piYmNYyMjI5AH9/f6qsrKz+rqqn/82hiP/UqIz/sKuo/6ysrP6FhYWxAKWlpeaoqav/k5ah/6SIfP+wkH//kZSf/6eoq/+pqansAKSkpeWCip3/UGGI/25sfP93cn//U2KJ/4CInf+np6fsAHt8fqhlcpH+SlqC/0xbgv9HWID/SluF/2hzkf5/gIKyACUnKzNLWXrQc3KC/qWWj/+Cf4z/UmCG/lZhfdYsLS86AAEBAgEWGyc6P0ZduZ2BcPWQfHH2PUdhvRkdJz4BAQICbTiKQnEKIQsAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsd,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/header.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"header-module-scss-module__4lyeFq__active\",\n  \"admin_info\": \"header-module-scss-module__4lyeFq__admin_info\",\n  \"align_header_search\": \"header-module-scss-module__4lyeFq__align_header_search\",\n  \"circle_img\": \"header-module-scss-module__4lyeFq__circle_img\",\n  \"dropdown_menu\": \"header-module-scss-module__4lyeFq__dropdown_menu\",\n  \"header\": \"header-module-scss-module__4lyeFq__header\",\n  \"header_buttons\": \"header-module-scss-module__4lyeFq__header_buttons\",\n  \"header_right\": \"header-module-scss-module__4lyeFq__header_right\",\n  \"hidden\": \"header-module-scss-module__4lyeFq__hidden\",\n  \"logo\": \"header-module-scss-module__4lyeFq__logo\",\n  \"navbar_content\": \"header-module-scss-module__4lyeFq__navbar_content\",\n  \"navbar_links\": \"header-module-scss-module__4lyeFq__navbar_links\",\n  \"open\": \"header-module-scss-module__4lyeFq__open\",\n  \"searchBar\": \"header-module-scss-module__4lyeFq__searchBar\",\n  \"searchButton\": \"header-module-scss-module__4lyeFq__searchButton\",\n  \"searchContainer\": \"header-module-scss-module__4lyeFq__searchContainer\",\n  \"searchIcon\": \"header-module-scss-module__4lyeFq__searchIcon\",\n  \"searchInput\": \"header-module-scss-module__4lyeFq__searchInput\",\n  \"search_wrapper\": \"header-module-scss-module__4lyeFq__search_wrapper\",\n  \"show\": \"header-module-scss-module__4lyeFq__show\",\n  \"user_drop\": \"header-module-scss-module__4lyeFq__user_drop\",\n  \"user_drop_btn\": \"header-module-scss-module__4lyeFq__user_drop_btn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/Notification.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface NotificationIconProps extends React.SVGProps<SVGSVGElement> {\r\n  hasNotification?: boolean;\r\n  onClick?: React.MouseEventHandler<SVGSVGElement>; // Added this line\r\n}\r\n\r\nfunction NotificationIcon(props: NotificationIconProps) {\r\n  const { hasNotification, ...restProps } = props;\r\n  return (\r\n    <svg {...restProps} className=\"cursor-pointer\" xmlns=\"http://www.w3.org/2000/svg\" width=\"25\" height=\"24\" viewBox=\"0 0 33 32\" fill=\"none\">\r\n      <path\r\n        d=\"M27.458 22.9624C26.251 20.8511 25.6133 18.4492 25.6133 16.0166C25.6133 16.0166 25.6133 14.0014 25.6133 14C25.6133 10.1198 22.9443 6.54149 19.2454 5.40924C19.4725 4.98712 19.6133 4.51202 19.6133 4C19.6133 2.3457 18.2676 1 16.6133 1C14.959 1 13.6133 2.3457 13.6133 4C13.6133 4.51233 13.7544 4.98767 13.9817 5.40997C10.2878 6.57581 7.61332 10.1457 7.61332 14.3071V16.0166C7.61332 18.4492 6.97562 20.8511 5.76811 22.9629C5.1221 24.0927 4.75006 25.2737 5.46489 26.5054C6.00736 27.4414 6.97758 28 8.05961 28H12.6133C12.6133 30.2056 14.4077 32 16.6133 32C18.8189 32 20.6133 30.2056 20.6133 28H25.167C26.249 28 27.2193 27.4414 27.7617 26.5054C28.4522 25.3141 28.0953 24.0784 27.458 22.9624ZM16.6133 3C17.1646 3 17.6133 3.44873 17.6133 4C17.6133 4.55127 17.1646 5 16.6133 5C16.062 5 15.6133 4.55127 15.6133 4C15.6133 3.44873 16.062 3 16.6133 3ZM16.6133 30C15.5103 30 14.6133 29.103 14.6133 28H18.6133C18.6133 29.103 17.7163 30 16.6133 30ZM26.0323 25.5019C25.9453 25.6514 25.687 26 25.167 26H8.05961C7.53967 26 7.28136 25.6515 7.19441 25.502C6.87823 24.9586 7.23496 24.428 7.50492 23.9546C8.88432 21.542 9.61332 18.7969 9.61332 16.0166C9.61332 16.0166 9.61332 14.3081 9.61332 14.3071C9.61332 10.5303 12.7077 7.00054 16.602 7.00049C20.3752 7.00044 23.6133 10.2392 23.6133 14V16.0166C23.6133 18.7968 24.3423 21.5419 25.7212 23.954C26.0017 24.4448 26.3567 24.9391 26.0323 25.5019Z\"\r\n        fill=\"#333333\"\r\n      />\r\n      {hasNotification && <circle cx=\"24.6136\" cy=\"10.6654\" r=\"4.83333\" fill=\"#D4000D\" stroke=\"white\" />}\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport default NotificationIcon;\r\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,iBAAiB,KAA4B;IACpD,MAAM,EAAE,eAAe,EAAE,GAAG,WAAW,GAAG;IAC1C,qBACE,6LAAC;QAAK,GAAG,SAAS;QAAE,WAAU;QAAiB,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAChI,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;YAEN,iCAAmB,6LAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAU,GAAE;gBAAU,MAAK;gBAAU,QAAO;;;;;;;;;;;;AAG9F;KAXS;uCAaM", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/storage.ts"], "sourcesContent": ["import SecureLS from \"secure-ls\";\r\n\r\nconst ls = new SecureLS();\r\n\r\ninterface Storage {\r\n  set: (key: string, data: unknown) => void;\r\n  get: (key: string) => unknown;\r\n  remove: (key: string) => void;\r\n  removeAll: () => void;\r\n  getAllKeys: () => string[];\r\n}\r\n\r\nconst storage: Storage = {\r\n  set: (key, data) => {\r\n    if (typeof window !== \"undefined\") {\r\n      ls.set(key, JSON.stringify(data));\r\n    }\r\n  },\r\n\r\n  get: (key) => {\r\n    if (typeof window !== \"undefined\") {\r\n      const data = ls.get(key);\r\n      if (data) {\r\n        return data ? JSON.parse(data) : null;\r\n      }\r\n    }\r\n    return null;\r\n  },\r\n\r\n  remove: (key) => {\r\n    if (typeof window !== \"undefined\") {\r\n      ls.remove(key);\r\n    }\r\n  },\r\n\r\n  removeAll: () => {\r\n    ls.removeAll();\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.clear();\r\n    }\r\n  },\r\n\r\n  getAllKeys: () => {\r\n    if (typeof window !== \"undefined\") {\r\n      const keys: string[] = ls.getAllKeys();\r\n      for (let i = 0; i < localStorage.length; i++) {\r\n        const key = localStorage.key(i);\r\n        if (key) {\r\n          keys.push(key);\r\n        }\r\n      }\r\n      return keys;\r\n    }\r\n    return [];\r\n  },\r\n};\r\n\r\nexport default storage;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,KAAK,IAAI,uJAAA,CAAA,UAAQ;AAUvB,MAAM,UAAmB;IACvB,KAAK,CAAC,KAAK;QACT,wCAAmC;YACjC,GAAG,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC;QAC7B;IACF;IAEA,KAAK,CAAC;QACJ,wCAAmC;YACjC,MAAM,OAAO,GAAG,GAAG,CAAC;YACpB,IAAI,MAAM;gBACR,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;YACnC;QACF;QACA,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,wCAAmC;YACjC,GAAG,MAAM,CAAC;QACZ;IACF;IAEA,WAAW;QACT,GAAG,SAAS;QACZ,wCAAmC;YACjC,aAAa,KAAK;QACpB;IACF;IAEA,YAAY;QACV,wCAAmC;YACjC,MAAM,OAAiB,GAAG,UAAU;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;gBAC7B,IAAI,KAAK;oBACP,KAAK,IAAI,CAAC;gBACZ;YACF;YACA,OAAO;QACT;;IAEF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/endpoint.ts"], "sourcesContent": ["// import config from \"@/config/config\";\r\n\r\nconst URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst endpoint = {\r\n  auth: {\r\n    SIGNIN: `${URL}/auth/sign-in`,\r\n    VERIFY_OTP: `${URL}/auth/verify-otp`,\r\n    RESEND_OTP: `${URL}/auth/resend-otp`,\r\n    FORGOT_PASSWORD: `${URL}/auth/forgot-password`,\r\n    RESET_PASSWORD: `${URL}/auth/reset-password`,\r\n    DELETE_SESSION: `${URL}/auth/delete-session`,\r\n    UPDATE_TIMEZONE: `${URL}/auth/update-timezone`,\r\n  },\r\n  interview: {\r\n    UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,\r\n    GET_INTERVIEWS: `${URL}/interview/get-interviews`,\r\n    GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,\r\n    GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,\r\n    UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,\r\n\r\n    GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,\r\n    GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,\r\n    UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,\r\n    ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,\r\n    GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,\r\n\r\n    GET_JOB_LIST: `${URL}/interview/get-job-list`,\r\n    GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,\r\n    END_INTERVIEW: `${URL}/interview/end-interview`,\r\n    CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`,\r\n  },\r\n  common: {\r\n    REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,\r\n    GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`,\r\n  },\r\n  jobRequirements: {\r\n    GENERATE_SKILL: `${URL}/jobs/generate-skills`,\r\n    UPLOAD_URL: `${URL}/jobs/upload-url`,\r\n    PARSE_PDF: `${URL}/jobs/parse-pdf`,\r\n    GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,\r\n    GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,\r\n    SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,\r\n    GET_JOBS_META: `${URL}/jobs/get-jobs-meta`, // <-- Full URL here\r\n    UPDATE_JOB_STATUS: \"/jobs/updateJob\",\r\n    GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,\r\n    UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,\r\n    GENERATE_PDF: `${URL}/jobs/generate-pdf`,\r\n  },\r\n  Dashboard: {\r\n    GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`,\r\n  },\r\n  resumeScreen: {\r\n    MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,\r\n    GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,\r\n    GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,\r\n    CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`,\r\n  },\r\n  employee: {\r\n    ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,\r\n    GET_EMPLOYEES: `${URL}/employee-management`,\r\n    GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,\r\n    UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,\r\n    UPDATE_EMPLOYEE_STATUS: `${URL}/employee-management/employee/change-status/:employeeId`,\r\n    // this task is for future use\r\n    // DELETE_EMPLOYEE: `${URL}/employee-management/employee/:employeeId`, // original\r\n    DELETE_EMPLOYEE: `${URL}/employee-management/dummy`, //dummy\r\n\r\n    UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`,\r\n  },\r\n  userprofile: {\r\n    GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,\r\n    UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`,\r\n  },\r\n\r\n  roles: {\r\n    GET_ROLES_WITH_PAGINATION: `${URL}/access-management/user-roles-pagination`,\r\n    GET_ROLES: `${URL}/access-management/user-roles`,\r\n    ADD_USER_ROLE: `${URL}/access-management/add-user-role`,\r\n    UPDATE_USER_ROLE: `${URL}/access-management/user-role`,\r\n    DELETE_USER_ROLE: `${URL}/access-management/user-role`,\r\n    GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,\r\n    GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,\r\n    UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,\r\n    USER_PERMISSIONS: `${URL}/access-management/user-permissions`,\r\n  },\r\n  notification: {\r\n    UPDATE_NOTIFICATION: `${URL}/notifications/mark-as-watched`,\r\n    GET_NOTIFICATIONS: `${URL}/notifications/get-notifications`,\r\n    DELETE_ALL_NOTIFICATIONS: `${URL}/notifications/delete-users-all-notifications`,\r\n    GET_UNREAD_NOTIFICATIONS_COUNT: `${URL}/notifications/get-unread-notifications-count`,\r\n  },\r\n\r\n  departments: {\r\n    GET_DEPARTMENTS: `${URL}/employee-management/departments`,\r\n    ADD_DEPARTMENT: `${URL}/employee-management/add-department`,\r\n    UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,\r\n    DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`,\r\n  },\r\n\r\n  assessment: {\r\n    CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,\r\n    GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,\r\n    CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,\r\n    SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,\r\n    SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,\r\n    GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,\r\n    SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,\r\n    GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,\r\n    VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,\r\n    GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`,\r\n  },\r\n  candidatesApplication: {\r\n    ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,\r\n    PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`, // Base URL for candidates-related endpoints\r\n    GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,\r\n    GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,\r\n    ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,\r\n    GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`, // New endpoint for individual candidate details\r\n    UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`, // Endpoint for updating job application status\r\n    GET_CANDIDATE_INTERVIEW_HISTORY: `${URL}/candidates/get-candidate-interview-history/:candidateId/:applicationId`,\r\n    GET_APPLICATION_FINAL_SUMMARY: `${URL}/candidates/application-final-summary/:candidateId`,\r\n    GET_APPLICATION_SKILL_SCORE_DATA: `${URL}/candidates/application-skill-score-data/:candidateId`,\r\n    GENERATE_FINAL_SUMMARY: `${URL}/candidates/generate-final-summary`, // Endpoint for generating final summary\r\n  },\r\n  subscription: {\r\n    GET_ALL_PLANS: `${URL}/subscription/all`,\r\n    GET_CURRENT_SUBSCRIPTION: `${URL}/subscription/current`,\r\n    CANCEL_SUBSCRIPTION: `${URL}/subscription/cancel`,\r\n    GET_TRANSACTIONS: `${URL}/subscription/transactions`,\r\n    BUY_SUBSCRIPTION: `${URL}/subscription/buy-subscription`,\r\n  },\r\n};\r\n\r\nexport default endpoint;\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;AAE5B;AAAZ,MAAM;AAEN,MAAM,WAAW;IACf,MAAM;QACJ,QAAQ,GAAG,IAAI,aAAa,CAAC;QAC7B,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;QAC9C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;IAChD;IACA,WAAW;QACT,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,gBAAgB,GAAG,IAAI,yBAAyB,CAAC;QACjD,kBAAkB,GAAG,IAAI,2BAA2B,CAAC;QACrD,mBAAmB,GAAG,IAAI,4BAA4B,CAAC;QACvD,0BAA0B,GAAG,IAAI,mCAAmC,CAAC;QAErE,gCAAgC,GAAG,IAAI,0CAA0C,CAAC;QAClF,+BAA+B,GAAG,IAAI,wCAAwC,CAAC;QAC/E,iCAAiC,GAAG,IAAI,0CAA0C,CAAC;QACnF,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,sBAAsB,GAAG,IAAI,+BAA+B,CAAC;QAE7D,cAAc,GAAG,IAAI,uBAAuB,CAAC;QAC7C,oBAAoB,GAAG,IAAI,6BAA6B,CAAC;QACzD,eAAe,GAAG,IAAI,wBAAwB,CAAC;QAC/C,sCAAsC,GAAG,IAAI,+CAA+C,CAAC;IAC/F;IACA,QAAQ;QACN,4BAA4B,GAAG,IAAI,2BAA2B,CAAC;QAC/D,wBAAwB,GAAG,IAAI,sBAAsB,CAAC;IACxD;IACA,iBAAiB;QACf,gBAAgB,GAAG,IAAI,qBAAqB,CAAC;QAC7C,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,WAAW,GAAG,IAAI,eAAe,CAAC;QAClC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,kBAAkB,GAAG,IAAI,sBAAsB,CAAC;QAChD,eAAe,GAAG,IAAI,mBAAmB,CAAC;QAC1C,mBAAmB;QACnB,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,wBAAwB,GAAG,IAAI,4BAA4B,CAAC;QAC5D,cAAc,GAAG,IAAI,kBAAkB,CAAC;IAC1C;IACA,WAAW;QACT,sBAAsB,GAAG,IAAI,sBAAsB,CAAC;IACtD;IACA,cAAc;QACZ,yBAAyB,GAAG,IAAI,sCAAsC,CAAC;QACvE,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,kCAAkC,GAAG,IAAI,+CAA+C,CAAC;QACzF,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;IAC7E;IACA,UAAU;QACR,eAAe,GAAG,IAAI,wCAAwC,CAAC;QAC/D,eAAe,GAAG,IAAI,oBAAoB,CAAC;QAC3C,6BAA6B,GAAG,IAAI,8BAA8B,CAAC;QACnE,sBAAsB,GAAG,IAAI,8CAA8C,CAAC;QAC5E,wBAAwB,GAAG,IAAI,uDAAuD,CAAC;QACvF,8BAA8B;QAC9B,kFAAkF;QAClF,iBAAiB,GAAG,IAAI,0BAA0B,CAAC;QAEnD,iCAAiC,GAAG,IAAI,yDAAyD,CAAC;IACpG;IACA,aAAa;QACX,gBAAgB,GAAG,IAAI,4BAA4B,CAAC;QACpD,mBAAmB,GAAG,IAAI,+BAA+B,CAAC;IAC5D;IAEA,OAAO;QACL,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;QAC3E,WAAW,GAAG,IAAI,6BAA6B,CAAC;QAChD,eAAe,GAAG,IAAI,gCAAgC,CAAC;QACvD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,sBAAsB,GAAG,IAAI,mCAAmC,CAAC;QACjE,4BAA4B,GAAG,IAAI,2CAA2C,CAAC;QAC/E,yBAAyB,GAAG,IAAI,2CAA2C,CAAC;QAC5E,kBAAkB,GAAG,IAAI,mCAAmC,CAAC;IAC/D;IACA,cAAc;QACZ,qBAAqB,GAAG,IAAI,8BAA8B,CAAC;QAC3D,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,0BAA0B,GAAG,IAAI,6CAA6C,CAAC;QAC/E,gCAAgC,GAAG,IAAI,6CAA6C,CAAC;IACvF;IAEA,aAAa;QACX,iBAAiB,GAAG,IAAI,gCAAgC,CAAC;QACzD,gBAAgB,GAAG,IAAI,mCAAmC,CAAC;QAC3D,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;QAC/E,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;IACjF;IAEA,YAAY;QACV,yBAAyB,GAAG,IAAI,yCAAyC,CAAC;QAC1E,gCAAgC,GAAG,IAAI,sCAAsC,CAAC;QAC9E,4BAA4B,GAAG,IAAI,4CAA4C,CAAC;QAChF,0BAA0B,GAAG,IAAI,+CAA+C,CAAC;QACjF,kBAAkB,GAAG,IAAI,kCAAkC,CAAC;QAC5D,mCAAmC,GAAG,IAAI,sCAAsC,CAAC;QACjF,mBAAmB,GAAG,IAAI,6CAA6C,CAAC;QACxE,uBAAuB,GAAG,IAAI,mCAAmC,CAAC;QAClE,wBAAwB,GAAG,IAAI,wCAAwC,CAAC;QACxE,2BAA2B,GAAG,IAAI,2CAA2C,CAAC;IAChF;IACA,uBAAuB;QACrB,iBAAiB,GAAG,IAAI,yCAAyC,CAAC;QAClE,0BAA0B,GAAG,IAAI,wCAAwC,CAAC;QAC1E,sCAAsC,GAAG,IAAI,0BAA0B,CAAC;QACxE,kCAAkC,GAAG,IAAI,0BAA0B,CAAC;QACpE,4BAA4B,GAAG,IAAI,qDAAqD,CAAC;QACzF,uBAAuB,GAAG,IAAI,iCAAiC,CAAC;QAChE,+BAA+B,GAAG,IAAI,2DAA2D,CAAC;QAClG,iCAAiC,GAAG,IAAI,uEAAuE,CAAC;QAChH,+BAA+B,GAAG,IAAI,kDAAkD,CAAC;QACzF,kCAAkC,GAAG,IAAI,qDAAqD,CAAC;QAC/F,wBAAwB,GAAG,IAAI,kCAAkC,CAAC;IACpE;IACA,cAAc;QACZ,eAAe,GAAG,IAAI,iBAAiB,CAAC;QACxC,0BAA0B,GAAG,IAAI,qBAAqB,CAAC;QACvD,qBAAqB,GAAG,IAAI,oBAAoB,CAAC;QACjD,kBAAkB,GAAG,IAAI,0BAA0B,CAAC;QACpD,kBAAkB,GAAG,IAAI,8BAA8B,CAAC;IAC1D;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/config/config.ts"], "sourcesContent": ["const config = {\r\n  env: process.env.NODE_ENV,\r\n  apiBaseUrl: process.env.NEXT_PUBLIC_BASE_URL,\r\n};\r\n\r\nexport default config;\r\n"], "names": [], "mappings": ";;;AACO;AADP,MAAM,SAAS;IACb,GAAG;IACH,UAAU;AACZ;uCAEe", "debugId": null}}, {"offset": {"line": 1295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/api.ts"], "sourcesContent": ["import { ApiResponse } from \"@/interfaces/commonInterfaces\";\r\nimport { TApiState } from \"@/types/types\";\r\n\r\nconst { toString } = Object.prototype;\r\n\r\nexport const isObject = <T>(arg: T): boolean => toString.call(arg) === \"[object Object]\";\r\n\r\nexport const withError = <T extends TApiState>(arg: T): ApiResponse => {\r\n  if (isObject(arg)) {\r\n    return {\r\n      data: null,\r\n      error: {\r\n        ...arg,\r\n      },\r\n    };\r\n  }\r\n\r\n  return {\r\n    data: null,\r\n    error: {\r\n      message: arg,\r\n    },\r\n  };\r\n};\r\n\r\nexport const withData = <T extends TApiState>(data: T): ApiResponse => ({\r\n  error: null,\r\n  data,\r\n});\r\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,SAAS;AAE9B,MAAM,WAAW,CAAI,MAAoB,SAAS,IAAI,CAAC,SAAS;AAEhE,MAAM,YAAY,CAAsB;IAC7C,IAAI,SAAS,MAAM;QACjB,OAAO;YACL,MAAM;YACN,OAAO;gBACL,GAAG,GAAG;YACR;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN,OAAO;YACL,SAAS;QACX;IACF;AACF;AAEO,MAAM,WAAW,CAAsB,OAAyB,CAAC;QACtE,OAAO;QACP;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/helper.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\r\nimport Cookies from \"js-cookie\";\r\nimport toast from \"react-hot-toast\";\r\n\r\nimport storage from \"./storage\";\r\n\r\nimport { ACCESS_TOKEN_KEY, PERMISSIONS_COOKIES_KEY } from \"@/constants/commonConstants\";\r\nimport { deleteSession } from \"@/services/authServices\";\r\nimport { getSignedUrl } from \"@/services/commonService\";\r\nimport { FilePath } from \"@/interfaces/commonInterfaces\";\r\n\r\nexport const getAccessToken = () => {\r\n  return storage.get(ACCESS_TOKEN_KEY);\r\n};\r\n\r\nexport const clearStorage = () => {\r\n  return storage.removeAll();\r\n};\r\n\r\nexport const setAccessToken = (accessToken: string) => {\r\n  storage.set(ACCESS_TOKEN_KEY, accessToken);\r\n};\r\n\r\n/**\r\n * Toast style object\r\n */\r\nconst style = {\r\n  fontSize: \"16px\",\r\n};\r\n\r\n/**\r\n * Toast success message\r\n * @param message - The message to display\r\n */\r\nexport const toastMessageSuccess = (message: string) => {\r\n  toast.success(message, {\r\n    style,\r\n  });\r\n};\r\n\r\n/**\r\n * Toast success message with icon\r\n * @param message - The message to display\r\n * @param icon - The icon to display\r\n */\r\nexport const toastMessageWithIcon = (message: string, icon: string) => {\r\n  toast.success(message, {\r\n    style,\r\n    icon,\r\n  });\r\n};\r\n\r\n/**\r\n * Toast error message\r\n * @param message - The message to display\r\n */\r\nexport const toastMessageError = (message: string) => {\r\n  toast.error(message, {\r\n    style,\r\n  });\r\n};\r\n\r\n/**\r\n * Dismiss all existing toast notifications\r\n */\r\nexport const dismissAllToasts = () => {\r\n  toast.dismiss();\r\n};\r\n\r\nexport const logout = async (userId?: number) => {\r\n  try {\r\n    deleteSession(userId);\r\n    await signOut({ redirect: false });\r\n    clearStorage();\r\n\r\n    // Delete permissions_data cookies when user logs out\r\n    Cookies.remove(PERMISSIONS_COOKIES_KEY, { path: \"/\" });\r\n  } catch (error) {\r\n    console.error(\"Error in logout:\", error);\r\n  }\r\n};\r\n\r\n/**\r\n *  get presignedUrl for image upload\r\n */\r\nexport const uploadFileOnS3 = async (file: Blob, filePath: string) => {\r\n  let body: FilePath = {\r\n    filePath: \"\",\r\n    fileFormat: \"\",\r\n  };\r\n  body = {\r\n    filePath,\r\n    fileFormat: file.type as string,\r\n  };\r\n  let signedUrl;\r\n  const presignedUrl = await getSignedUrl(body);\r\n  if (presignedUrl && presignedUrl.data) {\r\n    const response = await pushFileToS3(presignedUrl.data.data, file);\r\n    if (response?.url) {\r\n      signedUrl = response?.url.split(\"?\")?.[0];\r\n    }\r\n  }\r\n\r\n  return signedUrl?.replace(`${process.env.NEXT_PUBLIC_S3_URL}`, `${process.env.NEXT_PUBLIC_S3_CDN_URL}`);\r\n};\r\n\r\n/**\r\n *  Upload file on presignedUrl of S3\r\n */\r\nexport const pushFileToS3 = async (signedUrl: string, file: Blob): Promise<Response> => {\r\n  return fetch(signedUrl, {\r\n    method: \"PUT\",\r\n    body: file,\r\n    headers: {\r\n      \"Content-Type\": file.type,\r\n    },\r\n  });\r\n};\r\n\r\nexport const formatDate = (dateString: string) => {\r\n  const date = new Date(dateString);\r\n  return date.toLocaleDateString(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n  });\r\n};\r\n// Format times as HH:MM for time inputs\r\nexport const formatTimeForInput = (date: Date) => {\r\n  const hours = date.getHours().toString().padStart(2, \"0\");\r\n  const minutes = date.getMinutes().toString().padStart(2, \"0\");\r\n  return `${hours}:${minutes}`;\r\n};\r\n\r\nexport const toTitleCase = (name: string) => {\r\n  if (!name) return \"\";\r\n  return name\r\n    .toLowerCase()\r\n    .split(\" \")\r\n    .filter((word) => word) // remove extra spaces\r\n    .map((word) => word[0].toUpperCase() + word.slice(1))\r\n    .join(\" \");\r\n};\r\n\r\n// Normalize spaces (replace multiple spaces with a single space)\r\nexport const normalizeSpaces = (text: string): string => {\r\n  return text.trim().replace(/\\s+/g, \" \");\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAuG+B;AAvG/B;AACA;AACA;AAEA;AAEA;AACA;AACA;;;;;;;;AAGO,MAAM,iBAAiB;IAC5B,OAAO,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sIAAA,CAAA,mBAAgB;AACrC;AAEO,MAAM,eAAe;IAC1B,OAAO,0HAAA,CAAA,UAAO,CAAC,SAAS;AAC1B;AAEO,MAAM,iBAAiB,CAAC;IAC7B,0HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sIAAA,CAAA,mBAAgB,EAAE;AAChC;AAEA;;CAEC,GACD,MAAM,QAAQ;IACZ,UAAU;AACZ;AAMO,MAAM,sBAAsB,CAAC;IAClC,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;QACrB;IACF;AACF;AAOO,MAAM,uBAAuB,CAAC,SAAiB;IACpD,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;QACrB;QACA;IACF;AACF;AAMO,MAAM,oBAAoB,CAAC;IAChC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS;QACnB;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,0JAAA,CAAA,UAAK,CAAC,OAAO;AACf;AAEO,MAAM,SAAS,OAAO;IAC3B,IAAI;QACF,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD,EAAE;QACd,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC;QAEA,qDAAqD;QACrD,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,sIAAA,CAAA,0BAAuB,EAAE;YAAE,MAAM;QAAI;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;IACpC;AACF;AAKO,MAAM,iBAAiB,OAAO,MAAY;IAC/C,IAAI,OAAiB;QACnB,UAAU;QACV,YAAY;IACd;IACA,OAAO;QACL;QACA,YAAY,KAAK,IAAI;IACvB;IACA,IAAI;IACJ,MAAM,eAAe,MAAM,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;IACxC,IAAI,gBAAgB,aAAa,IAAI,EAAE;QACrC,MAAM,WAAW,MAAM,aAAa,aAAa,IAAI,CAAC,IAAI,EAAE;QAC5D,IAAI,UAAU,KAAK;YACjB,YAAY,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE;QAC3C;IACF;IAEA,OAAO,WAAW,QAAQ,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE;AACxG;AAKO,MAAM,eAAe,OAAO,WAAmB;IACpD,OAAO,MAAM,WAAW;QACtB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACrD,MAAM,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACzD,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KACJ,WAAW,GACX,KAAK,CAAC,KACN,MAAM,CAAC,CAAC,OAAS,MAAM,sBAAsB;KAC7C,GAAG,CAAC,CAAC,OAAS,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,KAAK,CAAC,IACjD,IAAI,CAAC;AACV;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,KAAK,IAAI,GAAG,OAAO,CAAC,QAAQ;AACrC", "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/http.ts"], "sourcesContent": ["// src/utils/http.ts\r\nimport axios, { AxiosResponse } from \"axios\";\r\nimport { getSession } from \"next-auth/react\";\r\n\r\nimport env from \"@/config/config\";\r\nimport { withData, withError } from \"@/utils/api\";\r\nimport { ISession } from \"@/interfaces/commonInterfaces\";\r\nimport { logout, toastMessageError } from \"./helper\";\r\nimport { TOKEN_EXPIRED } from \"@/constants/commonConstants\";\r\n\r\nexport const http = axios.create({\r\n  baseURL: env.apiBaseUrl,\r\n  headers: { \"Content-Type\": \"application/json\" },\r\n});\r\n\r\nhttp.interceptors.request.use(async (req) => {\r\n  const session = (await getSession()) as unknown as ISession;\r\n  const accessToken = session?.user?.data?.token;\r\n  if (accessToken) {\r\n    req.headers.authorization = `Bearer ${accessToken}`;\r\n  }\r\n  req.headers[\"ngrok-skip-browser-warning\"] = \"fjdlkghjsk\";\r\n  return req;\r\n});\r\n\r\n// Flag to prevent multiple logout calls\r\nlet isLoggingOut = false;\r\n\r\nhttp.interceptors.response.use(\r\n  (res) => withData(res.data) as AxiosResponse,\r\n  async (err) => {\r\n    const session = (await getSession()) as unknown as ISession;\r\n    const userId = session?.user?.data?.authData?.userData?.id;\r\n    const accessToken = session?.user?.data?.token;\r\n\r\n    if (err?.response?.status === 401 && !isLoggingOut && accessToken) {\r\n      isLoggingOut = true;\r\n      try {\r\n        await logout(userId);\r\n        toastMessageError(TOKEN_EXPIRED);\r\n\r\n        if (typeof window !== \"undefined\") {\r\n          window.location.reload();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Session cleanup error:\", error);\r\n      } finally {\r\n        isLoggingOut = false;\r\n      }\r\n    } else if (err?.response?.status === 403) {\r\n      // Show toast message for forbidden access (403)\r\n      toastMessageError(err?.response?.data?.message);\r\n    }\r\n    return withError(err?.response?.data?.error);\r\n  }\r\n);\r\n\r\nexport function get<P, R>(url: string, params?: P): Promise<R> {\r\n  return http({\r\n    method: \"get\",\r\n    url,\r\n    params,\r\n  });\r\n}\r\n\r\nexport function post<D, P, R>(url: string, data: D, params?: P): Promise<R> {\r\n  return http({\r\n    method: \"post\",\r\n    url,\r\n    data,\r\n    params,\r\n  });\r\n}\r\n\r\nexport function postFile<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {\r\n  return http({\r\n    method: \"post\",\r\n    url,\r\n    data,\r\n    params,\r\n    headers: { \"Content-Type\": \"multipart/form-data\" },\r\n  });\r\n}\r\nexport function put<D, P, R>(url: string, data: D, params?: P): Promise<R> {\r\n  return http({\r\n    method: \"put\",\r\n    url,\r\n    data,\r\n    params,\r\n  });\r\n}\r\n\r\nexport function patch<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {\r\n  return http({\r\n    method: \"patch\",\r\n    url,\r\n    data,\r\n    params,\r\n  });\r\n}\r\nexport function remove<P, R>(url: string, params?: P): Promise<R> {\r\n  return http({\r\n    method: \"delete\",\r\n    url,\r\n    params,\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;AACpB;AACA;AAEA;AACA;AAEA;AACA;;;;;;;AAEO,MAAM,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS,0HAAA,CAAA,UAAG,CAAC,UAAU;IACvB,SAAS;QAAE,gBAAgB;IAAmB;AAChD;AAEA,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO;IACnC,MAAM,UAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAChC,MAAM,cAAc,SAAS,MAAM,MAAM;IACzC,IAAI,aAAa;QACf,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;IACrD;IACA,IAAI,OAAO,CAAC,6BAA6B,GAAG;IAC5C,OAAO;AACT;AAEA,wCAAwC;AACxC,IAAI,eAAe;AAEnB,KAAK,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC5B,CAAC,MAAQ,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,IAAI,GAC1B,OAAO;IACL,MAAM,UAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAChC,MAAM,SAAS,SAAS,MAAM,MAAM,UAAU,UAAU;IACxD,MAAM,cAAc,SAAS,MAAM,MAAM;IAEzC,IAAI,KAAK,UAAU,WAAW,OAAO,CAAC,gBAAgB,aAAa;QACjE,eAAe;QACf,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,SAAM,AAAD,EAAE;YACb,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,sIAAA,CAAA,gBAAa;YAE/B,wCAAmC;gBACjC,OAAO,QAAQ,CAAC,MAAM;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,eAAe;QACjB;IACF,OAAO,IAAI,KAAK,UAAU,WAAW,KAAK;QACxC,gDAAgD;QAChD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,UAAU,MAAM;IACzC;IACA,OAAO,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,MAAM;AACxC;AAGK,SAAS,IAAU,GAAW,EAAE,MAAU;IAC/C,OAAO,KAAK;QACV,QAAQ;QACR;QACA;IACF;AACF;AAEO,SAAS,KAAc,GAAW,EAAE,IAAO,EAAE,MAAU;IAC5D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AAEO,SAAS,SAAkB,GAAW,EAAE,IAAO,EAAE,MAAU;IAChE,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;QACA,SAAS;YAAE,gBAAgB;QAAsB;IACnD;AACF;AACO,SAAS,IAAa,GAAW,EAAE,IAAO,EAAE,MAAU;IAC3D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AAEO,SAAS,MAAe,GAAW,EAAE,IAAO,EAAE,MAAU;IAC7D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AACO,SAAS,OAAa,GAAW,EAAE,MAAU;IAClD,OAAO,KAAK;QACV,QAAQ;QACR;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/authServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\r\nimport { IForgotPassword, ILogin, IResendOTP, IResetPassword, IVerifyOTP, UserPermissionsResponse } from \"@/interfaces/authInterfaces\";\r\nimport * as http from \"@/utils/http\";\r\nimport { ApiResponse, IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\r\nimport { AxiosResponse } from \"axios\";\r\n\r\n// Using UserPermissionsResponse interface from authInterfaces.ts\r\n\r\nexport const logIn = (data: ILogin): Promise<ApiResponse> => {\r\n  return http.post(endpoint.auth.SIGNIN, data);\r\n};\r\n\r\nexport const verifyOTP = (data: IVerifyOTP): Promise<IApiResponseCommonInterface<string>> => {\r\n  return http.post(endpoint.auth.VERIFY_OTP, data);\r\n};\r\n\r\nexport const resendOTP = (data: IResendOTP): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.auth.RESEND_OTP, data);\r\n};\r\n\r\nexport const forgotPassword = (data: IForgotPassword): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.auth.FORGOT_PASSWORD, data);\r\n};\r\n\r\nexport const resetPassword = (data: IResetPassword): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.auth.RESET_PASSWORD, data);\r\n};\r\n\r\nexport const deleteSession = (userId?: number): Promise<ApiResponse | AxiosResponse> => {\r\n  return http.remove(`${endpoint.auth.DELETE_SESSION}/${userId}`);\r\n};\r\n\r\nexport const updateTimezone = (data: { timezone: string }): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.auth.UPDATE_TIMEZONE, data);\r\n};\r\n\r\nexport const getUserPermissions = (): Promise<IApiResponseCommonInterface<UserPermissionsResponse>> => {\r\n  return http.get(endpoint.roles.USER_PERMISSIONS);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;AAMO,MAAM,QAAQ,CAAC;IACpB,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;AACzC;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,eAAe,EAAE;AAClD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,cAAc,EAAE;AACjD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,uHAAA,CAAA,SAAW,AAAD,EAAE,GAAG,+HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,QAAQ;AAChE;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,eAAe,EAAE;AAClD;AAEO,MAAM,qBAAqB;IAChC,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB;AACjD", "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1641, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/commonService.ts"], "sourcesContent": ["import * as http from \"@/utils/http\";\r\nimport endpoint from \"@/constants/endpoint\";\r\nimport { ApiResponse, FilePath } from \"@/interfaces/commonInterfaces\";\r\n\r\nexport const removeAttachmentsFromS3 = (data: { fileUrlArray: string }): Promise<ApiResponse<null>> => {\r\n  return http.post(endpoint.common.REMOVE_ATTACHMENTS_FROM_S3, data);\r\n};\r\n\r\nexport const getSignedUrl = (data: FilePath): Promise<ApiResponse<string | null>> => {\r\n  return http.post(endpoint.common.GENERATE_PRESIGNED_URL, data);\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,0BAA0B,EAAE;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,sBAAsB,EAAE;AAC3D", "debugId": null}}, {"offset": {"line": 1658, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1664, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/dataSecurityIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction dataSecurityIcon() {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" viewBox=\"0 0 43 42\" fill=\"none\">\r\n      <circle cx=\"21.5\" cy=\"21\" r=\"21\" fill=\"url(#paint0_linear_9593_1613)\" />\r\n      <path\r\n        d=\"M21.5091 9.28711H21.4886C18.2484 9.93022 15.0058 10.5721 11.7656 11.2139C11.7927 14.3154 11.8183 17.4181 11.8451 20.5211C11.8835 25.2709 14.6681 29.5713 18.9852 31.5523C19.8209 31.9347 20.6541 32.3187 21.4886 32.7014V32.7102C21.4924 32.709 21.4961 32.7064 21.499 32.7052C21.5015 32.7064 21.5053 32.709 21.5094 32.7102V32.7014C22.3439 32.3187 23.1771 31.935 24.0128 31.5523C28.3298 29.5716 31.1144 25.2709 31.1529 20.5211C31.1797 17.4184 31.2055 14.3154 31.2323 11.2139C27.9919 10.5721 24.7492 9.93022 21.5091 9.28711ZM29.4181 20.6065C29.3856 24.503 27.1019 28.0303 23.5604 29.6558C22.8757 29.9694 22.1919 30.2841 21.5072 30.5974V30.605C21.5043 30.604 21.5015 30.6021 21.4987 30.6012C21.4968 30.6021 21.4939 30.604 21.4911 30.605V30.5974C20.8064 30.2837 20.1226 29.9691 19.4379 29.6558C15.8964 28.0303 13.6127 24.503 13.5802 20.6065C13.5581 18.0621 13.537 15.5171 13.515 12.9727C16.1735 12.4462 18.8326 11.9198 21.4911 11.3924H21.5075C24.166 11.9198 26.8251 12.4462 29.4837 12.9727C29.4616 15.5171 29.4405 18.0621 29.4184 20.6065H29.4181Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M25.0804 18.5382H24.734V17.2775C24.734 15.5752 23.3538 14.1953 21.6518 14.1953H21.3743C19.672 14.1953 18.2921 15.5752 18.2921 17.2775V18.5382H17.9457C17.6619 18.5382 17.4321 18.768 17.4321 19.0517V24.4369C17.4321 24.7206 17.6619 24.9517 17.9457 24.9517H25.0807C25.3645 24.9517 25.5955 24.7206 25.5955 24.4369V19.0517C25.5955 18.768 25.3645 18.5382 25.0807 18.5382H25.0804ZM21.7965 21.7077V23.3868C21.7965 23.4804 21.7195 23.5576 21.6243 23.5576H21.3996C21.3059 23.5576 21.2287 23.4807 21.2287 23.3868V21.7077C20.9525 21.5961 20.7561 21.3253 20.7561 21.0069C20.7561 20.5949 21.0875 20.2598 21.4982 20.2535C21.5033 20.2522 21.5074 20.2522 21.5124 20.2522C21.9282 20.2522 22.2671 20.5899 22.2671 21.0069C22.2671 21.3253 22.072 21.5961 21.7961 21.7077H21.7965ZM23.7554 18.5382H19.2136V17.1672C19.2136 15.967 20.1855 14.9938 21.3869 14.9938H21.5808C22.7822 14.9938 23.7554 15.967 23.7554 17.1672V18.5382Z\"\r\n        fill=\"white\"\r\n      />\r\n      <defs>\r\n        <linearGradient id=\"paint0_linear_9593_1613\" x1=\"-2.3\" y1=\"17.5\" x2=\"29.5828\" y2=\"-6.01022\" gradientUnits=\"userSpaceOnUse\">\r\n          <stop stopColor=\"#74A8FF\" />\r\n          <stop offset=\"0.474301\" stopColor=\"#AACAFF\" />\r\n          <stop offset=\"1\" stopColor=\"#5D86CC\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport default dataSecurityIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BACtF,6LAAC;gBAAO,IAAG;gBAAO,IAAG;gBAAK,GAAE;gBAAK,MAAK;;;;;;0BACtC,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;0BACC,cAAA,6LAAC;oBAAe,IAAG;oBAA0B,IAAG;oBAAO,IAAG;oBAAO,IAAG;oBAAU,IAAG;oBAAW,eAAc;;sCACxG,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;4BAAK,QAAO;4BAAW,WAAU;;;;;;sCAClC,6LAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;uCAEe", "debugId": null}}, {"offset": {"line": 1757, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1763, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\r\n  LOGIN: \"/login\",\r\n  FORGOT_PASSWORD: \"/forgot-password\",\r\n  VERIFY: \"/verify\",\r\n  RESET_PASSWORD: \"/reset-password\",\r\n  CANDIDATE_ASSESSMENT: \"/candidate-assessment\",\r\n  DASHBOARD: \"/dashboard\",\r\n  HOME: \"/\",\r\n  BUY_SUBSCRIPTION: \"/buy-subscription\",\r\n  PROFILE: {\r\n    MY_PROFILE: \"/my-profile\",\r\n  },\r\n  SUBSCRIPTIONS: {\r\n    SUCCESS: \"/subscriptions/success\",\r\n    CANCEL: \"/subscriptions/cancel\",\r\n  },\r\n  JOBS: {\r\n    CAREER_BASED_SKILLS: \"/career-based-skills\",\r\n    ROLE_BASED_SKILLS: \"/role-based-skills\",\r\n    CULTURE_BASED_SKILLS: \"/culture-based-skills\",\r\n    GENERATE_JOB: \"/generate-job\",\r\n    EDIT_SKILLS: \"/edit-skills\",\r\n    HIRING_TYPE: \"/hiring-type\",\r\n    JOB_EDITOR: \"/job-editor\",\r\n    ACTIVE_JOBS: \"/active-jobs\",\r\n    CANDIDATE_PROFILE: \"/candidate-profile\",\r\n    ARCHIVE: \"/archive\",\r\n  },\r\n  SCREEN_RESUME: {\r\n    MANUAL_CANDIDATE_UPLOAD: \"/manual-upload-resume\",\r\n    CANDIDATE_QUALIFICATION: \"/candidate-qualification\",\r\n    CANDIDATE_LIST: \"/candidates-list\",\r\n    CANDIDATES: \"/candidates\",\r\n  },\r\n  INTERVIEW: {\r\n    ADD_CANDIDATE_INFO: \"/additional-submission\",\r\n    SCHEDULE_INTERVIEW: \"/schedule-interview\",\r\n    PRE_INTERVIEW_QUESTIONS_OVERVIEW: \"/pre-interview-questions-overview\",\r\n    INTERVIEW_QUESTION: \"/interview-question\",\r\n    CALENDAR: \"/calendar\",\r\n    INTERVIEW_SUMMARY: \"/interview-summary\",\r\n  },\r\n\r\n  ROLE_EMPLOYEES: {\r\n    ROLES_PERMISSIONS: \"/roles-permissions\",\r\n    EMPLOYEE_MANAGEMENT: \"/employee-management\",\r\n    EMPLOYEE_MANAGEMENT_DETAIL: \"/employee-management-detail\",\r\n    ADD_EMPLOYEE: \"/add-employees\",\r\n    ADD_DEPARTMENT: \"/add-department\",\r\n  },\r\n\r\n  FINAL_ASSESSMENT: {\r\n    FINAL_ASSESSMENT: \"/final-assessment\",\r\n  },\r\n};\r\n\r\nexport const BEFORE_LOGIN_ROUTES = [ROUTES.LOGIN, ROUTES.FORGOT_PASSWORD, ROUTES.VERIFY, ROUTES.RESET_PASSWORD, ROUTES.CANDIDATE_ASSESSMENT];\r\n\r\n// Routes that don't require permission checks for authenticated users\r\nexport const UNRESTRICTED_ROUTES = [ROUTES.SUBSCRIPTIONS.SUCCESS, ROUTES.SUBSCRIPTIONS.CANCEL];\r\n\r\nexport default ROUTES;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,SAAS;IACb,OAAO;IACP,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,sBAAsB;IACtB,WAAW;IACX,MAAM;IACN,kBAAkB;IAClB,SAAS;QACP,YAAY;IACd;IACA,eAAe;QACb,SAAS;QACT,QAAQ;IACV;IACA,MAAM;QACJ,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,mBAAmB;QACnB,SAAS;IACX;IACA,eAAe;QACb,yBAAyB;QACzB,yBAAyB;QACzB,gBAAgB;QAChB,YAAY;IACd;IACA,WAAW;QACT,oBAAoB;QACpB,oBAAoB;QACpB,kCAAkC;QAClC,oBAAoB;QACpB,UAAU;QACV,mBAAmB;IACrB;IAEA,gBAAgB;QACd,mBAAmB;QACnB,qBAAqB;QACrB,4BAA4B;QAC5B,cAAc;QACd,gBAAgB;IAClB;IAEA,kBAAkB;QAChB,kBAAkB;IACpB;AACF;AAEO,MAAM,sBAAsB;IAAC,OAAO,KAAK;IAAE,OAAO,eAAe;IAAE,OAAO,MAAM;IAAE,OAAO,cAAc;IAAE,OAAO,oBAAoB;CAAC;AAGrI,MAAM,sBAAsB;IAAC,OAAO,aAAa,CAAC,OAAO;IAAE,OAAO,aAAa,CAAC,MAAM;CAAC;uCAE/E", "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1842, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/EmployeeManagementIcon.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\n\r\nconst EmployeeManagementIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      style={{ minWidth: \"20px\" }}\r\n      width=\"20\"\r\n      height=\"20\"\r\n      id=\"Layer_1\"\r\n      viewBox=\"0 0 512 512\"\r\n      data-name=\"Layer 1\"\r\n    >\r\n      <path d=\"m491.181 367.517c-.803 1.939-2.082 3.646-3.718 4.96-7.396 5.943-23.179 14.709-35.54 20.76-.106.052-.213.103-.32.151l-161.875 73.625c-27.64 12.427-47.71 12.013-63.835 11.684-3.166-.065-6.157-.125-8.992-.083-6.33.098-12.055-.025-17.592-.146-26.562-.575-44.114-.953-78.805 27.231-2.317 1.882-5.105 2.799-7.875 2.799-3.635 0-7.238-1.577-9.709-4.618-4.353-5.358-3.539-13.231 1.819-17.584 41.816-33.973 66.512-33.438 95.109-32.822 5.31.115 10.799.234 16.666.143 3.287-.05 6.495.016 9.893.085 15.026.31 30.566.628 53.02-9.468l161.664-73.529c10.754-5.272 21.866-11.54 27.952-15.698.547-2.366.594-5.116-1.231-6.975-3.802-3.872-18.242-7.278-48.243 4.517-31.946 12.559-74.343 26.34-98.621 32.056-.07.016-.139.023-.209.038-1.226 1.811-2.617 3.538-4.191 5.16-20.602 21.24-65.264 21.16-105.511 19.42-3.906-.169-7.28-.315-9.934-.372-6.902-.146-12.378-5.861-12.231-12.763.146-6.902 5.858-12.404 12.763-12.231 2.929.062 6.43.213 10.483.389 21.537.931 71.973 3.113 86.486-11.85 2.658-2.74 3.7-5.898 3.38-10.241-.549-7.426-8.471-11.263-23.547-11.404-37.27-.349-70.54-3.121-112.094-7.483-52.693-3.541-95.592 43.946-126.936 78.622-2.467 2.73-5.866 4.118-9.277 4.118-2.988 0-5.986-1.065-8.378-3.227-5.122-4.629-5.521-12.534-.891-17.655 19.78-21.883 39.447-42.893 62.529-59.403 28.537-20.413 56.311-29.37 84.9-27.38.146.01.292.023.438.038 40.929 4.299 73.597 7.031 109.943 7.371 37.698.353 46.145 20.333 47.903 31.611 23.775-6.478 57.421-17.692 83.85-28.081 46.461-18.266 67.074-7.067 75.226 1.233 9.187 9.354 11.255 23.187 5.533 37zm-385.432-70.062c1.953-21.677 11.885-41.753 27.966-56.53 6.8-6.249 14.463-11.3 22.685-15.078-7.292-8.453-11.717-19.445-11.717-31.457 0-26.596 21.637-48.233 48.233-48.233s48.233 21.637 48.233 48.233c0 12.013-4.425 23.005-11.717 31.457 8.222 3.777 15.885 8.829 22.685 15.078 16.081 14.776 26.013 34.853 27.966 56.53.619 6.875-4.452 12.952-11.328 13.571-.381.034-.761.051-1.136.051-6.397 0-11.85-4.884-12.435-11.379-2.919-32.401-29.688-56.834-62.269-56.834s-59.349 24.433-62.268 56.834c-.62 6.876-6.706 11.941-13.571 11.328-6.875-.62-11.947-6.696-11.328-13.571zm63.935-103.065c0 12.811 10.422 23.232 23.233 23.232s23.233-10.422 23.233-23.232-10.422-23.233-23.233-23.233-23.233 10.422-23.233 23.233zm204.759-48.233c26.596 0 48.233 21.637 48.233 48.233 0 12.013-4.425 23.005-11.716 31.457 8.222 3.777 15.885 8.829 22.685 15.078 16.082 14.776 26.013 34.853 27.966 56.53.619 6.875-4.453 12.952-11.328 13.571-6.867.619-12.952-4.453-13.571-11.328-2.919-32.401-29.688-56.834-62.269-56.834s-59.349 24.434-62.269 56.834c-.585 6.494-6.039 11.379-12.435 11.379-.376 0-.754-.017-1.136-.051-6.876-.62-11.947-6.696-11.328-13.571 1.954-21.677 11.885-41.753 27.966-56.53 6.8-6.249 14.463-11.301 22.685-15.078-7.292-8.453-11.716-19.445-11.716-31.457 0-26.596 21.637-48.233 48.233-48.233zm-23.233 48.233c0 12.811 10.422 23.232 23.233 23.232s23.233-10.422 23.233-23.232-10.422-23.233-23.233-23.233-23.233 10.422-23.233 23.233zm-146.485-69.457c8.956-18.679 24.228-33.263 42.493-41.68-7.324-8.461-11.771-19.478-11.771-31.52 0-26.596 21.637-48.233 48.233-48.233s48.232 21.637 48.232 48.233c0 12.043-4.447 23.06-11.771 31.52 18.265 8.417 33.538 23.001 42.494 41.68 2.984 6.225.358 13.691-5.867 16.676-1.743.835-3.583 1.231-5.396 1.231-4.66 0-9.131-2.617-11.28-7.099-10.35-21.586-32.493-35.535-56.413-35.535s-46.062 13.948-56.412 35.535c-2.984 6.225-10.451 8.851-16.676 5.867-6.225-2.985-8.852-10.451-5.867-16.676zm55.722-73.201c0 12.811 10.422 23.232 23.233 23.232s23.232-10.422 23.232-23.232-10.422-23.233-23.232-23.233-23.233 10.422-23.233 23.233z\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default EmployeeManagementIcon;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,yBAAyB;IAC7B,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;YAAE,UAAU;QAAO;QAC1B,OAAM;QACN,QAAO;QACP,IAAG;QACH,SAAQ;QACR,aAAU;kBAEV,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;AAGd;KAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 1879, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/RolesIcon.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\n\r\nconst RolesIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      clipRule=\"evenodd\"\r\n      fillRule=\"evenodd\"\r\n      height=\"20\"\r\n      width=\"20\"\r\n      style={{ minWidth: \"20px\" }}\r\n      strokeLinejoin=\"round\"\r\n      strokeMiterlimit=\"2\"\r\n      viewBox=\"0 0 24 24\"\r\n    >\r\n      <g id=\"Icon\">\r\n        <path d=\"m11.5 20.263h-8.55c-.053 0-.104-.022-.141-.059-.038-.038-.059-.088-.059-.141 0-.001 0-1.451 0-1.451 0-.83.593-1.562 1.507-2.184 1.632-1.114 4.273-1.816 7.243-1.816.414 0 .75-.336.75-.75 0-.413-.336-.75-.75-.75-3.322 0-6.263.831-8.089 2.076-1.393.95-2.161 2.157-2.161 3.424v1.451c0 .45.179.883.498 1.202.319.318.751.498 1.202.498h8.55c.414-.001.75-.337.75-.751 0-.413-.336-.75-.75-.749z\" />\r\n        <path d=\"m11.5 1.25c-3.036 0-5.5 2.464-5.5 5.5s2.464 5.5 5.5 5.5 5.5-2.464 5.5-5.5-2.464-5.5-5.5-5.5zm0 1.5c2.208 0 4 1.792 4 4s-1.792 4-4 4-4-1.792-4-4 1.792-4 4-4z\" />\r\n        <path d=\"m17.5 13.938c-1.966 0-3.562 1.596-3.562 3.562s1.596 3.563 3.562 3.563 3.563-1.597 3.563-3.563-1.597-3.562-3.563-3.562zm0 1.5c1.138 0 2.063.924 2.063 2.062s-.925 2.063-2.063 2.063-2.063-.925-2.063-2.063.925-2.062 2.063-2.062z\" />\r\n        <path d=\"m18.25 14.687v-1.687c0-.414-.336-.75-.75-.75s-.75.336-.75.75v1.688c0 .413.336.75.75.75.414-.001.75-.337.75-.751z\" />\r\n        <path d=\"m20.019 16.042 1.193-1.194c.293-.292.293-.768 0-1.06-.292-.293-.768-.293-1.06 0l-1.194 1.193c-.292.293-.292.768 0 1.061.293.292.768.292 1.061 0z\" />\r\n        <path d=\"m20.312 18.25h1.688c.414 0 .75-.336.75-.75s-.336-.75-.75-.75h-1.688c-.413 0-.749.336-.749.75-.001.414.336.75.749.75z\" />\r\n        <path d=\"m18.958 20.019 1.194 1.193c.292.293.768.293 1.06 0 .293-.292.293-.768 0-1.06l-1.193-1.194c-.293-.292-.768-.292-1.061 0-.292.293-.292.768 0 1.061z\" />\r\n        <path d=\"m16.75 20.312v1.688c0 .414.336.75.75.75s.75-.336.75-.75v-1.688c0-.413-.336-.749-.75-.75-.414 0-.75.337-.75.75z\" />\r\n        <path d=\"m14.981 18.958-1.193 1.194c-.293.292-.293.768 0 1.06.292.293.768.293 1.06 0l1.194-1.193c.292-.293.292-.768 0-1.061-.293-.292-.768-.292-1.061 0z\" />\r\n        <path d=\"m14.687 16.75h-1.687c-.414 0-.75.336-.75.75s.336.75.75.75h1.687c.414 0 .751-.336.75-.75 0-.414-.336-.75-.75-.75z\" />\r\n        <path d=\"m16.042 14.981-1.194-1.193c-.292-.293-.768-.293-1.06 0-.293.292-.293.768 0 1.06l1.193 1.194c.293.292.768.292 1.061 0 .292-.293.292-.768 0-1.061z\" />\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default RolesIcon;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,YAAY;IAChB,qBACE,6LAAC;QACC,OAAM;QACN,UAAS;QACT,UAAS;QACT,QAAO;QACP,OAAM;QACN,OAAO;YAAE,UAAU;QAAO;QAC1B,gBAAe;QACf,kBAAiB;QACjB,SAAQ;kBAER,cAAA,6LAAC;YAAE,IAAG;;8BACJ,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;8BACR,6LAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;AAIhB;KA5BM;uCA8BS", "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ProfileIcon.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\n\r\nconst ProfileIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n      version=\"1.1\"\r\n      id=\"Layer_1\"\r\n      x=\"0px\"\r\n      y=\"0px\"\r\n      viewBox=\"0 0 512 512\"\r\n      xmlSpace=\"preserve\"\r\n      style={{ minWidth: \"20px\" }}\r\n      width=\"18\"\r\n      height=\"18\"\r\n    >\r\n      <g>\r\n        <g>\r\n          <path d=\"M256,0c-74.439,0-135,60.561-135,135s60.561,135,135,135s135-60.561,135-135S330.439,0,256,0z M256,240 c-57.897,0-105-47.103-105-105c0-57.897,47.103-105,105-105c57.897,0,105,47.103,105,105C361,192.897,313.897,240,256,240z\" />\r\n        </g>\r\n      </g>\r\n      <g>\r\n        <g>\r\n          <path d=\"M423.966,358.195C387.006,320.667,338.009,300,286,300h-60c-52.008,0-101.006,20.667-137.966,58.195 C51.255,395.539,31,444.833,31,497c0,8.284,6.716,15,15,15h420c8.284,0,15-6.716,15-15    C481,444.833,460.745,395.539,423.966,358.195z M61.66,482c7.515-85.086,78.351-152,164.34-152h60    c85.989,0,156.825,66.914,164.34,152H61.66z\" />\r\n        </g>\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default ProfileIcon;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,cAAc;IAClB,qBACE,6LAAC;QACC,OAAM;QACN,YAAW;QACX,SAAQ;QACR,IAAG;QACH,GAAE;QACF,GAAE;QACF,SAAQ;QACR,UAAS;QACT,OAAO;YAAE,UAAU;QAAO;QAC1B,OAAM;QACN,QAAO;;0BAEP,6LAAC;0BACC,cAAA,6LAAC;8BACC,cAAA,6LAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;0BAGZ,6LAAC;0BACC,cAAA,6LAAC;8BACC,cAAA,6LAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;AAKlB;KA3BM;uCA6BS", "debugId": null}}, {"offset": {"line": 2083, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2089, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/LogoutIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction LogoutIcon({ className }: { className?: string }) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={className}\r\n      style={{ minWidth: \"20px\", fill: \"#fff\" }}\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 32 33\"\r\n      fill=\"none\"\r\n    >\r\n      <g clipPath=\"url(#clip0_10037_3401)\">\r\n        <path\r\n          d=\"M18.6667 11.2415V8.57487C18.6667 7.86763 18.3857 7.18935 17.8856 6.68925C17.3855 6.18915 16.7072 5.9082 16 5.9082H6.66667C5.95942 5.9082 5.28115 6.18915 4.78105 6.68925C4.28095 7.18935 4 7.86763 4 8.57487V24.5749C4 25.2821 4.28095 25.9604 4.78105 26.4605C5.28115 26.9606 5.95942 27.2415 6.66667 27.2415H16C16.7072 27.2415 17.3855 26.9606 17.8856 26.4605C18.3857 25.9604 18.6667 25.2821 18.6667 24.5749V21.9082\"\r\n          strokeWidth=\"1.5\"\r\n          strokeLinecap=\"round\"\r\n          strokeLinejoin=\"round\"\r\n        />\r\n        <path d=\"M12 16.5762H28L24 12.5762\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n        <path d=\"M24 20.5762L28 16.5762\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_10037_3401\">\r\n          <rect width=\"32\" height=\"32\" fill=\"white\" transform=\"translate(0 0.271484)\" />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport default LogoutIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,6LAAC;QACC,OAAM;QACN,WAAW;QACX,OAAO;YAAE,UAAU;YAAQ,MAAM;QAAO;QACxC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;;0BAEL,6LAAC;gBAAE,UAAS;;kCACV,6LAAC;wBACC,GAAE;wBACF,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBAAK,GAAE;wBAA4B,aAAY;wBAAM,eAAc;wBAAQ,gBAAe;;;;;;kCAC3F,6LAAC;wBAAK,GAAE;wBAAyB,aAAY;wBAAM,eAAc;wBAAQ,gBAAe;;;;;;;;;;;;0BAE1F,6LAAC;0BACC,cAAA,6LAAC;oBAAS,IAAG;8BACX,cAAA,6LAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;KA5BS;uCA8BM", "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2189, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/loader/Loader.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst Loader = ({ className }: { className?: string }) => {\r\n  const t = useTranslations();\r\n  return (\r\n    <div className={`spinner-border ${className}`} role=\"status\">\r\n      <span className=\"visually-hidden\">{t(\"loading\")}</span>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loader;\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,SAAS,CAAC,EAAE,SAAS,EAA0B;;IACnD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,qBACE,6LAAC;QAAI,WAAW,CAAC,eAAe,EAAE,WAAW;QAAE,MAAK;kBAClD,cAAA,6LAAC;YAAK,WAAU;sBAAmB,EAAE;;;;;;;;;;;AAG3C;GAPM;;QACM,yMAAA,CAAA,kBAAe;;;KADrB;uCASS", "debugId": null}}, {"offset": {"line": 2230, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Button.tsx"], "sourcesContent": ["import React, { ButtonHTMLAttributes, DetailedHTMLP<PERSON>, MouseEvent, ReactNode } from \"react\";\r\nimport Loader from \"@/components/loader/Loader\";\r\n\r\ninterface Props extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {\r\n  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;\r\n  disabled?: boolean;\r\n  children?: ReactNode;\r\n  loading?: boolean;\r\n}\r\n\r\nconst Button = ({ className, type, disabled = false, onClick, children, loading }: Props) => (\r\n  <button\r\n    type={type}\r\n    className={`theme-btn ${className}`}\r\n    onClick={(e) => {\r\n      if (onClick) {\r\n        onClick(e as unknown as MouseEvent<HTMLButtonElement>);\r\n      }\r\n    }}\r\n    disabled={disabled}\r\n    aria-label=\"\"\r\n  >\r\n    {children}\r\n    {loading ? <Loader /> : null}\r\n  </button>\r\n);\r\n\r\nexport default Button;\r\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAS,iBACtF,6LAAC;QACC,MAAM;QACN,WAAW,CAAC,UAAU,EAAE,WAAW;QACnC,SAAS,CAAC;YACR,IAAI,SAAS;gBACX,QAAQ;YACV;QACF;QACA,UAAU;QACV,cAAW;;YAEV;YACA,wBAAU,6LAAC,yIAAA,CAAA,UAAM;;;;uBAAM;;;;;;;KAbtB;uCAiBS", "debugId": null}}, {"offset": {"line": 2273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2279, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/notificationServices/notificationService.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\r\nimport * as http from \"@/utils/http\";\r\nimport { IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\r\nimport { NotificationItem } from \"@/interfaces/notificationInterface\";\r\n\r\nexport const getNotifications = (data: { offset: number; limit: number }): Promise<IApiResponseCommonInterface<NotificationItem[]>> => {\r\n  return http.get(endpoint.notification.GET_NOTIFICATIONS, data);\r\n};\r\n\r\nexport const deleteAllNotifications = (): Promise<IApiResponseCommonInterface<unknown>> => {\r\n  return http.remove(endpoint.notification.DELETE_ALL_NOTIFICATIONS);\r\n};\r\n\r\nexport const updateNotificationStatus = (): Promise<IApiResponseCommonInterface<unknown>> => {\r\n  return http.post(endpoint.notification.UPDATE_NOTIFICATION, {});\r\n};\r\n\r\nexport const getUnreadNotificationsCount = (): Promise<IApiResponseCommonInterface<{ count: number }>> => {\r\n  return http.get(endpoint.notification.GET_UNREAD_NOTIFICATIONS_COUNT);\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAIO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE;AAC3D;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,SAAW,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,wBAAwB;AACnE;AAEO,MAAM,2BAA2B;IACtC,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;AAC/D;AAEO,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,8BAA8B;AACtE", "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2310, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/notification/Notifications.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport Button from \"@/components/formElements/Button\";\r\nimport { DEFAULT_LIMIT } from \"@/constants/commonConstants\";\r\nimport { setHasUnreadNotification, setNotificationsData } from \"@/redux/slices/notificationSlice\";\r\nimport { getNotifications, deleteAllNotifications, updateNotificationStatus } from \"@/services/notificationServices/notificationService\";\r\nimport { toastMessageError } from \"@/utils/helper\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport InfiniteScroll from \"react-infinite-scroll-component\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/redux/store\";\r\n\r\ninterface NotificationsProps {\r\n  setIsNotificationOpen: (value: boolean) => void;\r\n}\r\n\r\nconst Notifications = ({ setIsNotificationOpen }: NotificationsProps) => {\r\n  const t = useTranslations();\r\n  const tCommon = useTranslations(\"common\");\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [offset, setOffset] = useState<number>(0);\r\n  const [hasMore, setHasMore] = useState<boolean>(true);\r\n  const notificationRef = useRef<HTMLDivElement>(null);\r\n  const dispatch = useDispatch();\r\n  const { notifications } = useSelector((state: RootState) => state.notification);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        notificationRef.current &&\r\n        !notificationRef.current.contains(event.target as Node) &&\r\n        (event.target as HTMLElement).id !== \"notification-icon-id\"\r\n      ) {\r\n        setIsNotificationOpen(false);\r\n        dispatch(setHasUnreadNotification(false));\r\n        dispatch(setNotificationsData(notifications.map((item) => ({ ...item, isWatched: 1 }))));\r\n        updateNotificationStatus();\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    dispatch(setNotificationsData([]));\r\n    fetchNotifications(offset);\r\n  }, []);\r\n\r\n  const fetchNotifications = async (offset: number) => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await getNotifications({ offset, limit: DEFAULT_LIMIT });\r\n\r\n      if (response.data?.success) {\r\n        console.log(\"Notifications fetched successfully:\", response.data.data);\r\n\r\n        dispatch(setNotificationsData([...notifications, ...response.data.data]));\r\n        setOffset((prevOffset) => prevOffset + DEFAULT_LIMIT);\r\n        setHasMore(response.data.data.length === DEFAULT_LIMIT);\r\n      } else {\r\n        toastMessageError(t(response?.data?.message));\r\n      }\r\n    } catch (error) {\r\n      console.log(error);\r\n\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleClearAll = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const res = await deleteAllNotifications();\r\n\r\n      if (res.data?.success) {\r\n        dispatch(setNotificationsData([])); // Clear UI\r\n      } else {\r\n        toastMessageError(t(\"failed_to_delete_notifications\"));\r\n      }\r\n    } catch {\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"notifications\" ref={notificationRef}>\r\n      <div className=\"header-content\">\r\n        <h3>{tCommon(\"notifications\")}</h3>\r\n        <Button onClick={notifications.length === 0 ? handleClearAll : undefined} className=\"clear-btn p-0\">\r\n          {tCommon(\"clear_all\")}\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"read-btns\">{/* Future filter buttons */}</div>\r\n\r\n      <div className=\"notification-wrapper\" id=\"notification-scroll-container\">\r\n        <InfiniteScroll\r\n          dataLength={notifications.length}\r\n          next={() => fetchNotifications(offset)}\r\n          hasMore={hasMore}\r\n          loader={<p style={{ padding: \"1rem\" }}>Loading...</p>}\r\n          scrollableTarget=\"notification-scroll-container\"\r\n          endMessage={\r\n            notifications.length > 0 && (\r\n              <p style={{ padding: \"1rem\", textAlign: \"center\" }}>\r\n                <b>{tCommon(\"no_more_notifications\")}</b>\r\n              </p>\r\n            )\r\n          }\r\n        >\r\n          {notifications.length === 0 && !loading ? (\r\n            <p style={{ padding: \"1rem\" }}>{tCommon(\"no_notifications_found\")}</p>\r\n          ) : (\r\n            notifications.map((item) => (\r\n              <div key={item.id} className={`notification-item ${item.isWatched === 0 ? \"unread\" : \"\"}`}>\r\n                <h4 style={{ margin: 0 }}>{item.title}</h4>\r\n                <p>{item.description}</p>\r\n                <p className=\"time\">\r\n                  {new Date(item.createdTs).toLocaleString(\"en-IN\", {\r\n                    dateStyle: \"medium\",\r\n                    timeStyle: \"short\",\r\n                  })}\r\n                </p>\r\n              </div>\r\n            ))\r\n          )}\r\n        </InfiniteScroll>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default React.memo(Notifications);\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAOA,MAAM,gBAAgB,CAAC,EAAE,qBAAqB,EAAsB;;IAClE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAqB,MAAM,YAAY;;IAE9E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;8DAAqB,CAAC;oBAC1B,IACE,gBAAgB,OAAO,IACvB,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC9C,AAAC,MAAM,MAAM,CAAiB,EAAE,KAAK,wBACrC;wBACA,sBAAsB;wBACtB,SAAS,CAAA,GAAA,8IAAA,CAAA,2BAAwB,AAAD,EAAE;wBAClC,SAAS,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc,GAAG;0EAAC,CAAC,OAAS,CAAC;oCAAE,GAAG,IAAI;oCAAE,WAAW;gCAAE,CAAC;;wBACpF,CAAA,GAAA,iKAAA,CAAA,2BAAwB,AAAD;oBACzB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE,EAAE;YAChC,mBAAmB;QACrB;kCAAG,EAAE;IAEL,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE;gBAAQ,OAAO,sIAAA,CAAA,gBAAa;YAAC;YAEvE,IAAI,SAAS,IAAI,EAAE,SAAS;gBAC1B,QAAQ,GAAG,CAAC,uCAAuC,SAAS,IAAI,CAAC,IAAI;gBAErE,SAAS,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE;uBAAI;uBAAkB,SAAS,IAAI,CAAC,IAAI;iBAAC;gBACvE,UAAU,CAAC,aAAe,aAAa,sIAAA,CAAA,gBAAa;gBACpD,WAAW,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,sIAAA,CAAA,gBAAa;YACxD,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,MAAM,MAAM,CAAA,GAAA,iKAAA,CAAA,yBAAsB,AAAD;YAEvC,IAAI,IAAI,IAAI,EAAE,SAAS;gBACrB,SAAS,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE,EAAE,IAAI,WAAW;YACjD,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACtB;QACF,EAAE,OAAM;YACN,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAgB,KAAK;;0BAClC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAI,QAAQ;;;;;;kCACb,6LAAC,+IAAA,CAAA,UAAM;wBAAC,SAAS,cAAc,MAAM,KAAK,IAAI,iBAAiB;wBAAW,WAAU;kCACjF,QAAQ;;;;;;;;;;;;0BAIb,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;gBAAuB,IAAG;0BACvC,cAAA,6LAAC,kLAAA,CAAA,UAAc;oBACb,YAAY,cAAc,MAAM;oBAChC,MAAM,IAAM,mBAAmB;oBAC/B,SAAS;oBACT,sBAAQ,6LAAC;wBAAE,OAAO;4BAAE,SAAS;wBAAO;kCAAG;;;;;;oBACvC,kBAAiB;oBACjB,YACE,cAAc,MAAM,GAAG,mBACrB,6LAAC;wBAAE,OAAO;4BAAE,SAAS;4BAAQ,WAAW;wBAAS;kCAC/C,cAAA,6LAAC;sCAAG,QAAQ;;;;;;;;;;;8BAKjB,cAAc,MAAM,KAAK,KAAK,CAAC,wBAC9B,6LAAC;wBAAE,OAAO;4BAAE,SAAS;wBAAO;kCAAI,QAAQ;;;;;+BAExC,cAAc,GAAG,CAAC,CAAC,qBACjB,6LAAC;4BAAkB,WAAW,CAAC,kBAAkB,EAAE,KAAK,SAAS,KAAK,IAAI,WAAW,IAAI;;8CACvF,6LAAC;oCAAG,OAAO;wCAAE,QAAQ;oCAAE;8CAAI,KAAK,KAAK;;;;;;8CACrC,6LAAC;8CAAG,KAAK,WAAW;;;;;;8CACpB,6LAAC;oCAAE,WAAU;8CACV,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS;wCAChD,WAAW;wCACX,WAAW;oCACb;;;;;;;2BAPM,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAgB/B;GAzHM;;QACM,yMAAA,CAAA,kBAAe;QACT,yMAAA,CAAA,kBAAe;QAKd,4JAAA,CAAA,cAAW;QACF,4JAAA,CAAA,cAAW;;;KARjC;2DA2HS,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2567, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavCalendarIcon.tsx"], "sourcesContent": ["const NavCalendarIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 25 27\" fill=\"none\">\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        style={{ width: \"24px\", height: \"22px\" }}\r\n        d=\"M23.2117 11.406H1.1439C0.631332 11.406 0.215332 10.99 0.215332 10.4774C0.215332 9.96483 0.631332 9.54883 1.1439 9.54883H23.2117C23.7243 9.54883 24.1403 9.96483 24.1403 10.4774C24.1403 10.99 23.7243 11.406 23.2117 11.406Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M17.6835 16.24C17.171 16.24 16.75 15.824 16.75 15.3114C16.75 14.7988 17.1598 14.3828 17.6724 14.3828H17.6835C18.1961 14.3828 18.6121 14.7988 18.6121 15.3114C18.6121 15.824 18.1961 16.24 17.6835 16.24Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M12.1899 16.24C11.6773 16.24 11.2563 15.824 11.2563 15.3114C11.2563 14.7988 11.6662 14.3828 12.1787 14.3828H12.1899C12.7024 14.3828 13.1184 14.7988 13.1184 15.3114C13.1184 15.824 12.7024 16.24 12.1899 16.24Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M6.68427 16.24C6.1717 16.24 5.74951 15.824 5.74951 15.3114C5.74951 14.7988 6.16056 14.3828 6.67313 14.3828H6.68427C7.19685 14.3828 7.61285 14.7988 7.61285 15.3114C7.61285 15.824 7.19685 16.24 6.68427 16.24Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M17.6835 21.0525C17.171 21.0525 16.75 20.6365 16.75 20.1239C16.75 19.6113 17.1598 19.1953 17.6724 19.1953H17.6835C18.1961 19.1953 18.6121 19.6113 18.6121 20.1239C18.6121 20.6365 18.1961 21.0525 17.6835 21.0525Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M12.1899 21.0525C11.6773 21.0525 11.2563 20.6365 11.2563 20.1239C11.2563 19.6113 11.6662 19.1953 12.1787 19.1953H12.1899C12.7024 19.1953 13.1184 19.6113 13.1184 20.1239C13.1184 20.6365 12.7024 21.0525 12.1899 21.0525Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M6.68427 21.0525C6.1717 21.0525 5.74951 20.6365 5.74951 20.1239C5.74951 19.6113 6.16056 19.1953 6.67313 19.1953H6.68427C7.19685 19.1953 7.61285 19.6113 7.61285 20.1239C7.61285 20.6365 7.19685 21.0525 6.68427 21.0525Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M17.1786 6.31257C16.666 6.31257 16.25 5.89657 16.25 5.384V1.30943C16.25 0.796859 16.666 0.380859 17.1786 0.380859C17.6911 0.380859 18.1071 0.796859 18.1071 1.30943V5.384C18.1071 5.89657 17.6911 6.31257 17.1786 6.31257Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.17711 6.31257C6.66454 6.31257 6.24854 5.89657 6.24854 5.384V1.30943C6.24854 0.796859 6.66454 0.380859 7.17711 0.380859C7.68968 0.380859 8.10568 0.796859 8.10568 1.30943V5.384C8.10568 5.89657 7.68968 6.31257 7.17711 6.31257Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <mask id=\"mask0_15282_6458\" style={{ maskType: \"luminance\" }} maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"2\" width=\"25\" height=\"25\">\r\n        <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M0.101074 2.33594H24.2439V26.9999H0.101074V2.33594Z\" fill=\"white\" />\r\n      </mask>\r\n      <g>\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M6.93671 4.19289C3.72633 4.19289 1.95833 5.90518 1.95833 9.01404V20.2176C1.95833 23.3945 3.72633 25.1427 6.93671 25.1427H17.4085C20.6189 25.1427 22.3869 23.4267 22.3869 20.3117V9.01404C22.3919 7.48499 21.9808 6.29642 21.1649 5.47928C20.3255 4.63737 19.0317 4.19289 17.4197 4.19289H6.93671ZM17.4084 27H6.9366C2.72088 27 0.101074 24.4013 0.101074 20.2177V9.01422C0.101074 4.89384 2.72088 2.33594 6.9366 2.33594H17.4196C19.5355 2.33594 21.2849 2.96984 22.4796 4.16708C23.6397 5.33213 24.2501 7.00727 24.2439 9.0167V20.3118C24.2439 24.4372 21.6241 27 17.4084 27Z\"\r\n          // fill=\"#333333\"\r\n        />\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default NavCalendarIcon;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;gBACvC,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBAAK,IAAG;gBAAmB,OAAO;oBAAE,UAAU;gBAAY;gBAAG,WAAU;gBAAiB,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;0BACrH,cAAA,6LAAC;oBAAK,UAAS;oBAAU,UAAS;oBAAU,GAAE;oBAAsD,MAAK;;;;;;;;;;;0BAE3G,6LAAC;0BACC,cAAA,6LAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;;;;;;;;;;;;;;;;;AAMZ;KAvEM;uCAyES", "debugId": null}}, {"offset": {"line": 2723, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavCandidatesIcon.tsx"], "sourcesContent": ["const NavCandidatesIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 21\" fill=\"none\">\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        style={{ width: \"24px\", height: \"25px\" }}\r\n        d=\"M13.5942 11.4094H13.6276C16.7476 11.4094 19.2845 8.87132 19.2845 5.75256C19.2845 2.6338 16.7476 0.0957031 13.6276 0.0957031C10.5089 0.0957031 7.97201 2.6338 7.97201 5.74885C7.96582 7.25561 8.54648 8.67447 9.60877 9.74294C10.6686 10.8127 12.0837 11.4045 13.5942 11.4094ZM9.82915 5.75256C9.82915 3.6577 11.5328 1.95285 13.6276 1.95285C15.7225 1.95285 17.4273 3.6577 17.4273 5.75256C17.4273 7.84866 15.7225 9.55227 13.6276 9.55227H13.5967C12.5864 9.54856 11.638 9.15113 10.9261 8.43427C10.2142 7.71742 9.82544 6.7678 9.82915 5.75256Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        d=\"M20.1376 9.38204C20.2032 9.84633 20.6007 10.1819 21.0563 10.1819C21.0984 10.1819 21.1417 10.1794 21.185 10.1732C23.3827 9.8649 25.0429 7.95823 25.0479 5.73585C25.0479 3.52833 23.4668 1.66623 21.2903 1.30842C20.7814 1.22795 20.3072 1.56719 20.223 2.07357C20.1401 2.57995 20.483 3.05785 20.9882 3.14081C22.2647 3.35004 23.1908 4.44204 23.1908 5.73338C23.1883 7.03585 22.2164 8.15262 20.9288 8.33338C20.4199 8.40395 20.067 8.87319 20.1376 9.38204Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        d=\"M23.8712 17.4887C24.0123 17.8588 24.3652 18.0867 24.7391 18.0867C24.8493 18.0867 24.9607 18.0668 25.0697 18.026C26.8402 17.3512 27.1051 16.1218 27.1051 15.4483C27.1051 14.3451 26.4749 12.9362 23.475 12.4868C22.9637 12.4211 22.4932 12.7591 22.4177 13.2668C22.3422 13.7756 22.6925 14.2473 23.1989 14.3241C24.5583 14.5271 25.248 14.906 25.248 15.4483C25.248 15.6179 25.248 15.9708 24.4085 16.2902C23.9294 16.4722 23.688 17.0095 23.8712 17.4887Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M13.6276 20.8845C11.5736 20.8845 5.32121 20.8845 5.32121 16.9288C5.32121 12.9892 11.5736 12.9892 13.6276 12.9892C15.6816 12.9892 21.9327 12.9892 21.9327 16.9498C21.9327 20.8845 15.8995 20.8845 13.6276 20.8845ZM13.6276 14.8463C10.6859 14.8463 7.17835 15.2078 7.17835 16.9288C7.17835 18.6633 10.6859 19.0273 13.6276 19.0273C16.5693 19.0273 20.0756 18.6671 20.0756 16.9498C20.0756 15.2115 16.5693 14.8463 13.6276 14.8463Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        d=\"M6.19776 10.1818C6.15567 10.1818 6.11233 10.1793 6.069 10.1731C3.87138 9.86483 2.21233 7.95817 2.20738 5.73826C2.20738 3.52826 3.78843 1.66617 5.965 1.30836C6.48624 1.22664 6.94805 1.56959 7.03224 2.0735C7.11519 2.57988 6.77224 3.05778 6.2671 3.14074C4.99062 3.34997 4.06452 4.44197 4.06452 5.73578C4.067 7.03578 5.0389 8.15378 6.32529 8.33331C6.83414 8.40388 7.187 8.87312 7.11643 9.38198C7.05081 9.84626 6.65338 10.1818 6.19776 10.1818Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n      <path\r\n        d=\"M2.18559 18.026C2.29454 18.0669 2.40597 18.0867 2.51616 18.0867C2.89007 18.0867 3.24293 17.8589 3.38407 17.4887C3.56731 17.0096 3.32588 16.4722 2.84674 16.2902C2.00607 15.9696 2.00607 15.6179 2.00607 15.4483C2.00607 14.906 2.69569 14.5272 4.05512 14.3241C4.5615 14.2474 4.91188 13.7757 4.83635 13.2668C4.75959 12.7592 4.29159 12.4224 3.78026 12.4868C0.779116 12.9362 0.148926 14.3464 0.148926 15.4483C0.148926 16.1206 0.413878 17.35 2.18559 18.026Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default NavCandidatesIcon;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,oBAAoB;IACxB,qBACE,6LAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;gBACvC,GAAE;;;;;;0BAGJ,6LAAC;gBACC,GAAE;;;;;;0BAGJ,6LAAC;gBACC,GAAE;;;;;;0BAGJ,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,6LAAC;gBACC,GAAE;;;;;;0BAGJ,6LAAC;gBACC,GAAE;;;;;;;;;;;;AAKV;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 2804, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2810, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavHomeIcon.tsx"], "sourcesContent": ["const NavHomeIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" style={{ width: \"24px\", height: \"24px\" }} viewBox=\"0 0 30 30\" fill=\"none\">\r\n      <g>\r\n        <path d=\"M24.0664 13.9443C24.0664 13.212 23.7285 12.5205 23.1504 12.0713H23.1494L16.5469 6.93555C16.1304 6.61158 15.6175 6.43555 15.0898 6.43555C14.5624 6.43565 14.0501 6.61168 13.6338 6.93555L7.0293 12.0713C6.74431 12.293 6.51407 12.577 6.35547 12.9014C6.19686 13.2258 6.11414 13.5822 6.11426 13.9434V22.8584C6.11426 23.2689 6.27715 23.6629 6.56738 23.9531C6.8576 24.2433 7.25173 24.4062 7.66211 24.4062H22.5186C22.9289 24.4062 23.3231 24.2433 23.6133 23.9531C23.9035 23.6629 24.0664 23.2689 24.0664 22.8584V13.9443ZM25.9238 22.8584C25.9238 23.7612 25.5651 24.6271 24.9268 25.2656C24.2882 25.9041 23.4216 26.2637 22.5186 26.2637H7.66211C6.75911 26.2637 5.89242 25.9041 5.25391 25.2656C4.6156 24.6271 4.25684 23.7612 4.25684 22.8584V13.9443C4.25657 13.3002 4.40368 12.6646 4.68652 12.0859C4.96946 11.5071 5.38108 11 5.88965 10.6045L12.4932 5.46973C13.2355 4.89223 14.1493 4.57823 15.0898 4.57812C16.0305 4.57813 16.945 4.89213 17.6875 5.46973L24.2891 10.6045C25.3204 11.4058 25.9238 12.6389 25.9238 13.9443V22.8584Z\" />\r\n        <path d=\"M9.30687 18.6483C9.57186 18.2093 10.1423 18.068 10.5813 18.3328C11.7705 19.0508 13.3869 19.4373 15.053 19.4373C16.7189 19.4373 18.3359 19.0509 19.5266 18.3328C19.9657 18.068 20.5371 18.2091 20.802 18.6483C21.0666 19.0872 20.9253 19.6577 20.4866 19.9227C18.9411 20.8549 16.9718 21.2938 15.053 21.2938C13.1344 21.2937 11.1666 20.8549 9.6223 19.9227C9.18337 19.6577 9.04207 19.0872 9.30687 18.6483Z\" />\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_15321_8934\">\r\n          <rect width=\"29.7143\" height=\"29.7143\" fill=\"white\" transform=\"translate(0.196289 0.142578)\" />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default NavHomeIcon;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAO;YAAE,OAAO;YAAQ,QAAQ;QAAO;QAAG,SAAQ;QAAY,MAAK;;0BACzG,6LAAC;;kCACC,6LAAC;wBAAK,GAAE;;;;;;kCACR,6LAAC;wBAAK,GAAE;;;;;;;;;;;;0BAEV,6LAAC;0BACC,cAAA,6LAAC;oBAAS,IAAG;8BACX,cAAA,6LAAC;wBAAK,OAAM;wBAAU,QAAO;wBAAU,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKxE;KAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 2884, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2890, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavJobsIcon.tsx"], "sourcesContent": ["const NavJobsIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" style={{ width: \"25px\", height: \"24px\" }} viewBox=\"0 0 31 30\" fill=\"none\">\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M9.9135 8.37664H5.06568C3.70607 8.37664 2.5918 9.50363 2.5918 10.9084V15.6188C2.5918 15.9587 2.82564 16.254 3.15658 16.3318L3.58889 16.4335V23.6685C3.58889 24.2863 3.83434 24.8788 4.27115 25.3156C4.70803 25.7525 5.30047 25.9979 5.91828 25.9979H24.7605C25.3783 25.9979 25.9708 25.7525 26.4076 25.3156C26.8445 24.8788 27.0899 24.2863 27.0899 23.6685V16.4335L27.5222 16.3318C27.8531 16.254 28.087 15.9587 28.087 15.6188V10.9084C28.087 9.50363 26.9727 8.37664 25.6131 8.37664H20.7653V6.90869C20.7653 5.30445 19.4648 4.00391 17.8605 4.00391H12.8183C11.214 4.00391 9.9135 5.30445 9.9135 6.90869V8.37664ZM18.6651 18.4142L25.6251 16.7779V23.6685C25.6251 23.8978 25.5339 24.1177 25.3718 24.2798C25.2097 24.4419 24.9898 24.533 24.7605 24.533H5.91828C5.689 24.533 5.4691 24.4419 5.30697 24.2798C5.14484 24.1177 5.05373 23.8978 5.05373 23.6685V16.7779L12.0137 18.4142V19.3379C12.0137 20.2357 12.7426 20.9646 13.6404 20.9646H17.0384C17.9362 20.9646 18.6651 20.2357 18.6651 19.3379V18.4142ZM17.2002 19.3379V17.5495C17.2002 17.4602 17.1277 17.3877 17.0384 17.3877H13.6404C13.5511 17.3877 13.4786 17.4602 13.4786 17.5495V19.3379C13.4786 19.4272 13.5511 19.4997 13.6404 19.4997H17.0384C17.1277 19.4997 17.2002 19.4272 17.2002 19.3379ZM18.5454 16.9375C18.3038 16.3426 17.7197 15.9229 17.0384 15.9229H13.6404C12.9591 15.9229 12.375 16.3426 12.1334 16.9375L4.05664 15.0386V10.9084C4.05664 10.3256 4.5016 9.84148 5.06568 9.84148H25.6131C26.1772 9.84148 26.6221 10.3256 26.6221 10.9084V15.0386L18.5454 16.9375ZM19.3004 8.37664H11.3783V6.90869C11.3783 6.11346 12.023 5.46875 12.8183 5.46875H17.8605C18.6558 5.46875 19.3004 6.11346 19.3004 6.90869V8.37664Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default NavJobsIcon;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAO;YAAE,OAAO;YAAQ,QAAQ;QAAO;QAAG,SAAQ;QAAY,MAAK;kBACzG,cAAA,6LAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;;;;;;;;;;;AAKV;KAXM;uCAaS", "debugId": null}}, {"offset": {"line": 2926, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2932, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavSettingsIcon.tsx"], "sourcesContent": ["const NavSettingsIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 31 30\" fill=\"none\">\r\n      <path\r\n        d=\"M26.7675 16.9907L25.7251 16.1326C25.011 15.5448 25.0123 14.4523 25.7251 13.8657L26.7675 13.0076C27.5092 12.3969 27.699 11.3497 27.2186 10.5174L25.1603 6.95242C24.6798 6.12025 23.6778 5.76104 22.7782 6.09803L21.5139 6.57172C20.6478 6.89609 19.7023 6.349 19.5506 5.43824L19.3286 4.1064C19.1707 3.15864 18.3586 2.4707 17.3977 2.4707H13.2812C12.3203 2.4707 11.5082 3.15864 11.3503 4.10645L11.1283 5.43824C10.9762 6.35061 10.0294 6.89555 9.16502 6.57177L7.90068 6.09803C7.00102 5.76104 5.99914 6.1203 5.5186 6.95242L3.46036 10.5174C2.97993 11.3495 3.16962 12.3968 3.91149 13.0075L4.95384 13.8656C5.66797 14.4535 5.66655 15.5459 4.95384 16.1326L3.91144 16.9907C3.16962 17.6015 2.97988 18.6487 3.46032 19.4808L5.5186 23.0459C5.99909 23.878 7.00078 24.2372 7.90068 23.9002L9.16497 23.4266C10.0312 23.102 10.9765 23.6495 11.1282 24.56L11.3502 25.8918C11.5082 26.8397 12.3203 27.5276 13.2812 27.5276H17.3977C18.3586 27.5276 19.1707 26.8397 19.3286 25.8919L19.5505 24.5601C19.7025 23.648 20.6493 23.1027 21.5138 23.4267L22.7782 23.9003C23.6781 24.2373 24.6798 23.8781 25.1603 23.0459L27.2186 19.4808C27.699 18.6487 27.5092 17.6015 26.7675 16.9907ZM23.465 22.0671L22.2006 21.5934C20.1795 20.8363 17.9738 22.1137 17.6197 24.2383L17.3977 25.5701H13.2812L13.0592 24.2383C12.7045 22.1096 10.4955 20.8378 8.47826 21.5934L7.21391 22.0671L5.15567 18.5021L6.19803 17.644C7.86436 16.2722 7.86089 13.7233 6.19803 12.3543L5.15567 11.4962L7.21396 7.9312L8.47826 8.40489C10.4995 9.16198 12.7051 7.88466 13.0592 5.76006L13.2812 4.42827H17.3977L17.6196 5.76006C17.9744 7.88902 20.1835 9.16041 22.2006 8.40489L23.4649 7.9312L25.5237 11.4957C25.5237 11.4957 25.5235 11.4959 25.5231 11.4962L24.4808 12.3543C22.8145 13.726 22.8179 16.275 24.4808 17.6439L25.5232 18.502L23.465 22.0671ZM15.3394 10.1705C12.6769 10.1705 10.5108 12.3366 10.5108 14.9992C10.5108 17.6617 12.6769 19.8279 15.3394 19.8279C18.002 19.8279 20.1681 17.6617 20.1681 14.9992C20.1681 12.3366 18.002 10.1705 15.3394 10.1705ZM15.3394 17.8703C13.7563 17.8703 12.4683 16.5823 12.4683 14.9992C12.4683 13.416 13.7563 12.128 15.3394 12.128C16.9226 12.128 18.2106 13.416 18.2106 14.9992C18.2106 16.5823 16.9226 17.8703 15.3394 17.8703Z\"\r\n        // fill=\"#333333\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default NavSettingsIcon;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;kBAC/D,cAAA,6LAAC;YACC,GAAE;;;;;;;;;;;AAKV;KATM;uCAWS", "debugId": null}}, {"offset": {"line": 2962, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2968, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/header/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { AuthState } from \"@/redux/slices/authSlice\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nimport { syncReduxStateToCookies } from \"@/utils/syncReduxToCookies\";\r\nimport Logo from \"../../../public/assets/images/logo.svg\";\r\nimport downArrow from \"../../../public/assets/images/down-arrow.svg\";\r\nimport User from \"../../../public/assets/images/user.png\";\r\nimport styles from \"@/styles/header.module.scss\";\r\nimport NotificationIcon from \"../svgComponents/Notification\";\r\nimport { logout } from \"@/utils/helper\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { selectProfileData, setPermissions } from \"@/redux/slices/authSlice\";\r\nimport { getUserPermissions } from \"@/services/authServices\";\r\n\r\n// Interface definitions moved to authServices.ts\r\nimport DataSecurityIcon from \"../svgComponents/dataSecurityIcon\";\r\nimport ROUTES from \"@/constants/routes\";\r\nimport { IUserData } from \"@/interfaces/authInterfaces\";\r\nimport EmployeeManagementIcon from \"../svgComponents/EmployeeManagementIcon\";\r\nimport RolesIcon from \"../svgComponents/RolesIcon\";\r\nimport ProfileIcon from \"../svgComponents/ProfileIcon\";\r\nimport LogoutIcon from \"../svgComponents/LogoutIcon\";\r\nimport Notifications from \"../views/notification/Notifications\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { getUnreadNotificationsCount } from \"@/services/notificationServices/notificationService\";\r\nimport { setHasUnreadNotification } from \"@/redux/slices/notificationSlice\";\r\nimport NavCalendarIcon from \"../svgComponents/NavCalendarIcon\";\r\nimport NavCandidatesIcon from \"../svgComponents/NavCandidatesIcon\";\r\nimport NavHomeIcon from \"../svgComponents/NavHomeIcon\";\r\nimport NavJobsIcon from \"../svgComponents/NavJobsIcon\";\r\nimport NavSettingsIcon from \"../svgComponents/NavSettingsIcon\";\r\n\r\nconst Header = () => {\r\n  const [dropdown, SetDropdown] = useState(false);\r\n  const userProfile: IUserData | null = useSelector(selectProfileData);\r\n\r\n  const path = usePathname();\r\n  const dispatch = useDispatch();\r\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\r\n  const t = useTranslations(\"header\");\r\n  const tCommon = useTranslations(\"common\");\r\n  const tr = useTranslations();\r\n  const pathname = usePathname();\r\n  const [isNotificationOpen, setIsNotificationOpen] = useState(false);\r\n  const dropdownRef = React.useRef<HTMLDivElement>(null);\r\n  const hasUnreadNotification = useSelector((state: RootState) => state.notification.hasUnreadNotifications);\r\n\r\n  const navigate = useRouter();\r\n\r\n  // Handle clicks outside of dropdown to close it\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        SetDropdown(false);\r\n      }\r\n    };\r\n\r\n    // Add event listener when dropdown is open\r\n    if (dropdown) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n\r\n    // Clean up event listener\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdown]);\r\n\r\n  // Toggle dropdown visibility\r\n  const MenuDropdown = () => {\r\n    SetDropdown(!dropdown);\r\n  };\r\n\r\n  // Function to fetch permissions using the authServices\r\n  const fetchPermissions = useCallback(async () => {\r\n    try {\r\n      const response = await getUserPermissions();\r\n\r\n      // Only update Redux store when success is true\r\n      if (response.data?.success) {\r\n        dispatch(setPermissions(response.data.data.rolePermissions));\r\n        // Sync Redux state to cookies after updating permissions\r\n        syncReduxStateToCookies(response.data.data.rolePermissions, true);\r\n      } else {\r\n        console.log(\"Permission fetch unsuccessful:\", response.data?.message);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching permissions:\", error);\r\n    }\r\n  }, [path, dispatch]);\r\n\r\n  const getUserNotificationsUnreadStatus = useCallback(async () => {\r\n    try {\r\n      const response = await getUnreadNotificationsCount();\r\n      if (response.data?.success) {\r\n        const hasUnreadNotifications = response.data.data.count > 0;\r\n        dispatch(setHasUnreadNotification(hasUnreadNotifications));\r\n      } else {\r\n        console.error(\"Failed to fetch unread notifications status:\", response.data?.message);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching unread notifications status:\", error);\r\n    }\r\n  }, []);\r\n\r\n  // Sync Redux state to cookies after mounting component\r\n  useEffect(() => {\r\n    syncReduxStateToCookies();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Check if this is first mount or a genuine route change\r\n    fetchPermissions();\r\n    getUserNotificationsUnreadStatus();\r\n  }, [path, dispatch, fetchPermissions]);\r\n\r\n  /**\r\n   * Logs out the user if the access token is invalid.\r\n   * If the access token is invalid, it logs out the user and shows a toast message.\r\n   */\r\n\r\n  // const logoutUser = async () => {\r\n  //   const token = getAccessToken();\r\n  //   if (!token) {\r\n  //     onHandleLogout();\r\n  //     toast.dismiss();\r\n  //     toastMessageError(t(\"session_expired\"));\r\n  //   }\r\n  // };\r\n\r\n  const onHandleLogout = async () => {\r\n    await logout(authData?.id);\r\n\r\n    if (typeof window !== \"undefined\") {\r\n      window.location.reload();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header\r\n        className={styles.header}\r\n        // className={`${styles.header} ${isVisible ? \"\" : `${styles.hidden}`}`}\r\n      >\r\n        <nav className=\"navbar navbar-expand-sm\">\r\n          <div className=\"container\">\r\n            <div className=\"d-flex align-items-center justify-content-between w-100\">\r\n              <Link className=\"navbar-brand\" href={ROUTES.HOME}>\r\n                <Image src={Logo} alt=\"logo\" width={640} height={320} className={styles.logo} />\r\n              </Link>\r\n              {/* <Button className=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#collapsibleNavbar\">\r\n              <span className=\"navbar-toggler-icon\"></span>\r\n            </Button> */}\r\n              <ul className=\"header_links\">\r\n                <li className={pathname === ROUTES.DASHBOARD ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.DASHBOARD)}>\r\n                  <NavHomeIcon /> Home\r\n                </li>\r\n                <li\r\n                  className={pathname === ROUTES.SCREEN_RESUME.CANDIDATES ? \"active\" : \"\"}\r\n                  onClick={() => navigate.push(ROUTES.SCREEN_RESUME.CANDIDATES)}\r\n                >\r\n                  <NavCandidatesIcon /> Candidates\r\n                </li>\r\n                <li className={pathname === ROUTES.INTERVIEW.CALENDAR ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.INTERVIEW.CALENDAR)}>\r\n                  <NavCalendarIcon /> Calendar\r\n                </li>\r\n                <li className={pathname === ROUTES.JOBS.ACTIVE_JOBS ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.JOBS.ACTIVE_JOBS)}>\r\n                  <NavJobsIcon /> Jobs\r\n                </li>\r\n                <li className={pathname === ROUTES.PROFILE.MY_PROFILE ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.PROFILE.MY_PROFILE)}>\r\n                  <NavSettingsIcon /> Settings\r\n                </li>\r\n                <span></span>\r\n              </ul>\r\n              <div className={`collapse navbar-collapse justify-content-end ${styles.navbar_content}`} id=\"collapsibleNavbar\">\r\n                {/* <ul className={`navbar-nav ${styles.navbar_links}`}>\r\n                <li className=\"nav-item\">\r\n                  <Link\r\n                    className={`nav-link ${pathname === ROUTES.JOBS.GENERATE_JOB || pathname === ROUTES.JOBS.CAREER_BASED_SKILLS || pathname === ROUTES.JOBS.CULTURE_BASED_SKILLS || pathname === ROUTES.JOBS.ROLE_BASED_SKILLS || pathname === ROUTES.JOBS.EDIT_SKILLS || pathname === ROUTES.JOBS.JOB_EDITOR || pathname === ROUTES.JOBS.HIRING_TYPE ? styles.active : \"\"}`}\r\n                    href={ROUTES.JOBS.HIRING_TYPE}\r\n                  >\r\n                    {t(\"job_requirement_generations\")}\r\n                  </Link>\r\n                </li>\r\n                <li className=\"nav-item\">\r\n                  <Link\r\n                    className={`nav-link ${pathname === ROUTES.JOBS.ACTIVE_JOBS || pathname?.startsWith(ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD) || pathname?.startsWith(ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION) ? styles.active : \"\"}`}\r\n                    href={ROUTES.JOBS.ACTIVE_JOBS}\r\n                  >\r\n                    {t(\"resume_screening\")}\r\n                  </Link>\r\n                </li>\r\n                <li className=\"nav-item\">\r\n                  <Link className=\"nav-link\" href=\"#\">\r\n                    {t(\"conduct_interview\")}\r\n                  </Link>\r\n                </li>\r\n                <li className=\"nav-item\">\r\n                  <Link\r\n                    className={`nav-link ${pathname === ROUTES.DASHBOARD || pathname === ROUTES.SCREEN_RESUME.CANDIDATES || pathname === ROUTES.JOBS.ARCHIVE ? styles.active : \"\"}`}\r\n                    href={ROUTES.DASHBOARD}\r\n                  >\r\n                    {tCommon(\"hm_dashboard\")}\r\n                  </Link>\r\n                </li>\r\n              </ul> */}\r\n\r\n                <div className={styles.header_right}>\r\n                  <NotificationIcon\r\n                    hasNotification={hasUnreadNotification}\r\n                    id=\"notification-icon-id\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      e.preventDefault();\r\n                      setIsNotificationOpen((prev) => !prev);\r\n                    }}\r\n                  />\r\n                  <div className={`dropdown ${styles.user_drop}`}>\r\n                    <button type=\"button\" className={`dropdown-toggle ${styles.user_drop_btn}`} data-bs-toggle=\"dropdown\" onClick={MenuDropdown}>\r\n                      <div className={`${styles.circle_img}`}>\r\n                        <Image src={userProfile?.image || User} alt=\"Profile\" width={100} height={100} />\r\n                      </div>\r\n                      <div className={styles.admin_info}>\r\n                        <h5>{`${userProfile?.first_name}`}</h5>\r\n                      </div>\r\n                      <Image src={downArrow} alt=\"downArrow\" style={{ rotate: `${dropdown ? \"180deg\" : \"0deg\"}` }} />\r\n                    </button>\r\n                    {dropdown && (\r\n                      <ul className={styles.dropdown_menu}>\r\n                        <li>\r\n                          <ProfileIcon />\r\n                          <span\r\n                            onClick={() => {\r\n                              navigate.push(ROUTES.PROFILE.MY_PROFILE);\r\n                              SetDropdown(false);\r\n                            }}\r\n                          >\r\n                            {t(\"my_profile\")}\r\n                          </span>\r\n                        </li>\r\n                        <li>\r\n                          <RolesIcon />\r\n                          <span\r\n                            onClick={() => {\r\n                              navigate.push(ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS);\r\n                              SetDropdown(false);\r\n                            }}\r\n                          >\r\n                            {tr(\"roles_and_permissions\")}\r\n                          </span>\r\n                        </li>\r\n\r\n                        <li>\r\n                          <EmployeeManagementIcon />\r\n                          <span\r\n                            onClick={() => {\r\n                              navigate.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);\r\n                              SetDropdown(false);\r\n                            }}\r\n                          >\r\n                            {tr(\"employee_management\")}\r\n                          </span>\r\n                        </li>\r\n                        <li>\r\n                          <LogoutIcon className=\"strokeSvg\" />\r\n                          <span onClick={() => onHandleLogout()}>Logout</span>\r\n                        </li>\r\n                      </ul>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </nav>\r\n      </header>\r\n      {isNotificationOpen ? <Notifications setIsNotificationOpen={setIsNotificationOpen} /> : null}\r\n\r\n      {/* common pages information box for  Job Requirement Generation page */}\r\n      {pathname === ROUTES.JOBS.GENERATE_JOB && (\r\n        <div className=\"information-box\">\r\n          <DataSecurityIcon />\r\n          <p>{tCommon(\"data_security_msg\")}</p>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,iDAAiD;AACjD;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AArCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,MAAM,SAAS;;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAgC,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE,sIAAA,CAAA,oBAAiB;IAEnE,MAAM,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;wCAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;;IAChF,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,KAAK,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACzB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IACjD,MAAM,wBAAwB,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,QAAqB,MAAM,YAAY,CAAC,sBAAsB;;IAEzG,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEzB,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;uDAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,YAAY;oBACd;gBACF;;YAEA,2CAA2C;YAC3C,IAAI,UAAU;gBACZ,SAAS,gBAAgB,CAAC,aAAa;YACzC;YAEA,0BAA0B;YAC1B;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG;QAAC;KAAS;IAEb,6BAA6B;IAC7B,MAAM,eAAe;QACnB,YAAY,CAAC;IACf;IAEA,uDAAuD;IACvD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD;gBAExC,+CAA+C;gBAC/C,IAAI,SAAS,IAAI,EAAE,SAAS;oBAC1B,SAAS,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe;oBAC1D,yDAAyD;oBACzD,CAAA,GAAA,qIAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC9D,OAAO;oBACL,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI,EAAE;gBAC/D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;+CAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,mCAAmC,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE;YACnD,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,8BAA2B,AAAD;gBACjD,IAAI,SAAS,IAAI,EAAE,SAAS;oBAC1B,MAAM,yBAAyB,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;oBAC1D,SAAS,CAAA,GAAA,8IAAA,CAAA,2BAAwB,AAAD,EAAE;gBACpC,OAAO;oBACL,QAAQ,KAAK,CAAC,gDAAgD,SAAS,IAAI,EAAE;gBAC/E;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;QACF;+DAAG,EAAE;IAEL,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,CAAA,GAAA,qIAAA,CAAA,0BAAuB,AAAD;QACxB;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,yDAAyD;YACzD;YACA;QACF;2BAAG;QAAC;QAAM;QAAU;KAAiB;IAErC;;;GAGC,GAED,mCAAmC;IACnC,oCAAoC;IACpC,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,+CAA+C;IAC/C,MAAM;IACN,KAAK;IAEL,MAAM,iBAAiB;QACrB,MAAM,CAAA,GAAA,yHAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QAEvB,wCAAmC;YACjC,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,qBACE;;0BACE,6LAAC;gBACC,WAAW,wJAAA,CAAA,UAAM,CAAC,MAAM;0BAGxB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAe,MAAM,6HAAA,CAAA,UAAM,CAAC,IAAI;8CAC9C,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCAAC,KAAK,uSAAA,CAAA,UAAI;wCAAE,KAAI;wCAAO,OAAO;wCAAK,QAAQ;wCAAK,WAAW,wJAAA,CAAA,UAAM,CAAC,IAAI;;;;;;;;;;;8CAK9E,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAW,aAAa,6HAAA,CAAA,UAAM,CAAC,SAAS,GAAG,WAAW;4CAAI,SAAS,IAAM,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,SAAS;;8DACzG,6LAAC,qJAAA,CAAA,UAAW;;;;;gDAAG;;;;;;;sDAEjB,6LAAC;4CACC,WAAW,aAAa,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,UAAU,GAAG,WAAW;4CACrE,SAAS,IAAM,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,UAAU;;8DAE5D,6LAAC,2JAAA,CAAA,UAAiB;;;;;gDAAG;;;;;;;sDAEvB,6LAAC;4CAAG,WAAW,aAAa,6HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,WAAW;4CAAI,SAAS,IAAM,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,QAAQ;;8DAC3H,6LAAC,yJAAA,CAAA,UAAe;;;;;gDAAG;;;;;;;sDAErB,6LAAC;4CAAG,WAAW,aAAa,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW;4CAAI,SAAS,IAAM,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;;8DACvH,6LAAC,qJAAA,CAAA,UAAW;;;;;gDAAG;;;;;;;sDAEjB,6LAAC;4CAAG,WAAW,aAAa,6HAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,GAAG,WAAW;4CAAI,SAAS,IAAM,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;;8DAC3H,6LAAC,yJAAA,CAAA,UAAe;;;;;gDAAG;;;;;;;sDAErB,6LAAC;;;;;;;;;;;8CAEH,6LAAC;oCAAI,WAAW,CAAC,6CAA6C,EAAE,wJAAA,CAAA,UAAM,CAAC,cAAc,EAAE;oCAAE,IAAG;8CAiC1F,cAAA,6LAAC;wCAAI,WAAW,wJAAA,CAAA,UAAM,CAAC,YAAY;;0DACjC,6LAAC,sJAAA,CAAA,UAAgB;gDACf,iBAAiB;gDACjB,IAAG;gDACH,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,EAAE,cAAc;oDAChB,sBAAsB,CAAC,OAAS,CAAC;gDACnC;;;;;;0DAEF,6LAAC;gDAAI,WAAW,CAAC,SAAS,EAAE,wJAAA,CAAA,UAAM,CAAC,SAAS,EAAE;;kEAC5C,6LAAC;wDAAO,MAAK;wDAAS,WAAW,CAAC,gBAAgB,EAAE,wJAAA,CAAA,UAAM,CAAC,aAAa,EAAE;wDAAE,kBAAe;wDAAW,SAAS;;0EAC7G,6LAAC;gEAAI,WAAW,GAAG,wJAAA,CAAA,UAAM,CAAC,UAAU,EAAE;0EACpC,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEAAC,KAAK,aAAa,SAAS,uSAAA,CAAA,UAAI;oEAAE,KAAI;oEAAU,OAAO;oEAAK,QAAQ;;;;;;;;;;;0EAE5E,6LAAC;gEAAI,WAAW,wJAAA,CAAA,UAAM,CAAC,UAAU;0EAC/B,cAAA,6LAAC;8EAAI,GAAG,aAAa,YAAY;;;;;;;;;;;0EAEnC,6LAAC,gIAAA,CAAA,UAAK;gEAAC,KAAK,yTAAA,CAAA,UAAS;gEAAE,KAAI;gEAAY,OAAO;oEAAE,QAAQ,GAAG,WAAW,WAAW,QAAQ;gEAAC;;;;;;;;;;;;oDAE3F,0BACC,6LAAC;wDAAG,WAAW,wJAAA,CAAA,UAAM,CAAC,aAAa;;0EACjC,6LAAC;;kFACC,6LAAC,qJAAA,CAAA,UAAW;;;;;kFACZ,6LAAC;wEACC,SAAS;4EACP,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;4EACvC,YAAY;wEACd;kFAEC,EAAE;;;;;;;;;;;;0EAGP,6LAAC;;kFACC,6LAAC,mJAAA,CAAA,UAAS;;;;;kFACV,6LAAC;wEACC,SAAS;4EACP,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,cAAc,CAAC,iBAAiB;4EACrD,YAAY;wEACd;kFAEC,GAAG;;;;;;;;;;;;0EAIR,6LAAC;;kFACC,6LAAC,gKAAA,CAAA,UAAsB;;;;;kFACvB,6LAAC;wEACC,SAAS;4EACP,SAAS,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,cAAc,CAAC,mBAAmB;4EACvD,YAAY;wEACd;kFAEC,GAAG;;;;;;;;;;;;0EAGR,6LAAC;;kFACC,6LAAC,oJAAA,CAAA,UAAU;wEAAC,WAAU;;;;;;kFACtB,6LAAC;wEAAK,SAAS,IAAM;kFAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW1D,mCAAqB,6LAAC,+JAAA,CAAA,UAAa;gBAAC,uBAAuB;;;;;uBAA4B;YAGvF,aAAa,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,kBACpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0JAAA,CAAA,UAAgB;;;;;kCACjB,6LAAC;kCAAG,QAAQ;;;;;;;;;;;;;;AAKtB;GA/PM;;QAEkC,4JAAA,CAAA,cAAW;QAEpC,qIAAA,CAAA,cAAW;QACP,4JAAA,CAAA,cAAW;QACX,4JAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;QACT,yMAAA,CAAA,kBAAe;QACpB,yMAAA,CAAA,kBAAe;QACT,qIAAA,CAAA,cAAW;QAGE,4JAAA,CAAA,cAAW;QAExB,qIAAA,CAAA,YAAS;;;KAftB;uCAiQS", "debugId": null}}, {"offset": {"line": 3546, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3552, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/header/HeaderWrapper.tsx"], "sourcesContent": ["\"use client\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport Header from \"@/components/header/Header\";\r\nimport { BEFORE_LOGIN_ROUTES } from \"@/constants/routes\";\r\n\r\nexport default function HeaderWrapper() {\r\n  const pathname = usePathname();\r\n\r\n  console.log(\"pathname\", pathname);\r\n  if (BEFORE_LOGIN_ROUTES.includes(pathname!)) {\r\n    return null;\r\n  }\r\n\r\n  return <Header />;\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,QAAQ,GAAG,CAAC,YAAY;IACxB,IAAI,6HAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,WAAY;QAC3C,OAAO;IACT;IAEA,qBAAO,6LAAC,yIAAA,CAAA,UAAM;;;;;AAChB;GATwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 3589, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}