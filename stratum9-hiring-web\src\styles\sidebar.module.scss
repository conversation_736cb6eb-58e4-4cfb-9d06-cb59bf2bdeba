@use "./abstracts" as *;

//sidebar style ----------
.sidebar {
  background: rgba($primary, 0.1);
  width: 70px;
  min-width: 70px;
  max-width: 70px;
  border-radius: 24px;
  min-height: calc(100vh - 330px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 1rem;
  padding: 25px 20px;
  position: sticky;
  top: 1rem;
  left: 0;
  z-index: 1;
  .sidebar_list {
    @extend %listSpacing;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 15px;
    .sidebar_item {
      border-radius: 10px;
      width: 40px;
      min-width: 40px;
      height: 40px;
      min-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      flex-direction: column;
      svg {
        fill: $dark;
      }
      &:hover,
      &.active {
        background: $primary;
        transition: all 0.4s ease;
        svg {
          fill: $white;
        }
      }
      &:last-child {
        margin-top: 30px;
        position: relative;
        &:after {
          content: "";
          position: absolute;
          top: -30px;
          left: 0;
          right: 0;
          width: 90%;
          height: 1px;
          margin: auto;
          background: $dark;
          border-radius: 10px;
        }
        // svg{
        //   fill: $dark;
        // }
        // &:hover, &.active{
        //   background: transparent;
        //   svg{
        //     fill: $primary;
        //   }
        // }
      }
    }
  }
  .sidebar {
    .logout_icon {
      min-width: 28px;
      min-height: 28px;
    }
  }
}
