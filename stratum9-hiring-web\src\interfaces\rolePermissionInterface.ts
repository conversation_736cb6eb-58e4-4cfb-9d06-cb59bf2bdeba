import { UpdateRolePermissionsResponse } from "@/services/roleService";

export interface Permission {
  id: number;
  description: string;
  selected: boolean;
}

export interface RolePermissionsResponse {
  success: boolean;
  message: string;
  data: {
    role_id: number;
    role_name: string;
    permissions: Permission[];
  };
  code: number;
}

export interface IProps {
  onClickCancel: () => void;
  onSubmitSuccess: (message?: string, updatedRole?: UpdateRolePermissionsResponse) => void;
  disabled?: boolean;
  role: { id: number; name: string };
}
