import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import { ExtendedFormValues } from "@/types/types";

/**
 * Generates job skills based on the provided form data
 * @param formData Job requirement form data
 * @returns Generated job response
 */
export const generateJobSkills = (formData: ExtendedFormValues): Promise<ApiResponse> => {
  return http.post(endpoint.jobRequirements.GENERATE_SKILL, formData);
};

/**
 * Generates job requirement based on the provided form data
 * @param formData Job requirement form data
 * @returns Generated job response
 */
export const generateJobRequirement = (
  formData: Omit<ExtendedFormValues, "compliance_statement"> & { compliance_statement: string }
): Promise<ApiResponse> => {
  return http.post(endpoint.jobRequirements.GENERATE_JOB_REQUIREMENT, formData);
};
