"use client";
import React, { useEffect, useState } from "react";
import style from "@/styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { PlanData } from "../../../interfaces/subscriptionInterfaces";
import { subscriptionService } from "@/services/subscription";
import NewJobCreationIcon from "@/components/svgComponents/subscription/NewJobCreationIcon";
import CandidateTrackingIcon from "@/components/svgComponents/subscription/CandidateTrackingIcon";
import PreScreeningIcon from "@/components/svgComponents/subscription/PreScreeningIcon";
import ResumeScreeningIcon from "@/components/svgComponents/subscription/ResumeScreeningIcon";
import AIJobDescriptionIcon from "@/components/svgComponents/subscription/AIJobDescriptionIcon";
import ApplicantTrackingSystemIcon from "@/components/svgComponents/subscription/ApplicantTrackingSystemIcon";
import AIGeneratedQuestionsIcon from "@/components/svgComponents/subscription/AIGeneratedQuestionsIcon";
import RealTimeFollowUpIcon from "@/components/svgComponents/subscription/RealTimeFollowUpIcon";
import NonVerbalCommunicationIcon from "@/components/svgComponents/subscription/NonVerbalCommunicationIcon";
import FinalAnalysisIcon from "@/components/svgComponents/subscription/FinalAnalysisIcon";
import AIPoweredInterviewSummaryIcon from "@/components/svgComponents/subscription/AIPoweredInterviewSummaryIcon";
import SkillSpecificAssessmentsIcon from "@/components/svgComponents/subscription/SkillSpecificAssessmentsIcon";
import RoleBasedAccessIcon from "@/components/svgComponents/subscription/RoleBasedAccessIcon";
import DedicatedSupportIcon from "@/components/svgComponents/subscription/DedicatedSupportIcon";
import ManualResumeUploadIcon from "@/components/svgComponents/subscription/ManualResumeUploadIcon";
import DataSecurityIcon from "@/components/svgComponents/subscription/DataSecurityIcon";
import ROUTES from "@/constants/routes";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { setCurrentPlan } from "@/redux/slices/authSlice";
import { toastMessageError } from "@/utils/helper";
import { useTranslations } from "next-intl";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { SUBSCRIPTION_PAYMENT_TYPE } from "@/constants/subscriptionConstants";
import ConfirmationModal from "@/components/commonModals/ConfirmationModal";

const initialDataOfConfirmationModal = {
  isOpen: false,
  title: "",
  message: "",
  confirmButtonText: "",
  cancelButtonText: "",
  onclickConfirm: () => {},
  onClickCancel: () => {},
};
function BuySubscription() {
  const t = useTranslations();

  const router = useRouter();
  const dispatch = useDispatch();

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingAllPlans, setLoadingAllPlans] = useState<boolean>(false);
  const [plans, setPlans] = useState<PlanData[]>([]);

  const [confirmationModalInfo, setConfirmationModalInfo] = useState(initialDataOfConfirmationModal);

  const myCurrentPlan = useSelector((state: RootState) => state.auth.currentPlan);

  console.log("Current Plan from Redux:", myCurrentPlan, loadingAllPlans);

  // Helper function to directly get the feature text value by slug
  const getBenefitValue = (plan: PlanData, benefitKey: string): string => {
    console.log("Plan Benefits:", plan.subscriptionPlanBenefits);

    // Find the feature with the matching slug
    const feature = plan.subscriptionPlanBenefits?.find((item) => item.slug === benefitKey);

    // Return the text value if found, otherwise return 'Not available'
    return feature?.text || "Not available";
  };

  useEffect(() => {
    initializeSubscriptionData();
    fetchAllPlans();
  }, []);

  // Initialize subscription data - calls current plan first, then all plans
  const initializeSubscriptionData = async () => {
    // try {
    setLoading(true);
    // First fetch the current subscription
    const response = await subscriptionService.getCurrentSubscription();
    // let currentPlanId = response.data.data.plan_id; // Default to Free Trial if no active subscription

    if (response?.data?.success) {
      dispatch(setCurrentPlan(response.data.data));
    }
    //   interface ApiSubscriptionResponse {
    //     plan_id?: number;
    //     plan_name?: string;
    //     subscription_status?: string;
    //     pricing_type?: string;
    //     price?: string;
    //     expiry_date?: string;
    //     next_billing_date?: string;
    //     [key: string]: string | number | boolean | null | undefined; // Allow other properties with specific types
    //   }

    //   const responseData = response.data?.data || {};
    //   const apiData = responseData as unknown as ApiSubscriptionResponse;

    //   if (apiData.plan_id) {
    //     currentPlanId = apiData.plan_id;
    //   }
    // }

    // // Now fetch all plans with the current plan ID
    // await fetchPlansWithCurrentPlan(String(currentPlanId));
    // } catch (error) {
    //   console.error("Error initializing subscription data:", error);
    // }
    setLoading(false);
  };

  // Fetch all plans and set the current plan based on the user's subscription
  const fetchAllPlans = async () => {
    try {
      setLoadingAllPlans(true);
      const response = await subscriptionService.getAllPlans();

      // Extract the plans array from the API response's nested data structure
      let plansArray: PlanData[] = [];
      if (response?.data?.success && response.data.data) {
        plansArray = response.data.data as PlanData[];
      }

      setPlans(plansArray);

      // const currentPlan = plansArray.find((plan) => String(plan.plan_id) === currentPlanId);
      // if (currentPlan) {
      //   // Set the current plan as selected
      //   setSelectedPlan(currentPlan.plan_name);
      //   // setSelectedPlanId(String(currentPlan.plan_id));

      //   // Update pricing options for this plan
      //   if (currentPlan.pricing_options && currentPlan.pricing_options.length > 0) {
      //     setSelectedPricingId(String(currentPlan.pricing_options[0].pricing_id));
      //   }
      // } else {
      //   const freeTrial = plans.find((plan) => plan.plan_name === "Free Trial");
      //   if (freeTrial) {
      //     setSelectedPlan(freeTrial.plan_name);
      //   } else {
      //     const anyPlanWithPricing = plans.find((plan) => plan.pricing_options.length > 0);
      //     if (anyPlanWithPricing) {
      //       setSelectedPlan(anyPlanWithPricing.plan_name);
      //       setSelectedPricingId(String(anyPlanWithPricing.pricing_options[0].pricing_id));
      //     }
      //   }
      // }
    } catch (error) {
      console.error("Error fetching subscription plans:", error);
      toast.error("Failed to load subscription plans. Please try again.");
    } finally {
      setLoadingAllPlans(false);
    }
  };

  // const handlePlanSelect = (plan: string) => {
  //   const selectedPlanObj = plans.find((p) => p.plan_name === plan);
  //   if (selectedPlanObj) {
  //     setSelectedPlan(plan);
  //     updatePricingForSelectedPlan(String(selectedPlanObj.plan_id));
  //   }
  // };

  // const updatePricingForSelectedPlan = (planId: string) => {
  //   const selectedPlan = plans.find((plan) => String(plan.plan_id) === planId);
  //   if (selectedPlan && selectedPlan.pricing_options.length > 0) {
  //     // Always prioritize Monthly pricing if available
  //     const monthlyPricing = selectedPlan.pricing_options.find((price) => price.type === "Monthly");
  //     if (monthlyPricing) {
  //       setSelectedPricingId(String(monthlyPricing.pricing_id));
  //     } else {
  //       // Fall back to first pricing option if Monthly is not available
  //       const defaultPricing = selectedPlan.pricing_options[0];
  //       setSelectedPricingId(String(defaultPricing.pricing_id));
  //     }
  //   }
  // };

  const handleSubscribeClick = async (selectedPlan: PlanData) => {
    try {
      setLoading(true);

      if (selectedPlan.subscriptionPlanPaymentType === SUBSCRIPTION_PAYMENT_TYPE.FREE) {
        // For free trial, just navigate to the dashboard
        router.push(ROUTES.DASHBOARD);
        return;
      }

      // Prepare data for buy subscription API
      const buySubscriptionData = {
        planId: selectedPlan.subscriptionPlanId,
        pricingId: selectedPlan.pricingId!,
      };

      console.log("Buy subscription data:", buySubscriptionData);

      // Call the simplified buy subscription API
      const buySubscriptionResponse = await subscriptionService.buySubscription(buySubscriptionData);

      console.log("Buy subscription response:", buySubscriptionResponse);

      // Handle the response and redirect to checkout
      if (buySubscriptionResponse.data && buySubscriptionResponse.data.success) {
        const checkoutUrl = buySubscriptionResponse.data.data?.checkoutUrl;
        if (checkoutUrl) {
          console.log("Redirecting to checkout URL:", checkoutUrl);
          // Close the confirmation modal
          setConfirmationModalInfo(initialDataOfConfirmationModal);
          // Show success message
          // toastMessageSuccess("Payment URL fetched successfully! Redirecting to checkout...");
          // Open checkout URL in new tab
          // window.open(checkoutUrl, "_blank");
          window.location.href = checkoutUrl;
        } else {
          setConfirmationModalInfo(initialDataOfConfirmationModal);
          toastMessageError(t(buySubscriptionResponse.data?.message || "failed_to_fetch_checkout_url"));
          return;
        }
      } else {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
        toastMessageError(t(buySubscriptionResponse.data?.message || "failed"));
      }
    } catch (error) {
      setConfirmationModalInfo(initialDataOfConfirmationModal);
      console.error("Error during subscription process:", error);
      toast.error(error instanceof Error ? error.message : "Failed to process subscription. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const onClickFreePlan = () => {
    // show confirmation modal
    setConfirmationModalInfo({
      ...confirmationModalInfo,
      isOpen: true,
      title: "Switch to Free Plan",
      message:
        "You are currently subscribed to a paid plan. To switch to the Free Plan, please cancel your current plan. Once your current plan expires, the Free Plan will automatically be applied to your organization.",
      confirmButtonText: "Proceed",
      onclickConfirm: () => {
        router.push(ROUTES.PROFILE.MY_PROFILE);
      },
    });
  };

  const onClickBuyPlan = (plan: PlanData) => {
    // show confirmation modal
    setConfirmationModalInfo({
      ...confirmationModalInfo,
      isOpen: true,
      title: "Confirm Plan Change",
      message:
        "You are about to switch to a different plan. Please note, your current plan will be canceled, and the new plan will take effect immediately. Would you like to proceed with this change?",
      confirmButtonText: "Buy Plan",
      cancelButtonText: "Cancel",
      onClickCancel: () => {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
      },
      onclickConfirm: () => {
        handleSubscribeClick(plan);
      },
    });
  };
  return (
    <div className={style.subscription_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Subscription <span>Plan</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-content">
          <div className="row">
            <div className="col-md-3">
              <ul className={`${style.subscription_plan} ${style.side_bar} pe-4`}>
                <li className={style.subscription_benefit_text}>Benefits</li>
                <li className={style.fixed_height} title="Job Postings">
                  <NewJobCreationIcon /> <span className={style.benefit_text}>Job Postings</span>
                </li>
                <li className={style.fixed_height} title="Resume Screening">
                  <ResumeScreeningIcon />
                  <span className={style.benefit_text}>Resume Screening</span>
                </li>
                <li className={`${style.fixed_height} ${style.ai_job_description}`} title="AI Job Description">
                  <AIJobDescriptionIcon />
                  <span className={style.benefit_text}>AI Job Description</span>
                </li>
                <li className={`${style.fixed_height} ${style.max_height}`} title="Candidate Tracking">
                  <CandidateTrackingIcon />
                  <span className={style.benefit_text}>Candidate Tracking</span>
                </li>
                <li className={style.fixed_height} title="Applicant Tracking System">
                  <ApplicantTrackingSystemIcon />
                  <span className={style.benefit_text}>Applicant Tracking System</span>
                </li>
                <li className={style.fixed_height} title="Pre-Interview Assessment Upload">
                  <PreScreeningIcon />
                  <span className={style.benefit_text}>Pre-Interview Assessment Upload</span>
                </li>
                <li className={style.fixed_height} title="AI-Generated Pre-Interview Questions">
                  <AIGeneratedQuestionsIcon />
                  <span className={style.benefit_text}>AI-Generated Pre-Interview Questions</span>
                </li>
                <li className={style.fixed_height} title="Real-Time Follow-Up Questions">
                  <RealTimeFollowUpIcon />
                  <span className={style.benefit_text}>Real-Time Follow-Up Questions</span>
                </li>
                <li className={style.fixed_height} title="Non-Verbal Communication Analysis">
                  <NonVerbalCommunicationIcon />
                  <span className={style.benefit_text}>Non-Verbal Communication Analysis</span>
                </li>
                <li className={style.fixed_height} title="Final Analysis After Interview">
                  <FinalAnalysisIcon />
                  <span className={style.benefit_text}>Final Analysis After Interview</span>
                </li>
                <li className={style.fixed_height} title="AI-Powered Interview Summary">
                  <AIPoweredInterviewSummaryIcon />
                  <span className={style.benefit_text}>AI-Powered Interview Summary</span>
                </li>
                <li className={style.fixed_height} title="Skill-Specific AI Assessments">
                  <SkillSpecificAssessmentsIcon />
                  <span className={style.benefit_text}>Skill-Specific AI Assessments</span>
                </li>
                <li className={style.fixed_height} title="Role-Based Access">
                  <RoleBasedAccessIcon />
                  <span className={style.benefit_text}>Role-Based Access</span>
                </li>
                <li className={style.fixed_height} title="Dedicated Support">
                  <DedicatedSupportIcon />
                  <span className={style.benefit_text}>Dedicated Support</span>
                </li>
                <li className={style.fixed_height} title="Manual Resume Upload">
                  <ManualResumeUploadIcon />
                  <span className={style.benefit_text}>Manual Resume Upload</span>
                </li>
                <li className={style.fixed_height} title="Data Security">
                  <DataSecurityIcon />
                  <span className={style.benefit_text}>Data Security</span>
                </li>
              </ul>
            </div>

            {/* Right Side: Dynamic Plan Options */}
            <div className="col">
              <div className="row g-4">
                {plans.map((plan) => {
                  const isCurrentPlan = myCurrentPlan?.subscriptionPlanId === plan.subscriptionPlanId;
                  return (
                    <div key={plan.subscriptionPlanId} className="col-12 col-md-6 col-lg-3">
                      <div className={`${style.subscription_plan_card} ${isCurrentPlan ? style.selected_plan : ""}`}>
                        <div className={style.subscription_option}>
                          <h4 className={style.plan_name}>{plan.subscriptionPlanName}</h4>
                          <div>
                            <h4 className={style.plan_price}>${plan.price || "0.00"}</h4>
                            <p className={style.price_type}>Billed Monthly</p>
                          </div>
                          {isCurrentPlan && <span className={style.save_badge}>Current Plan</span>}
                          <div className="mt-3">
                            {plan.subscriptionPlanPaymentType === SUBSCRIPTION_PAYMENT_TYPE.FREE ? (
                              <Button className={"dark-outline-btn "} onClick={() => onClickFreePlan()} disabled={loading || isCurrentPlan}>
                                {isCurrentPlan ? "Current Plan" : "Avail free"}
                              </Button>
                            ) : (
                              <Button
                                className={isCurrentPlan ? "dark-outline-btn opacity-50 hover-none" : "primary-btn"}
                                onClick={() => onClickBuyPlan(plan)}
                                disabled={loading || isCurrentPlan}
                              >
                                {isCurrentPlan ? "Current Plan" : "Buy Now"}
                              </Button>
                            )}
                          </div>
                        </div>
                        <ul
                          className={`${style.subscription_plan} ${style.subscription_benefit_text} ${isCurrentPlan ? style.selected_plan : ""}`}
                          // onClick={() => handlePlanSelect(plan.subscriptionPlanName)}
                        >
                          {/* <li className={style.fixed_height}>{plan.subscriptionPlanName}</li> */}
                          <li className={style.fixed_height}>{getBenefitValue(plan, "job_postings")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "resume_screening")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "ai_job_description")}</li>
                          <li className={`${style.fixed_height} ${style.max_height}`}>{getBenefitValue(plan, "candidate_tracking")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "applicant_tracking_system")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "pre_interview_assessment_upload")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "ai_generated_questions")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "realtime_follow_up_questions")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "non_verbal_communication_analysis")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "final_analysis_after_interview")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "ai_powered_interview_summary")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "skill_specific_assessments")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "role_based_access")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "dedicated_support")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "manual_resume_upload")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "data_security")}</li>
                        </ul>
                      </div>
                    </div>
                  );
                })}

                {/* Skeleton Loader card  */}
                {loadingAllPlans ? (
                  <div className="row g-4">
                    <div className="col-12 col-md-6 col-lg-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                    <div className="col-12 col-md-6 col-lg-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                    <div className="col-12 col-md-6 col-lg-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                    <div className="col-12 col-md-6 col-lg-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          </div>

          {/* {
            <div className="button-align py-5">
              <Button className="primary-btn rounded-md" onClick={handleSubscribeClick} disabled={loading}>
                {`Subscribe to ${selectedPlan} Monthly Plan`}
              </Button>
              <Button className="dark-outline-btn rounded-md" onClick={handleCancel} disabled={loading || !selectedPricingId}>
                Cancel
              </Button>
            </div>
          } */}

          <ConfirmationModal
            isOpen={confirmationModalInfo.isOpen}
            onClose={confirmationModalInfo.onClickCancel}
            onConfirm={confirmationModalInfo.onclickConfirm}
            title={confirmationModalInfo.title}
            message={confirmationModalInfo.message}
            confirmButtonText={confirmationModalInfo.confirmButtonText}
            // cancelButtonText={confirmationModalInfo.cancelButtonText}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
}

export default BuySubscription;
