{"name": "stratum9-backend-innerview", "version": "1.0.0", "description": "", "main": "App.ts", "scripts": {"start": "set NODE_ENV=local&& nodemon src/App.ts&& node --max-old-space-size=5120", "build": "tsc -p .", "check-types": "tsc --pretty --noEmit", "check-prettier": "prettier --check .", "check-lint": "eslint . --ext .ts --ext .tsx --ext .js --ext .jsx", "prettier-fix": "prettier --write .", "lint-fix": "eslint --fix --ext .js,.jsx,.ts,.tsx", "test-all": "npm run check-prettier && npm run check-lint && npm run check-types && npm run build", "prepare": "husky install", "typeorm": "typeorm-ts-node-esm -d ./src/dataSource.ts", "precommit": "npm run test-all"}, "keywords": [], "author": "Biz4group", "license": "ISC", "dependencies": {"@aws-sdk/client-cloudfront": "^3.438.0", "@aws-sdk/client-s3": "^3.438.0", "@aws-sdk/client-secrets-manager": "^3.438.0", "@aws-sdk/credential-providers": "^3.438.0", "@aws-sdk/s3-request-presigner": "^3.438.0", "@deepgram/sdk": "^3.11.2", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^7.70.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.18", "@types/joi": "^17.2.3", "@types/mysql": "^2.15.22", "@types/swagger-jsdoc": "^6.0.1", "agent-base": "^7.1.4", "axios": "^1.5.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-js": "^4.1.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.10.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "mysql": "^2.18.1", "openai": "^4.100.0", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.2.133", "prettier": "^3.0.3", "puppeteer": "^24.10.0", "redis": "^4.6.10", "reflect-metadata": "^0.2.2", "socket.io": "^4.8.1", "stripe": "^18.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.17", "typeorm-naming-strategies": "^4.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/aws-serverless-express": "^3.3.6", "@types/bcryptjs": "^2.4.4", "@types/cors": "^2.8.14", "@types/crypto-js": "^4.1.2", "@types/ejs": "^3.1.3", "@types/lodash": "^4.14.199", "@types/multer": "^1.4.12", "@types/node": "^20.8.5", "@types/pdf-parse": "^1.1.5", "@types/pubnub": "^7.2.2", "@types/stripe": "^8.0.417", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "concurrently": "^8.2.1", "eslint": "^8.51.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.4.2", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": "20.x"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}