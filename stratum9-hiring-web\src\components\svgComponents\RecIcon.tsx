import React from "react";

function RecIcon({ className }: { className: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="20" height="20" viewBox="0 0 32 32" fill="none">
      <g clipPath="url(#clip0_9893_7870)">
        <path
          d="M16 1C7.72656 1 1 7.72656 1 16C1 24.2734 7.72656 31 16 31C24.2734 31 31 24.2734 31 16C31 7.72656 24.2734 1 16 1ZM16 30.0625C8.24805 30.0625 1.9375 23.752 1.9375 16C1.9375 8.24805 8.24805 1.9375 16 1.9375C23.752 1.9375 30.0625 8.24805 30.0625 16C30.0625 23.752 23.752 30.0625 16 30.0625ZM14.6992 13.5684V15.5312H18.0508C18.3086 15.5312 18.5195 15.7422 18.5195 16C18.5195 16.2578 18.3086 16.4688 18.0508 16.4688H14.6992V18.4316H18.0508C18.3086 18.4316 18.5195 18.6426 18.5195 18.9004C18.5195 19.1582 18.3086 19.3691 18.0508 19.3691H14.2246C13.9668 19.3691 13.7559 19.1582 13.7559 18.9004V13.0996C13.7559 12.8418 13.9668 12.6309 14.2246 12.6309H18.0508C18.3086 12.6309 18.5195 12.8418 18.5195 13.0996C18.5195 13.3574 18.3086 13.5684 18.0508 13.5684H14.6992ZM20.5586 14.7344V17.2598C20.5586 17.9043 21.0801 18.4258 21.7246 18.4258H22.2812C22.9141 18.4258 23.4355 17.9102 23.4473 17.2832C23.4531 17.0254 23.6641 16.8145 23.9277 16.8203C24.1855 16.8262 24.3906 17.0371 24.3906 17.3008C24.3672 18.4375 23.4238 19.3633 22.2871 19.3633H21.7305C20.5703 19.3633 19.627 18.4199 19.627 17.2598V14.7344C19.627 13.5742 20.5703 12.6309 21.7305 12.6309H22.2871C23.4238 12.6309 24.3672 13.5566 24.3906 14.6934C24.3965 14.9512 24.1914 15.168 23.9277 15.1738C23.6641 15.1797 23.4531 14.9746 23.4473 14.7109C23.4355 14.0781 22.9141 13.5684 22.2812 13.5684H21.7246C21.0801 13.5684 20.5586 14.0957 20.5586 14.7344ZM10.4336 12.6309H8.08984C7.83203 12.6309 7.62109 12.8418 7.62109 13.0996V16.4277V18.8945C7.62109 19.1523 7.83203 19.3633 8.08984 19.3633C8.34766 19.3633 8.55859 19.1523 8.55859 18.8945V16.8965H9.29688L11.0195 19.0938C11.1133 19.2109 11.248 19.2754 11.3887 19.2754C11.4883 19.2754 11.5938 19.2402 11.6758 19.1758C11.8809 19.0176 11.916 18.7188 11.7578 18.5195L10.4863 16.9023C11.6406 16.873 12.5664 15.9297 12.5664 14.7695C12.5723 13.5918 11.6113 12.6309 10.4336 12.6309ZM10.4336 15.9648H8.55859V13.5742H10.4336C11.0957 13.5742 11.6289 14.1074 11.6289 14.7695C11.6289 15.4258 11.0957 15.9648 10.4336 15.9648Z"
          strokeWidth="0.6"
        />
      </g>
      <defs>
        <clipPath id="clip0_9893_7870">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default RecIcon;
