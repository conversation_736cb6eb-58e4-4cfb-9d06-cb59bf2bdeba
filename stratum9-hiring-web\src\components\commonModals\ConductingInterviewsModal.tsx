"use client";
import React, { FC } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Image from "next/image";
import InterviewInfoImg from "../../../public/assets/images/interview-info.png";
import { INTERVIEW_SCHEDULE_ROUND_TYPE, ONE_TO_ONE_INTERVIEW_INSTRUCTIONS, VIDEO_CALL_INTERVIEW_INSTRUCTIONS } from "@/constants/commonConstants";
interface IProps {
  onClickCancel: () => void;
  onClickContinue: () => void;
  type: string;
  disabled?: boolean;
}

const ConductingInterviewsModal: FC<IProps> = ({ onClickCancel, onClickContinue, type }) => {
  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content">
          <div className="modal-header text-start pb-0">
            <h4>
              Best Practices for <span>Conducting Interviews</span>
            </h4>
            {/* <p className="m-0 textMd">Ensure a Smooth and Professional Interview Experience</p> */}
            <Button className="modal-close-btn" onClick={onClickCancel}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body position-relative  ">
            {/* interview-info */}
            <Image src={InterviewInfoImg} alt="InterviewInfoImg" className="interview-info-img" width={500} height={500} />
            <div className="interview-info">
              {type === INTERVIEW_SCHEDULE_ROUND_TYPE[0].value
                ? ONE_TO_ONE_INTERVIEW_INSTRUCTIONS.map((instruction, index) => (
                    <div className="info-item w-75" key={index}>
                      <h4 className="info-title">
                        <span className="dot" />
                        {instruction}
                      </h4>
                    </div>
                  ))
                : VIDEO_CALL_INTERVIEW_INSTRUCTIONS.map((instruction, index) => (
                    <div className="info-item w-75" key={index}>
                      <h4 className="info-title">
                        <span className="dot" />
                        {instruction}
                      </h4>
                    </div>
                  ))}
            </div>
            <div className="action-btn">
              <Button onClick={onClickContinue} className="primary-btn rounded-md">
                Continue
              </Button>
              <Button onClick={onClickCancel} className="dark-outline-btn rounded-md">
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ConductingInterviewsModal;
