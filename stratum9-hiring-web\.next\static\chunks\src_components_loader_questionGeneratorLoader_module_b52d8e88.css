/* [project]/src/components/loader/questionGeneratorLoader.module.css [app-client] (css) */
.questionGeneratorLoader-module__dUgNXW__question_generator_loader_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000b3;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.questionGeneratorLoader-module__dUgNXW__loader_wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.questionGeneratorLoader-module__dUgNXW__loader_container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.questionGeneratorLoader-module__dUgNXW__loader_text {
  margin-top: 15px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  width: 100%;
  position: relative;
}

.questionGeneratorLoader-module__dUgNXW__loader {
  width: 8px;
  height: 40px;
  border-radius: 4px;
  display: block;
  margin: 20px auto;
  position: relative;
  background: currentColor;
  color: #fff;
  box-sizing: border-box;
  animation: .3s linear .3s infinite alternate questionGeneratorLoader-module__dUgNXW__loaderAnim;
}

.questionGeneratorLoader-module__dUgNXW__loader:after, .questionGeneratorLoader-module__dUgNXW__loader:before {
  content: "";
  width: 8px;
  height: 40px;
  border-radius: 4px;
  background: currentColor;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 20px;
  box-sizing: border-box;
  animation: .3s linear .45s infinite alternate questionGeneratorLoader-module__dUgNXW__loaderAnim;
}

.questionGeneratorLoader-module__dUgNXW__loader:before {
  left: -20px;
  animation-delay: 0s;
}

@keyframes questionGeneratorLoader-module__dUgNXW__loaderAnim {
  0% {
    height: 48px;
  }

  100% {
    height: 4px;
  }
}

/*# sourceMappingURL=src_components_loader_questionGeneratorLoader_module_b52d8e88.css.map*/