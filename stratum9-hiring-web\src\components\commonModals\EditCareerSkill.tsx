import React, { <PERSON> } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import { useForm } from "react-hook-form";
import { RoleModalProps } from "@/interfaces/roleInterface";
import Textarea from "../formElements/Textarea";

const EditCareerSkill: FC<RoleModalProps> = ({ onClickCancel }) => {
  const { control } = useForm();

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>Edit Career Skill</h2>
            <Button className="modal-close-btn" onClick={onClickCancel}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            <form>
              <InputWrapper>
                <InputWrapper.Label htmlFor="name" required>
                  Skill Name
                </InputWrapper.Label>
                <Textbox className="form-control" control={control} name="name" type="text" placeholder="Enter Skill Name"></Textbox>
              </InputWrapper>
              <InputWrapper>
                <InputWrapper.Label htmlFor="description">
                  Description <span className="text-muted">(Optional)</span>
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="description"
                  control={control}
                  placeholder="Is there anything specific you'd like included in the job post or description?"
                  className="form-control"
                />
              </InputWrapper>

              <div className="button-align mt-4">
                <Button type="submit" className="primary-btn rounded-md w-100">
                  Save Changes
                </Button>
                <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel}>
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditCareerSkill;
