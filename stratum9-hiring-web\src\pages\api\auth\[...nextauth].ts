import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

import endpoints from "@/constants/endpoint";
import routes from "@/constants/routes";

export default NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },

      async authorize(credentials) {
        const { email, password } = credentials as { email: string; password: string };

        const res = await fetch(endpoints.auth.SIGNIN, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email,
            password,
          }),
        });

        const user = await res.json();

        if (user) {
          return user;
        } else return null;
      },
    }),
  ],
  session: {
    maxAge: 3 * 24 * 60 * 60,
  },
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: routes.LOGIN,
  },
  callbacks: {
    async jwt({ token, user }) {
      return { ...token, ...user };
    },
    async session({ session, token }) {
      session.user = token;
      return session;
    },
  },
});
