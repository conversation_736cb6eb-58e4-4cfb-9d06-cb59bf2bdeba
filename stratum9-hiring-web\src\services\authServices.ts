import endpoint from "@/constants/endpoint";
import { IForgotPassword, ILogin, IResendOTP, IResetPassword, IVerifyOTP, UserPermissionsResponse } from "@/interfaces/authInterfaces";
import * as http from "@/utils/http";
import { ApiResponse, IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { AxiosResponse } from "axios";

// Using UserPermissionsResponse interface from authInterfaces.ts

export const logIn = (data: ILogin): Promise<ApiResponse> => {
  return http.post(endpoint.auth.SIGNIN, data);
};

export const verifyOTP = (data: IVerifyOTP): Promise<IApiResponseCommonInterface<string>> => {
  return http.post(endpoint.auth.VERIFY_OTP, data);
};

export const resendOTP = (data: IResendOTP): Promise<ApiResponse<null>> => {
  return http.post(endpoint.auth.RESEND_OTP, data);
};

export const forgotPassword = (data: IForgotPassword): Promise<ApiResponse<null>> => {
  return http.post(endpoint.auth.FORGOT_PASSWORD, data);
};

export const resetPassword = (data: IResetPassword): Promise<ApiResponse<null>> => {
  return http.post(endpoint.auth.RESET_PASSWORD, data);
};

export const deleteSession = (userId?: number): Promise<ApiResponse | AxiosResponse> => {
  return http.remove(`${endpoint.auth.DELETE_SESSION}/${userId}`);
};

export const updateTimezone = (data: { timezone: string }): Promise<ApiResponse<null>> => {
  return http.post(endpoint.auth.UPDATE_TIMEZONE, data);
};

export const getUserPermissions = (): Promise<IApiResponseCommonInterface<UserPermissionsResponse>> => {
  return http.get(endpoint.roles.USER_PERMISSIONS);
};
