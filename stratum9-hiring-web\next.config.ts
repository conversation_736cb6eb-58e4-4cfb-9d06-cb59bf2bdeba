import { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const nextConfig: NextConfig = {
  images: {
    domains: [
      "dxxd0n8h8rh9s.cloudfront.net",
      "s9-interview-assets.s3.us-east-1.amazonaws.com",
      "s9-interview-assets-prod.s3.us-east-1.amazonaws.com",
    ],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "dxxd0n8h8rh9s.cloudfront.net",
        pathname: "/profile-images/**",
      },
    ],
  },
};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig);
