/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { Control, Controller } from "react-hook-form";
import TimePicker from "react-time-picker";
import "react-clock/dist/Clock.css";
import "react-time-picker/dist/TimePicker.css";

interface CommonTimePickerProps {
  name: string;
  control: Control<any>;
  label?: string;
  required?: boolean;
  defaultValue?: string;
  disableClock?: boolean;
  className?: string;
  disabled?: boolean;
}

const CommonTimePicker: React.FC<CommonTimePickerProps> = ({ name, control, defaultValue = "00:00", disableClock = true, disabled, className }) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field }) => (
        <TimePicker
          {...field}
          onChange={(value) => field.onChange(value)}
          value={field.value}
          disableClock={disableClock}
          disabled={disabled}
          className={className}
          format="HH:mm" // Ensures 24-hour format
          clockIcon={null} // Optionally remove the clock icon for a cleaner look
        />
      )}
    />
  );
};

export default CommonTimePicker;
