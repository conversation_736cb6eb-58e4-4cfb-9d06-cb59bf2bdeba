# Repository Information

## Project: Stratum 9 Hiring Platform

### Architecture
- **Frontend**: Next.js 15 with TypeScript, React 19
- **Backend**: Node.js/Express API
- **Authentication**: NextAuth.js
- **State Management**: Redux Toolkit with persistence
- **Styling**: SCSS with Bootstrap

### Testing Framework
- **E2E Testing**: <PERSON><PERSON> (configured for this repository)
- **Unit Testing**: Jest with jsdom environment

### Key Application Features
- AI-powered job description generation
- Resume screening and candidate assessment  
- Structured interview processes
- Dashboard with analytics
- Employee management
- Subscription management
- Calendar integration

### Authentication Flow
- Login via email/password using NextAuth.js
- Role-based permissions system
- Multi-tenant organization support
- JWT tokens for API authentication

### Directory Structure
- `/src/app/` - Next.js 13+ app router pages
- `/src/components/` - Reusable React components
- `/src/redux/` - Redux state management
- `/src/services/` - API service layers
- `/tests/e2e/` - Playwright E2E tests (to be created)