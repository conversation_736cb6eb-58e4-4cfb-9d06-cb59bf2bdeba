import { JS<PERSON>, ReactNode } from "react";
import Button from "./Button";

/**
 * Wrapper component for input fields
 * @param {string} className - Class name for the input field
 * @returns {JSX.Element} - Wrapper component
 */
const InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (
  <div className={`form-group ${className ?? ""}`}>{children}</div>
);

/**
 * Label component for input fields
 * @param {string} children - Label text
 * @returns {JSX.Element} - Label component
 */
InputWrapper.Label = function ({
  children,
  htmlFor,
  required,
  className,
  onClick,
  style,
}: {
  children: ReactNode;
  htmlFor?: string;
  required?: boolean;
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
  ref?: React.RefObject<HTMLInputElement>;
}): JSX.Element {
  return (
    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>
      {children}
      {required ? <sup>*</sup> : null}
    </label>
  );
};

/**
 * Error component for input fields to display error message
 * @param { string } message - Error message
 * @param { React.CSSProperties } style - Optional style object
 * @returns { JSX.Element } - Error component
 */
InputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {
  return message ? (
    <p className="auth-msg error" style={style}>
      {message}
    </p>
  ) : null;
};

/**
 * Icon component for input fields
 * @param { string } src - Icon source
 * @param { function } onClick - Function to be called on click
 * @returns { JSX.Element } - Icon component
 */
InputWrapper.Icon = function ({
  children,
  // src,
  onClick,
}: {
  children: ReactNode;
  // src: string;
  onClick?: () => void;
}): JSX.Element {
  return (
    <Button className="show-icon" type="button" onClick={onClick}>
      {children}
    </Button>
  );
};

export default InputWrapper;
