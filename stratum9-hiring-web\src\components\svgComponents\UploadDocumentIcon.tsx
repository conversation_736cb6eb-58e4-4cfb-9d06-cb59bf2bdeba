import React from "react";

type UploadDocumentIconProps = {
  className?: string;
};

function UploadDocumentIcon({ className }: UploadDocumentIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 52 52" fill="none" className={className}>
      <g opacity="0.7" clipPath="url(#clip0_9593_10462)">
        <path d="M32.5 17.332H32.5206" stroke="#333333" strokeWidth="2.6" strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M6.5 13C6.5 11.2761 7.18482 9.62279 8.40381 8.40381C9.62279 7.18482 11.2761 6.5 13 6.5H39C40.7239 6.5 42.3772 7.18482 43.5962 8.40381C44.8152 9.62279 45.5 11.2761 45.5 13V39C45.5 40.7239 44.8152 42.3772 43.5962 43.5962C42.3772 44.8152 40.7239 45.5 39 45.5H13C11.2761 45.5 9.62279 44.8152 8.40381 43.5962C7.18482 42.3772 6.5 40.7239 6.5 39V13Z"
          stroke="#333333"
          strokeWidth="1.95"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.5 34.6673L17.3333 23.8339C19.344 21.8991 21.8227 21.8991 23.8333 23.8339L34.6667 34.6673"
          stroke="#333333"
          strokeWidth="1.95"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M30.3281 30.3326L32.4948 28.166C34.5055 26.2311 36.9841 26.2311 38.9948 28.166L45.4948 34.666"
          stroke="#333333"
          strokeWidth="1.95"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_9593_10462">
          <rect width="52" height="52" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default UploadDocumentIcon;
