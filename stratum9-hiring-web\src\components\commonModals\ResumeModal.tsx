"use client";

import { useEffect, useRef } from "react";

import Button from "../formElements/Button";
// import { useTranslations } from "next-intl";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import "../../styles/eventModal.scss";

interface ResumeModalProps {
  isOpen: boolean; // Controls whether the modal is visible
  onClose: () => void; // Function to close the modal
  resumeLink?: string | null; // Optional link to the resume PDF
}
// 🔁 Static test link
// const resumeLink = "https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf";

const ResumeModal: React.FC<ResumeModalProps> = ({ isOpen, onClose, resumeLink }) => {
  // const t = useTranslations("common");
  // const [fileError, setFileError] = useState<string>("");

  // 🧠 Ref to detect clicks outside the modal
  const modalRef = useRef<HTMLDivElement>(null);

  // 🧩 Detect outside click and close modal
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isOpen, onClose]);

  // ✅ Prevent rendering if modal not open
  if (!isOpen) return null;
  return (
    <div className="modal theme-modal show-modal modal-lg">
      <div className="modal-dialog modal-dialog-centered" ref={modalRef}>
        <div className="modal-content">
          {/* ✅ Header */}
          <div className="modal-header">
            <h4 className="m-0">Resume Preview</h4>
            <Button className="modal-close-btn" onClick={onClose} type="button">
              <ModalCloseIcon />
            </Button>
          </div>

          {/* ✅ Resume preview area */}
          {resumeLink && (
            <div className="modal-body" style={{ height: "80vh" }}>
              <iframe src={resumeLink} className="w-100 h-100" title="Resume Preview" style={{ border: "none" }} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResumeModal;
