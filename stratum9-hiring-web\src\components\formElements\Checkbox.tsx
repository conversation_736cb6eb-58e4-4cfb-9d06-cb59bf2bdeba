import React, { InputHTMLAttributes } from "react";
import { Control, Controller, FieldValues, Path } from "react-hook-form";

interface CheckboxProps<T extends FieldValues> extends Omit<InputHTMLAttributes<HTMLInputElement>, "type"> {
  name: Path<T>;
  control: Control<T>;
  label?: React.ReactNode;
  className?: string;
}

export default function Checkbox<T extends FieldValues>({ name, control, label, className = "", ...props }: CheckboxProps<T>) {
  return (
    <div className={`checkbox-wrapper ${className}`}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <label className="checkbox-label">
            <input type="checkbox" checked={!!field.value} onChange={(e) => field.onChange(e.target.checked)} {...props} aria-label="" />
            {label && <span className="checkbox-text">{label}</span>}
          </label>
        )}
        defaultValue={false as T[typeof name]}
      />
    </div>
  );
}
