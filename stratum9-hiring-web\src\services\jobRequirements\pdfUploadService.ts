import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import { AxiosResponse } from "axios";

/**
 * Process PDF file to extract form fields using GPT
 *
 * This sends the PDF to the backend which:
 * 1. Parses the PDF content
 * 2. Uses GPT to extract structured job information
 * 3. Returns form field data to pre-fill the job form
 *
 * @param file The PDF file to process
 * @param userId The user ID (optional)
 * @param orgId The organization ID (optional)
 * @returns Promise with extracted form fields and jd_link
 */
export const processPdfForFormFields = (file: File): Promise<ApiResponse | AxiosResponse> => {
  const formData = new FormData();
  formData.append("file", file);
  return http.postFile(endpoint.jobRequirements.UPLOAD_URL, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

/**
 * Upload a file to a pre-signed URL
 *
 * This will be used in the final step when saving the job
 *
 * @param url The pre-signed URL to upload to
 * @param file The file to upload
 * @returns Promise that resolves when the upload is complete
 */
export const uploadFileToPredefinedUrl = (url: string, file: File): Promise<Response> => {
  return fetch(url, {
    method: "PUT",
    body: file,
    headers: {
      "Content-Type": file.type,
    },
  });
};
