import * as Sentry from "@sentry/node";
import sendMail from "./sendgrid";

/* eslint-disable no-unused-vars */

export enum SubscriptionEmailType {
  PURCHASE = "purchase",
  CANCEL = "cancel",
  UPGRADE = "upgrade",
  DOWNGRADE = "downgrade",
  RENEW = "renew",
  PAYMENT_FAILED = "payment_failed",
  EXPIRED = "expired",
}

// For backwards compatibility
export const SUBSCRIPTION_EMAIL_TYPE = SubscriptionEmailType;

/**
 * Interface for subscription email parameters
 */
interface SubscriptionEmailParams {
  /** Recipient's email address */
  email: string;
  /** User's name for personalization */
  userName: string;
  /** Current subscription plan name */
  planName: string;
  /** Previous plan name (for upgrade/downgrade emails) */
  oldPlanName?: string;
  /** Type of email notification to send */
  emailType: SubscriptionEmailType | string;
  /** Subscription start date */
  startDate?: Date;
  /** Subscription cancellation date */
  cancellationDate?: Date;
  /** Subscription upgrade date */
  upgradeDate?: Date;
  /** Subscription downgrade date */
  downgradeDate?: Date;
  /** Billing interval (monthly/yearly) */
  interval?: string;
}

/**
 * Interface for email response
 */
interface EmailResponse {
  response?: any;
  error?: string;
}

/**
 * Helper to format dates consistently across the application
 * @param date - Date to format
 * @returns Formatted date string or default text if date is undefined
 */
const formatDate = (date?: Date, defaultText = "N/A"): string => {
  if (!date) return defaultText;

  try {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (error) {
    Sentry.captureException(error);
    return defaultText;
  }
};

/**
 * HTML Email Templates
 */
const EMAIL_TEMPLATES = {
  confirmation: `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Confirmation</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Logo Section -->
            <tr>
                <td align="center" style="padding:20px 0 0; background-color: #fff;">
                    <img src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/stratum-logo.png" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
                </td>
            </tr>
            <!-- Heading Section -->
            <tr>
                <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                    Subscription Plan Purchase Confirmation
                </td>
            </tr>
            <tr>
                <td style="font-size: 24px; font-weight: 500; color: #333;" align="center">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Thank You for Choosing <span style="color: #3182ce; font-weight: 500;">S9 InnerView</span></p>
                </td>
            </tr>
            <!-- Email Content -->
            <tr>
                <td style="padding: 30px;">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear, <span style="color: #3182ce; font-weight: 600;">[Customer Name]</span>,</p>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        Thank you for choosing <strong>S9 InnerView</strong>! We are thrilled to have you onboard. Your purchase of the <strong>[Plan Name]</strong> subscription plan is confirmed. Below are the details of your purchase:
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">Please find your access details below:</p>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                        <tr>
                            <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                                <strong>Subscription Plan: </strong>[Plan Name]<br>
                                <strong>Start Date:</strong> [Start Date]<br>
                                <strong>Billing Cycle:</strong> [Billing Cycle]
                            </td>
                        </tr>
                    </table>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        With your subscription, you now have access to all the benefits. If you have any questions or need assistance, please don't hesitate to reach out to our support team.
                    </p>
                    <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                        We appreciate your trust in S9 InnerView, and we look forward to supporting you on your journey with us.
                    </p>
                    <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.5;">
                        Best regards,<br>
                        S9 InnerView Team,<br>
                        <strong>Stratum 9</strong>
                    </p>
                </td>
            </tr>
            <!-- Footer -->
            <tr>
                <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                    © [CURRENT_YEAR] STRATUM 9. All rights reserved.
                </td>
            </tr>
        </table>
    </body>
    </html>
  `,

  upgrade: `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Upgrade</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Logo Section -->
            <tr>
                <td align="center" style="padding:20px 0 0; background-color: #fff;">
                    <img src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/stratum-logo.png" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
                </td>
            </tr>
            <!-- Heading Section -->
            <tr>
                <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                    Subscription Upgrade Confirmation
                </td>
            </tr>
            <tr>
                <td style="font-size: 24px; font-weight: 500; color: #333;" align="center">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        You've Successfully Upgraded to <span style="color: #3182ce; font-weight: 500;">[Plan Name]</span>
                    </p>
                </td>
            </tr>
            <!-- Email Content -->
            <tr>
                <td style="padding: 30px;">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        Hi <span style="color: #3182ce; font-weight: 600;">[Customer Name]</span>,
                    </p>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        We're excited to inform you that your subscription has been successfully upgraded to the <strong>[Plan Name]</strong> plan. Here are the details of your upgrade:
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">Please find your upgrade details below:</p>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                        <tr>
                            <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                                <strong>Previous Plan:</strong> [Old Plan Name]<br>
                                <strong>New Plan:</strong> [Plan Name]<br>
                                <strong>Upgrade Date:</strong> [Upgrade Date]<br>
                                <strong>Billing Cycle:</strong> [Billing Cycle]
                            </td>
                        </tr>
                    </table>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        With your upgraded plan, you now have access to enhanced features and capabilities. We're confident that these new tools will help you achieve even greater success.
                    </p>
                    <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                        Thank you for choosing to grow with us. If you have any questions about your new features, our support team is here to help.
                    </p>
                    <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.5;">
                        Best regards,<br>
                        S9 InnerView Team,<br>
                        <strong>Stratum 9</strong>
                    </p>
                </td>
            </tr>
            <!-- Footer -->
            <tr>
                <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                    © [CURRENT_YEAR] STRATUM 9. All rights reserved.
                </td>
            </tr>
        </table>
    </body>
    </html>
  `,

  downgrade: `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Downgrade</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Logo Section -->
            <tr>
                <td align="center" style="padding:20px 0 0; background-color: #fff;">
                    <img src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/stratum-logo.png" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
                </td>
            </tr>
            <!-- Heading Section -->
            <tr>
                <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                   Subscription Plan Downgrade Confirmation 
                </td>
            </tr>
            <tr>
                <td style="font-size: 24px; font-weight: 500; color: #333;" align="center">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        Your Subscription Has Been Downgraded to <span style="color: #3182ce; font-weight: 500;">[Plan Name]</span>
                    </p>
                </td>
            </tr>
            <!-- Email Content -->
            <tr>
                <td style="padding: 30px;">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        Hi <span style="color: #3182ce; font-weight: 600;">[Customer Name]</span>,
                    </p>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        We wanted to confirm that your subscription has been successfully downgraded to the <strong>[Plan Name]</strong>.  plan. Here are the details of your downgrade: 
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">Please find your downgrade details below:</p>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                        <tr>
                            <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                                <strong>Previous Plan:</strong> [Old Plan Name]<br>
                                <strong>New Plan:</strong> [Plan Name]<br>
                                <strong>Downgrade Date:</strong> [Downgrade Date]<br>
                                <strong>Billing Cycle:</strong> [Billing Cycle]
                            </td>
                        </tr>
                    </table>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                      With this downgrade, some features may no longer be available. If you have any questions about what's included in your new plan or need further clarification, feel free to contact us.                    </p>
                    <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                     We appreciate your continued support and hope to serve you better with this plan. Should you wish to upgrade again or need additional assistance, please don't hesitate to reach out.                     </p>
                    <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.5;">
                        Best regards,<br>
                        S9 InnerView Team,<br>
                        <strong>Stratum 9</strong>
                    </p>
                </td>
            </tr>
            <!-- Footer -->
            <tr>
                <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                    © [CURRENT_YEAR] STRATUM 9. All rights reserved.
                </td>
            </tr>
        </table>
    </body>
    </html>
  `,

  cancellation: `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Cancellation</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Logo Section -->
            <tr>
                <td align="center" style="padding:20px 0 0; background-color: #fff;">
                    <img src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/stratum-logo.png" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
                </td>
            </tr>
            <!-- Heading Section -->
            <tr>
                <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                    Subscription Cancellation Confirmation
                </td>
            </tr>
            <tr>
                <td style="font-size: 24px; font-weight: 500; color: #333;" align="center">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">We're Sorry to See You Go</p>
                </td>
            </tr>
            <!-- Email Content -->
            <tr>
                <td style="padding: 30px;">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        Hi <span style="color: #3182ce; font-weight: 600;">[Customer Name]</span>,
                    </p>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        We have processed your request to cancel your <strong>[Plan Name]</strong> subscription. Here are the details of your cancellation:
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">Please find your cancellation details below:</p>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                        <tr>
                            <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                                <strong>Cancelled Plan:</strong> [Plan Name]<br>
                                <strong>Cancellation Date:</strong> [Start Date]
                            </td>
                        </tr>
                    </table>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        Your subscription will remain active until the end of your current billing period. After that, your account will be moved to our free plan.
                    </p>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        If you change your mind or this cancellation was made by mistake, you can reactivate your subscription at any time before it expires.
                    </p>
                    <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                        Thank you for being part of the S9 InnerView community. We hope to serve you again in the future.
                    </p>
                    <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.5;">
                        Best regards,<br>
                        S9 InnerView Team,<br>
                        <strong>Stratum 9</strong>
                    </p>
                </td>
            </tr>
            <!-- Footer -->
            <tr>
                <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                    © [CURRENT_YEAR] STRATUM 9. All rights reserved.
                </td>
            </tr>
        </table>
    </body>
    </html>
  `,
};

/**
 * Replaces placeholders in HTML template with actual values
 * @param template - HTML template content
 * @param params - Subscription email parameters
 * @returns Processed HTML content
 */
const processTemplate = (
  template: string,
  params: SubscriptionEmailParams
): string => {
  const {
    userName,
    planName,
    oldPlanName,
    startDate,
    cancellationDate,
    upgradeDate,
    downgradeDate,
    interval,
  } = params;

  let processedTemplate = template;

  // Replace common placeholders
  const replacements: { [key: string]: string } = {
    "[Customer Name]": userName,
    "[Plan Name]": planName,
    "[Old Plan Name]": oldPlanName || "Previous Plan",
    "[Start Date]": formatDate(startDate, "Today"),
    "[Cancellation Date]": formatDate(cancellationDate, "Today"),
    "[Upgrade Date]": formatDate(upgradeDate || startDate, "Today"),
    "[Downgrade Date]": formatDate(downgradeDate || startDate, "Today"),
    "[Billing Cycle]": interval || "Monthly",
    "[CURRENT_YEAR]": new Date().getFullYear().toString(),
  };

  console.log("📧 [DEBUG] Email template replacements:", {
    userName,
    planName,
    oldPlanName,
    startDate: formatDate(startDate, "Today"),
    cancellationDate: formatDate(cancellationDate, "Today"),
    upgradeDate: formatDate(upgradeDate || startDate, "Today"),
    downgradeDate: formatDate(downgradeDate || startDate, "Today"),
    interval: interval || "Monthly",
    currentYear: new Date().getFullYear().toString(),
  });

  // Apply all replacements
  Object.entries(replacements).forEach(([placeholder, value]) => {
    processedTemplate = processedTemplate.replace(
      new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
      value
    );
  });

  return processedTemplate;
};

/**
 * Generates email templates based on subscription event type
 */
const generateEmailContent = (
  params: SubscriptionEmailParams
): { subject: string; htmlContent: string } => {
  const { emailType, oldPlanName } = params;

  let subject = "";
  let htmlContent = "";
  let templateName = "";

  // Map email types to template names and subjects
  switch (emailType) {
    case SubscriptionEmailType.PURCHASE:
      subject = "🎉 Subscription Confirmation – S9 InnerView";
      templateName = "subscription-confirmation";
      break;

    case SubscriptionEmailType.CANCEL:
      subject = "❌ Subscription Cancelled – S9 InnerView";
      templateName = "subscription-cancellation";
      break;

    case SubscriptionEmailType.UPGRADE:
      subject = "🚀 Subscription Upgraded – S9 InnerView";
      templateName = "subscription-upgrade";
      break;

    case SubscriptionEmailType.DOWNGRADE:
      subject = "📉 Subscription Downgraded – S9 InnerView";
      templateName = "subscription-downgrade";
      break;

    case SubscriptionEmailType.RENEW:
      subject = "🔄 Subscription Renewed – S9 InnerView";
      // Use confirmation template for renewal
      templateName = "subscription-confirmation";
      break;

    case SubscriptionEmailType.PAYMENT_FAILED:
      subject = "⚠️ Payment Failed – S9 InnerView Subscription";
      // Use cancellation template for payment failed
      templateName = "subscription-cancellation";
      break;

    case SubscriptionEmailType.EXPIRED:
      subject = "⏰ Subscription Expired – S9 InnerView";
      // Use cancellation template for expired
      templateName = "subscription-cancellation";
      break;

    default:
      subject = "📧 Subscription Update – S9 InnerView";
      templateName = "subscription-confirmation";
      break;
  }

  // Get the appropriate template
  let template: string;

  switch (templateName) {
    case "subscription-confirmation":
      template = EMAIL_TEMPLATES.confirmation;
      break;
    case "subscription-upgrade":
      template = EMAIL_TEMPLATES.upgrade;
      break;
    case "subscription-downgrade":
      template = EMAIL_TEMPLATES.downgrade;
      break;
    case "subscription-cancellation":
      template = EMAIL_TEMPLATES.cancellation;
      break;
    default:
      template = EMAIL_TEMPLATES.confirmation;
      break;
  }

  // Process the template with actual values
  htmlContent = processTemplate(template, params);
  console.log(`✅ Using HTML template: ${templateName}`);

  return { subject, htmlContent };
};

/**
 * Generates fallback HTML content when templates are not available
 * NOTE: This function is currently not used since we're using inline templates
 * Keeping it commented for potential future use
 */
/*
const generateFallbackContent = (
  params: SubscriptionEmailParams
): string => {
  const {
    userName,
    planName,
    oldPlanName,
    emailType,
    startDate,
    cancellationDate,
    upgradeDate,
    downgradeDate,
    interval,
  } = params;

  switch (emailType) {
    case SubscriptionEmailType.PURCHASE:
      return `
        <p>Dear <strong>${userName}</strong>,</p>
        <p>Thank you for purchasing the <strong>${planName}</strong> plan.</p>
        <p><strong>Start Date:</strong> ${formatDate(startDate, "Today")}<br />
        <strong>Billing Cycle:</strong> ${interval || "Monthly"}</p>
        <p>We're excited to have you onboard!</p>`;

    case SubscriptionEmailType.CANCEL:
      return `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>Your subscription for <strong>${planName}</strong> has been cancelled.</p>
        <p><strong>Cancellation Date:</strong> ${formatDate(cancellationDate, "Today")}<br />
        <strong>Next Billing Date:</strong> ${formatDate(startDate, "Today")}</p>
        <p>If this was a mistake, contact us to reactivate your plan.</p>`;

    case SubscriptionEmailType.UPGRADE:
      return `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>Your subscription has been upgraded from <strong>${oldPlanName || "previous plan"}</strong> to <strong>${planName}</strong>.</p>
        <p><strong>Upgrade Date:</strong> ${formatDate(upgradeDate || startDate, "Today")}<br />
        <strong>Billing Cycle:</strong> ${interval || "Monthly"}</p>
        <p>Enjoy your enhanced features!</p>`;

    case SubscriptionEmailType.DOWNGRADE:
      return `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>Your subscription has been downgraded from <strong>${oldPlanName || "previous plan"}</strong> to <strong>${planName}</strong>.</p>
        <p><strong>Downgrade Date:</strong> ${formatDate(downgradeDate || startDate, "Today")}<br />
        <strong>Billing Cycle:</strong> ${interval || "Monthly"}</p>
        <p>Your changes will take effect on your next billing cycle.</p>`;

    default:
      return `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>There has been an update to your <strong>${planName}</strong> subscription.</p>
        <p>If you have any questions, please contact our support team.</p>`;
  }
};
*/

/**
 * Generates plain text version of the email for clients that don't support HTML
 * NOTE: Currently using a simple text version. Can be enhanced if needed.
 */
const generateTextContent = (params: SubscriptionEmailParams): string => {
  const {
    userName,
    planName,
    emailType,
    startDate,
    cancellationDate,
    upgradeDate,
    downgradeDate,
    interval,
  } = params;

  const relevantDate =
    startDate || cancellationDate || upgradeDate || downgradeDate;

  return `
Hello ${userName},

This is your subscription update:

Plan: ${planName}
Status: ${emailType}
Date: ${formatDate(relevantDate, "Today")}
${interval ? `Billing Cycle: ${interval}` : "Billing Cycle: Monthly"}

Thanks,
S9 InnerView Team
Stratum 9
  `;
};

/**
 * Sends subscription-related emails using templates based on event type.
 *
 * @param params - Subscription email parameters
 * @returns Promise with email sending response or error
 */
const sendSubscriptionEmail = async (
  params: SubscriptionEmailParams
): Promise<EmailResponse> => {
  try {
    // Validate required inputs
    if (!params.email || !params.userName || !params.planName) {
      throw new Error(
        "Missing required email parameters: email, userName, and planName are mandatory"
      );
    }

    // Validate email format
    // if (!isValidEmail(params.email)) {
    //   throw new Error(`Invalid email format: ${params.email}`);
    // }

    // Generate email content based on type
    const { subject, htmlContent } = generateEmailContent(params);

    // Generate plain text version
    const textContent = generateTextContent(params);

    // Send the email
    const response = await sendMail({
      email: params.email.toLowerCase().trim(),
      subject,
      textContent,
      htmlContent,
    });

    // Log successful email sending (could use a proper logger here)
    console.log(
      `Subscription email sent successfully to ${params.email} for event: ${params.emailType}`
    );

    return { response };
  } catch (error: any) {
    // Structured error logging
    const errorDetails = {
      recipient: params.email,
      emailType: params.emailType,
      errorMessage: error.message,
      stack: error.stack,
    };

    console.error("Failed to send subscription email:", errorDetails);
    Sentry.captureException(error, {
      contexts: { email_params: { ...params } },
    });

    return { error: `Subscription email sending failed: ${error.message}` };
  }
};
// Export utility function to format dates for external use if needed
sendSubscriptionEmail.formatDate = formatDate;

export default sendSubscriptionEmail;
