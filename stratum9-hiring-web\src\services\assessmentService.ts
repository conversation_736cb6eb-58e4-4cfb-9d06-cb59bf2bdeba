import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse, IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { IAssessmentSubmission, ICreateAssessmentQuestionRequest, IQuestionData, IShareAssessmentRequest } from "@/interfaces/finalAssessment";
import { IAssessmentStatus } from "@/components/views/conductInterview/InterviewSummary";
import { IAssessmentData } from "@/interfaces/candidateFinalAssessment";

/**
 * Interface for Final Assessment request
 */
export interface IFinalAssessmentRequest {
  jobId: number;
  jobApplicationId: number;
}
export interface IFinalAssessmentResponse extends IFinalAssessmentRequest {
  assessmentId: number;
}

/**
 * Get assessment status for a job application
 * @param jobApplicationId ID of the job application
 * @returns Promise with API response containing assessment status
 */
export const getAssessmentStatus = (jobApplicationId: string): Promise<IApiResponseCommonInterface<IAssessmentStatus>> => {
  return http.post(endpoint.assessment.GET_ASSESSMENT_STATUS, { jobApplicationId });
};

/**
 * Create a final assessment for a job application
 * @param data Object containing jobId and jobApplicationId
 * @returns Promise with API response
 */
export const createFinalAssessment = (data: IFinalAssessmentRequest): Promise<IApiResponseCommonInterface<IFinalAssessmentResponse>> => {
  return http.post(endpoint.assessment.CREATE_FINAL_ASSESSMENT, data);
};

/**
 * Get questions for a final assessment
 * @param finalAssessmentId ID of the final assessment
 * @param isShared Boolean indicating if assessment is shared
 * @param isSubmitted Boolean indicating if assessment is submitted
 * @returns Promise with API response containing assessment questions
 */
export const getFinalAssessmentQuestions = (finalAssessmentId: number, jobId: number, jobApplicationId: number): Promise<ApiResponse> => {
  return http.get(endpoint.assessment.GET_FINAL_ASSESSMENT_QUESTIONS, { finalAssessmentId, jobId, jobApplicationId });
};

/**
 * Interface for creating assessment question
 */

/**
 * Create a question for a final assessment
 * @param finalAssessmentId ID of the final assessment
 * @param data Question data
 * @returns Promise with API response
 */
export const createAssessmentQuestion = (data: ICreateAssessmentQuestionRequest): Promise<IApiResponseCommonInterface<IQuestionData>> => {
  const url = endpoint.assessment.CREATE_ASSESSMENT_QUESTION;
  return http.post(url, data);
};

/**
 * Interface for sharing assessment with candidate
 */

/**
 * Share assessment with candidate via email
 * @param data Object containing finalAssessmentId and optional candidateEmail
 * @returns Promise with API response
 */
export const shareAssessmentToCandidate = (data: IShareAssessmentRequest): Promise<ApiResponse> => {
  return http.post(endpoint.assessment.SHARE_ASSESSMENT, data);
};

/**
 * Get assessment data for a candidate using finalAssessmentId
 * @param finalAssessmentId ID of the final assessment
 * @returns Promise with API response containing assessment data
 */
export const getFinalAssessmentByCandidate = (finalAssessmentId: number): Promise<IApiResponseCommonInterface<IAssessmentData>> => {
  return http.get(endpoint.assessment.GET_FINAL_ASSESSMENT_BY_CANDIDATE, { finalAssessmentId });
};

/**
 * Interface for assessment submission data
 */

/**
 * Submit candidate's assessment answers
 * @param data Assessment submission data
 * @returns Promise with API response
 */
export const submitAssessment = (data: IAssessmentSubmission): Promise<ApiResponse> => {
  return http.post(endpoint.assessment.SUBMIT_ASSESSMENT, data);
};

/**
 * Interface for email verification request
 */
export interface IVerifyEmailRequest {
  email: string;
  token: string;
}

/**
 * Response interface for email verification
 */
export interface IVerifyEmailResponse {
  finalAssessmentId: number;
}

/**
 * Verify if candidate email exists in the system
 * @param data Object containing email and token
 * @returns Promise with API response containing finalAssessmentId
 */
export const verifyCandidateEmail = (data: IVerifyEmailRequest): Promise<IApiResponseCommonInterface<IVerifyEmailResponse>> => {
  return http.post(endpoint.assessment.VERIFY_CANDIDATE_EMAIL, data);
};

/**
 * Interface for assessment token request
 */
export interface IAssessmentTokenRequest {
  finalAssessmentId: number;
}

/**
 * Interface for assessment token response
 */
export interface IAssessmentTokenResponse {
  token: string;
}

/**
 * Get assessment token for generating secure assessment URL
 * @param data Object containing finalAssessmentId
 * @returns Promise with API response containing assessment token
 */
export const getAssessmentToken = (data: IAssessmentTokenRequest): Promise<IApiResponseCommonInterface<IAssessmentTokenResponse>> => {
  return http.post(endpoint.assessment.GENERATE_ASSESSMENT_TOKEN, data);
};
