import React, { FC } from "react";

const Instagram: FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" fill="none">
      <rect x="0.666667" y="0.666667" width="62.6667" height="62.6667" rx="31.3333" stroke="white" strokeOpacity="0.22" strokeWidth="1.33333" />
      <g clipPath="url(#clip0_630_2388)">
        <path
          d="M39.9995 16H23.9999C19.6004 16 16.0001 19.6003 16.0001 23.9998V40.0002C16.0001 44.3985 19.6004 48 23.9999 48H39.9995C44.399 48 47.9993 44.3985 47.9993 40.0002V23.9998C47.9993 19.6003 44.399 16 39.9995 16ZM45.3326 40.0002C45.3326 42.9401 42.9411 45.3333 39.9995 45.3333H23.9999C21.0596 45.3333 18.6669 42.9401 18.6669 40.0002V23.9998C18.6669 21.0591 21.0596 18.6667 23.9999 18.6667H39.9995C42.9411 18.6667 45.3326 21.0591 45.3326 23.9998V40.0002Z"
          fill="white"
        />
        <path
          d="M40.6669 25.3358C41.7715 25.3358 42.6669 24.4404 42.6669 23.3359C42.6669 22.2313 41.7715 21.3359 40.6669 21.3359C39.5624 21.3359 38.667 22.2313 38.667 23.3359C38.667 24.4404 39.5624 25.3358 40.6669 25.3358Z"
          fill="white"
        />
        <path
          d="M31.9998 24C27.5807 24 24 27.5811 24 31.9998C24 36.4169 27.5807 40.0004 31.9998 40.0004C36.4177 40.0004 39.9996 36.4169 39.9996 31.9998C39.9996 27.5811 36.4177 24 31.9998 24ZM31.9998 37.3337C29.0546 37.3337 26.6667 34.9457 26.6667 31.9998C26.6667 29.0539 29.0546 26.6667 31.9998 26.6667C34.945 26.6667 37.3329 29.0539 37.3329 31.9998C37.3329 34.9457 34.945 37.3337 31.9998 37.3337Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_630_2388">
          <rect width="32" height="32" fill="white" transform="translate(16 16)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Instagram;
