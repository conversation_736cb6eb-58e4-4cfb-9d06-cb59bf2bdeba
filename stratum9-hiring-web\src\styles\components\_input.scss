@use "../abstracts" as *;

//common input style ----------
.form-group {
  margin-bottom: 20px;
  position: relative;
  label {
    font-size: $text-sm;
    margin-bottom: 5px;
    color: $dark;
    font-weight: $medium;
    sup {
      color: $danger;
      font-size: $text-sm;
      top: -2px;
    }
  }
  .form-control {
    border: 1px solid rgba($white, 0.6);
    padding: 11px 15px;
    font-size: $text-sm;
    border-radius: 12px;
    background: rgba(51, 51, 51, 0.05);
    color: $dark;
    resize: none;
    min-height: 46px;
    &::placeholder {
      color: rgba($dark, 0.4);
      font-weight: $medium;
    }

    &:focus {
      outline: none;
      box-shadow: none;
      color: $dark;
    }
  }
  &.border-danger {
    input,
    textarea,
    select {
      border-color: $danger;
    }
  }
  .auth-msg {
    font-size: $text-xs;
    margin: 5px 0 0;
    font-weight: 400;
    &.error {
      color: $danger;
    }
  }
  .icon-align {
    position: relative;
    input {
      padding-right: 46px;
    }
    &.left {
      input {
        padding-left: 46px;
      }
      img,
      button,
      svg {
        left: 25px;
      }
    }
    &.right {
      img {
        right: 5px;
      }
    }
    .show-icon {
      background-color: transparent;
      border: none;
      padding: 0;
      position: absolute;
      top: 50%;
      right: 0px;
      transform: translate(-50%, -50%);
      img,
      svg {
        width: 20px;
        height: 20px;
        object-fit: contain;
      }
    }
  }
  .css-13cymwt-control,
  .css-t3ipsp-control {
    background: transparent;
    border: none !important;
    outline: none !important;
    border-color: transparent !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
  }

  .select__control {
    box-shadow: none !important;
    border: none;
    background: transparent;
    &:focus {
      box-shadow: none !important;
    }
  }
}
.css-3iigni-container {
  pointer-events: none;
  position: relative;
  box-sizing: border-box;
  padding: 3px 5px !important;
  min-height: 45px;
}

.select--is-disabled,
input:disabled {
  background-color: #e0e0e0 !important;
  color: #555555 !important;
}

.disabled {
  opacity: 0.5;
}

select {
  &:disabled {
    background-color: #e0e0e0 !important;
    color: #555555 !important;
  }
}

.common-search-group {
  .search-view-box {
    position: absolute;
    top: 42px;
    left: 0;
    z-index: 10;
    background-color: $white;
    box-shadow: rgba(0, 0, 0, 0.08) 0px 4px 12px;
    width: 100%;
    border-radius: 0 0 12px 12px;
    display: none;
    ul {
      @extend %listSpacing;
      li {
        padding: 10px;
        cursor: pointer;
        font-size: $text-sm;
        &:hover {
          background-color: $secondary;
          color: $white;
        }
      }
    }
  }
  &:focus,
  &:focus-visible,
  &:focus-within {
    .form-control + .search-view-box {
      display: block !important;
    }
  }
}

select {
  border: 1px solid rgba($white, 0.6);
  padding: 11px 15px;
  font-size: $text-sm;
  border-radius: 12px;
  color: $dark;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: rgba(51, 51, 51, 0.05);
  background-image: url("../../../public/assets/images/down-arrow.svg");
  background-repeat: no-repeat;
  background-position-x: calc(100% - 12px);
  background-position-y: center;
  background-size: 14px 14px;
  padding-right: 32px;
  border-radius: 12px;
  color: rgba($dark, 0.4);
  &:focus {
    outline: none;
    box-shadow: none;
    color: $dark;
  }

  option {
    color: $dark;
    font-style: normal;
  }

  // Style for the first option (placeholder)
  option:first-child {
    color: rgba($dark, 0.4);
  }
}

/* Chrome, Opera */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
  -webkit-text-fill-color: $dark !important;
}

/* Firefox */
input:-moz-autofill,
input:-moz-autofill:hover,
input:-moz-autofill:focus,
input:-moz-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
  color: $dark !important;
}

/* Safari */
input:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s;
  -webkit-text-fill-color: $dark !important;
}

//upload box style ----------
.upload-box {
  border: 1px solid rgba($white, 0.6);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: $white;
  position: relative;
  .upload-para {
    font-weight: 500;
    margin: 10px 0;
  }
  .browse {
    text-align: center;
    color: rgba($white, 0.6);
    margin: 0;
    span {
      color: $secondary;
      font-weight: 600;
      text-decoration: underline;
    }
  }
  input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}

//checkbox style ----------
.checkbox-wrapper {
  margin-bottom: 15px;
  .checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    input[type="checkbox"] {
      width: 14px;
      height: 14px;
    }
  }
  .checkbox-text {
    line-height: 22px;
    margin-top: -5px;
  }
}
//radio button style ----------
.radio-wrapper {
  display: flex;
  line-height: 22px;
  gap: 15px;
  margin-bottom: 15px;
  input[type="radio"] {
    width: 25px;
    height: 25px;
    min-width: 25px;
    min-height: 25px;
    border-color: rgba($primary, 0.6) !important;
    margin: 0;
    &:active {
      box-shadow: none;
    }
  }
  .radio-label {
    cursor: pointer;
    font-size: $text-xl;
    font-weight: $bold;
  }
  .radio-text {
    line-height: 22px;
    margin-top: -5px;
  }
}
.form-check-input[type="radio"] {
  --bs-form-check-bg: transparent;
}
.form-check-input:checked[type="radio"] {
  --bs-form-check-bg-image: url("../../../public/assets/images/check-bg-image.svg");
  background-color: $white;
  border-color: $primary;
  box-shadow: none;
}
//date picker style ----------
.custom-datepicker {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  font-weight: 300 !important;
  svg {
    path {
      fill: $white;
    }
  }
}
.calendar {
  position: absolute;
  top: 110%;
  left: 0;
  z-index: 100;
  background-color: #2b2b2b;
  color: #fff;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.6);
  width: 100%;
  max-width: 300px;

  // Root
  .rdp-root {
    background-color: inherit;
    color: inherit;
  }

  // Nav buttons
  .rdp-nav {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;

    .rdp-button_previous,
    .rdp-button_next {
      background: transparent;
      border: none;
      color: #ddd;
      cursor: pointer;
      padding: 0.25rem;

      &:hover {
        background-color: #3a3a3a;
        border-radius: 4px;
      }

      .rdp-chevron polygon {
        fill: #ddd;
      }
    }
  }

  // Caption
  .rdp-caption_label {
    font-weight: 600;
    font-size: 1rem;
    color: #f0f0f0;
  }

  // Weekdays
  .rdp-weekday {
    color: $grey;
    font-weight: 700;
    font-size: $text-sm;
    text-transform: uppercase;
    padding-bottom: 0.5rem;
    text-align: center;
  }

  // Days
  .rdp-day {
    button {
      background: transparent;
      border: none;
      color: #eee;
      padding: 2px;
      border-radius: 6px;
      width: 25px;
      height: 25px;
      cursor: pointer;
      font-size: $text-sm;
      &:hover {
        background-color: #444;
      }
    }

    &.rdp-today button {
      border: 1px solid $secondary;
    }

    &.rdp-selected button {
      background-color: $secondary;
      color: $dark;
    }

    &.rdp-outside {
      button {
        color: #666;
        opacity: 0.5;
      }
    }

    // &.rdp-hidden {
    //   display: none;
    // }
  }

  // Grid
  .rdp-month_grid {
    width: 100%;
    border-collapse: collapse;
  }

  .rdp-week {
    display: table-row;
  }

  .rdp-day {
    display: table-cell;
    text-align: center;
  }

  .rdp-months {
    position: relative;
    .rdp-month_caption {
      position: absolute;
      top: 4px;
      left: 0;
      right: 0;
      text-align: center;
      width: 80%;
      margin: 0 auto;
      span {
        font-size: $text-sm;
      }
    }
  }
}

//react time picker style ----------
.react-time-picker {
  padding: 10.5px 15px !important;
  .react-time-picker__wrapper {
    border: 0 !important;
    select {
      padding: 0;
      border: 0;
    }
    input,
    select {
      color: $white;
    }
    .react-time-picker__clock-button,
    .react-time-picker__clear-button {
      padding-block: 0;
      svg {
        line {
          stroke: $white;
        }
        circle,
        path {
          stroke: $white;
        }
      }
    }
  }
}

.custom-checkbox {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 12px;
  cursor: pointer;
  text-align: left;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: rgba($dark, 1);
  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 22px;
    width: 22px;
    background-color: #eee;
    border-radius: 4px;
  }
  input:checked ~ .checkmark {
    background-color: rgba($primary, 1);
  }
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  input:checked ~ .checkmark:after {
    display: block;
  }
  .checkmark:after {
    left: 9px;
    top: 5px;
    width: 5px;
    height: 10px;
    border: solid $white;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
}

.forgot-password {
  color: $primary;
  font-weight: $semiBold;
  font-size: $text-sm;
}

.otp-main {
  margin-bottom: 10px;
  div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 5%;
  }
  input {
    text-align: center;
    background-color: #ffffff;
    border: 1px solid #707070;
    border-radius: 10px;
    color: #000000;
    font-size: 14px;
    height: 47px;
    width: 100% !important;
  }
}

//react multi select style
.react-multi-select {
  &.show-active {
    border: 1px solid $primary;
  }
  .select__control {
    background: transparent;
    border: none;
    min-height: 45px;

    &.select__control--menu-is-open,
    &.select__control--is-focused {
      box-shadow: none;
    }

    .select__multi-value {
      color: $dark;
      align-items: center;
      padding: 2px 6px;
      border-radius: 8px;
      gap: 8px;
      background: $secondary;
      .select__multi-value__label {
        color: $dark;
        font-size: $text-xs;
        font-weight: $medium;
      }
      .select__multi-value__remove {
        color: $dark;
        border-radius: 100%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $dark;
        color: $white;

        &:hover {
          background-color: $danger;
          color: $white;
        }
      }
    }
  }
}

//common datepicker style ----------
.common-datepicker-wrapper {
  .react-datepicker-wrapper {
    .react-datepicker__input-container {
      display: flex;
      gap: 15px;
      align-items: center;
      line-height: 1;
      .react-datepicker__calendar-icon {
        position: static;
        width: 18px;
        height: 18px;
        min-width: 18px;
        padding: 0;
        line-height: 1;
      }
      input {
        padding: 0;
        background: transparent;
        border: none;
        outline: none;
      }
    }
  }
  .react-datepicker {
    background: $white;
    width: 100%;
    min-height: 46px !important;
    padding: 0 !important;
    border: 1px solid $primary !important;
    border-radius: 18px !important;
    display: block;
    overflow: hidden;
    font-size: 16px;

    .react-datepicker__day {
      font-size: 16px;
      width: 35px;
      line-height: 2.5rem;
      padding: 5px !important;
      &:disabled {
        opacity: 0.5 !important;
      }
    }
    .react-datepicker__day--selected {
      background: $primary !important;
      color: $white !important;
    }

    .react-datepicker__day--today {
      color: $secondary;
      border-radius: 10px;
    }
    .react-datepicker__day-names,
    .react-datepicker__day {
      div {
        font-size: 16px;
        width: 35px;
        line-height: 2.5rem;
      }
    }
    .react-datepicker__day {
      font-size: 16px;
    }
    .react-datepicker__current-month {
      font-size: 16px;
      margin-bottom: 5px;
    }
  }
  .react-datepicker-popper {
    top: 5px !important;
    left: 0px !important;
    transform: translate(0px, 69px) !important;
  }
}

//react time picker style ----------
.react-time-picker {
  padding: 10.5px 15px !important;
  .react-time-picker__wrapper {
    border: 0 !important;
    select {
      padding: 0;
      border: 0;
    }
    input,
    select {
      color: $dark;
      &:focus {
        outline: none;
        // border-bottom: 1px solid $dark;
      }
    }
    .react-time-picker__clock-button,
    .react-time-picker__clear-button {
      padding-block: 0;
      svg {
        line {
          stroke: $dark;
        }
        circle,
        path {
          stroke: $dark;
        }
      }
    }
  }
}
