@use "./abstracts" as *;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background: white;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.modalHeaderWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
  }
}

.closeButton {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0 5px;
  line-height: 1;
  transition: color 0.2s;

  &:hover {
    color: #333;
  }
}

.modalBody {
  margin-bottom: 20px;

  p {
    margin: 0 0 10px;
    color: #555;
    line-height: 1.5;
  }
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    min-width: 100px;
  }
}

.darkOutlineBtn {
  background-color: transparent;
  border: 1px solid #555;
  color: #333;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }
}

.primaryBtn {
  background-color: #2563eb;
  border: none;
  color: white;
  padding: 10px 16px;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    background-color: #1d4ed8;
  }
}

.rounded-md {
  border-radius: 4px;
}

.formGroup {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }

  input,
  select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }
}

.timeGroup {
  display: flex;
  gap: 16px;

  .formGroup {
    flex: 1;
  }
}

.timeInputContainer {
  display: flex;
  gap: 8px;

  input {
    flex: 3;
  }

  select {
    flex: 1;
  }
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;

  button {
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
  }
}

.saveButton {
  background-color: #4285f4;
  color: white;
  border: none;
}

.cancelButton {
  background-color: transparent;
  border: 1px solid #ddd;
}
