import React from "react";

function CandidateResumeIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="21" viewBox="0 0 25 24" fill="none">
      <path
        d="M14.582 0.963867V0.964844C14.6601 0.963757 14.7375 0.977964 14.8096 1.00781L14.9131 1.06348C14.9452 1.08531 14.9748 1.11067 15.002 1.13867L20.6953 6.83105L20.7695 6.92188C20.8339 7.01865 20.8687 7.13309 20.8691 7.25098V20.8828C20.8691 21.4538 20.642 22.0015 20.2383 22.4053C19.8345 22.809 19.2868 23.0361 18.7158 23.0361H5.2832C4.78372 23.0361 4.302 22.8626 3.91895 22.5488L3.76074 22.4053C3.35708 22.0015 3.12988 21.4537 3.12988 20.8828V3.11719L3.14062 2.9043C3.18955 2.41143 3.40745 1.94802 3.76074 1.59473L3.91895 1.45215C4.30205 1.13825 4.78359 0.963867 5.2832 0.963867H14.582ZM5.28418 2.15332C5.02866 2.15342 4.78323 2.25486 4.60254 2.43555C4.42192 2.6163 4.32031 2.86165 4.32031 3.11719V20.8828C4.32031 21.1383 4.42203 21.3837 4.60254 21.5645L4.67383 21.6289C4.84525 21.7693 5.06065 21.8476 5.28418 21.8477H18.7168L18.8115 21.8428C18.9056 21.8336 18.9982 21.8106 19.0859 21.7744L19.1719 21.7334C19.2551 21.6889 19.3314 21.6323 19.3984 21.5654C19.488 21.476 19.5599 21.3699 19.6084 21.2529L19.6406 21.1631C19.6681 21.0726 19.6816 20.9777 19.6816 20.8828V7.8457H15.8818C15.4426 7.84567 15.0195 7.69293 14.6826 7.41699L14.543 7.29199C14.188 6.93701 13.9893 6.45515 13.9893 5.95312V2.15332H5.28418ZM15.1758 5.95312C15.1758 6.13981 15.2508 6.31916 15.3828 6.45117L15.4893 6.53906C15.6041 6.61572 15.7407 6.65723 15.8809 6.65723H18.8398L15.1758 2.99316V5.95312Z"
        fill="#436EB6"
        stroke="#436EB6"
        strokeWidth="0.15"
      />
      <path
        d="M9.22949 4.86719C9.58595 4.7495 9.96794 4.72524 10.3379 4.79883L10.4941 4.83594C10.8559 4.93613 11.1864 5.12881 11.4531 5.39551L11.5625 5.51367C11.8075 5.79789 11.9762 6.14094 12.0498 6.51074L12.0752 6.66992C12.1145 6.98934 12.0824 7.31354 11.9814 7.61914L11.9258 7.76953C11.7609 8.16767 11.4814 8.50863 11.123 8.74805C10.8095 8.95751 10.4475 9.08071 10.0732 9.1084L9.91211 9.11523C9.40667 9.11455 8.91972 8.93781 8.53223 8.62012L8.37207 8.47559C7.96386 8.06717 7.73411 7.513 7.7334 6.93555L7.73926 6.77441C7.76705 6.40043 7.89127 6.03894 8.10059 5.72559L8.19531 5.59473C8.42628 5.29901 8.73076 5.06714 9.0791 4.92285L9.22949 4.86719ZM9.91211 5.94629C9.64983 5.9468 9.39836 6.05086 9.21289 6.23633C9.0273 6.42191 8.92323 6.67406 8.92285 6.93652C8.92297 7.13211 8.98118 7.3237 9.08984 7.48633L9.17969 7.60059C9.27746 7.70838 9.39767 7.79439 9.5332 7.85059L9.67188 7.89648C9.81322 7.93189 9.96138 7.93585 10.1055 7.90723L10.2461 7.86816C10.3833 7.81905 10.5084 7.73966 10.6123 7.63574C10.7507 7.4973 10.8456 7.32093 10.8838 7.12891L10.9014 6.98438C10.9061 6.88718 10.8967 6.78971 10.873 6.69531L10.8271 6.55664C10.7523 6.37609 10.6253 6.22197 10.4629 6.11328C10.3001 6.00451 10.1079 5.94629 9.91211 5.94629Z"
        fill="#436EB6"
        stroke="#436EB6"
        strokeWidth="0.15"
      />
      <path
        d="M10.5127 9.19043L10.8135 9.20605C11.5087 9.27592 12.1627 9.58361 12.6611 10.082L12.8623 10.3047C13.3053 10.8452 13.5508 11.5245 13.5518 12.2295V12.5469C13.5517 12.6652 13.5169 12.78 13.4521 12.877L13.3779 12.9668C13.2664 13.0783 13.1147 13.1416 12.957 13.1416C12.839 13.1415 12.7247 13.1056 12.6279 13.041L12.5371 12.9668C12.4257 12.8554 12.3633 12.7044 12.3633 12.5469V12.2295L12.3535 12.0469C12.317 11.684 12.1746 11.3396 11.9434 11.0576L11.8203 10.9219C11.5168 10.6184 11.1187 10.4312 10.6953 10.3887L10.5127 10.3799H9.3125C8.88326 10.3806 8.46967 10.53 8.14062 10.7998L8.00488 10.9219C7.65812 11.2686 7.46365 11.7391 7.46289 12.2295V12.5469C7.46285 12.6653 7.42713 12.78 7.3623 12.877L7.28809 12.9668C7.17662 13.0782 7.02577 13.1416 6.86816 13.1416C6.74981 13.1416 6.63509 13.1058 6.53809 13.041L6.44727 12.9668C6.33605 12.8554 6.27349 12.7043 6.27344 12.5469V12.2295L6.28906 11.9297C6.35882 11.2342 6.66648 10.5806 7.16504 10.082L7.3877 9.87988C7.92816 9.43694 8.60761 9.19134 9.3125 9.19043H10.5127Z"
        fill="#436EB6"
        stroke="#436EB6"
        strokeWidth="0.15"
      />
      <path
        d="M12.8838 15.7529L13 15.7646C13.1144 15.7874 13.221 15.8431 13.3047 15.9268L13.3789 16.0176C13.4435 16.1145 13.4785 16.2294 13.4785 16.3477C13.4785 16.4659 13.4436 16.5808 13.3789 16.6777L13.3047 16.7676C13.1932 16.8791 13.0415 16.9424 12.8838 16.9424H6.58008C6.46169 16.9424 6.34702 16.9066 6.25 16.8418L6.15918 16.7676C6.04798 16.6561 5.98536 16.5051 5.98535 16.3477C5.98535 16.19 6.04769 16.0382 6.15918 15.9268L6.25 15.8525C6.34694 15.7879 6.46186 15.7529 6.58008 15.7529H12.8838Z"
        fill="#436EB6"
        stroke="#436EB6"
        strokeWidth="0.15"
      />
      <path
        d="M16.4502 18.4873C16.6076 18.4873 16.7587 18.5499 16.8701 18.6611L16.9443 18.752C17.0091 18.849 17.0449 18.9636 17.0449 19.082C17.0449 19.2004 17.0091 19.3151 16.9443 19.4121L16.8701 19.502C16.7586 19.6134 16.6078 19.6767 16.4502 19.6768H6.58008C6.46169 19.6768 6.34702 19.641 6.25 19.5762L6.15918 19.502C6.04798 19.3905 5.98536 19.2395 5.98535 19.082C5.98535 18.9244 6.04769 18.7726 6.15918 18.6611L6.25 18.5869C6.34694 18.5223 6.46186 18.4873 6.58008 18.4873H16.4502Z"
        fill="#436EB6"
        stroke="#436EB6"
        strokeWidth="0.15"
      />
    </svg>
  );
}

export default CandidateResumeIcon;
